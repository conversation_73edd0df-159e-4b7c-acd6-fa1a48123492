<drawer wx:if="{{visible}}" visible isMaskClose isDisableMove="{{false}}" catch:close="onClose" catch:hide="onClose">
  <view class="wrap">
    <view class="header">
      <view class="title">换电话/微信</view>
      <icon-font type="yp-guanbi" size="48rpx" color="rgba(0, 0, 0, 0.25)" catch:tap="onClose" />
    </view>
    <view class="body">
      <view class="item" catch:tap="onExChangeTelClick">
        <image wx:if="{{conversation.rightsStatusInfo.exchangeTel.status == 1 || conversation.rightsStatusInfo.exchangeTel.status == 3}}" class="img" src="https://cdn.yupaowang.com/yupao_common/82543fa3.png" />
        <image wx:else class="img" src="https://cdn.yupaowang.com/yupao_common/8ba6c44b.png" />
        <view class="txt {{conversation.rightsStatusInfo.exchangeTel.status == 1 || conversation.rightsStatusInfo.exchangeTel.status == 3?'':'txt-no'}}">
          <block wx:if="{{conversation.rightsStatusInfo.exchangeTel.status == 2}}">请求中</block>
          <block  wx:elif="{{conversation.rightsStatusInfo.exchangeTel.status == 3}}">查看电话</block>
          <block wx:else>请求交换电话</block>
        </view>
      </view>
      <view class="item" catch:tap="onExChangeWechatClick">
        <image wx:if="{{conversation.rightsStatusInfo.exchangeWechat.status == 1 || conversation.rightsStatusInfo.exchangeWechat.status == 3}}" class="img" src="https://cdn.yupaowang.com/yupao_common/86be044e.png" />
        <image wx:else class="img" src="https://cdn.yupaowang.com/yupao_common/288e6aff.png" />
        <view class="txt {{conversation.rightsStatusInfo.exchangeWechat.status == 1 || conversation.rightsStatusInfo.exchangeWechat.status == 3?'':'txt-no'}}">
          <block wx:if="{{conversation.rightsStatusInfo.exchangeWechat.status == 2}}">请求中</block>
          <block  wx:elif="{{conversation.rightsStatusInfo.exchangeWechat.status == 3}}">查看微信</block>
          <block wx:else>请求交换微信</block>
        </view>
      </view>
    </view>
    <m-stripes />
  </view>
</drawer>
<!-- 换电话 -->
<exchange-tel wx:if="{{isExChangeTelShow}}" visible="{{isExChangeTelShow}}" crExchangeType="{{crExchangeType}}" bind:close="onExChangeTelClose" bind:crcallback="onCrCallback"/>
<!-- 换微信 -->
<exchange-wechat wx:if="{{isExChangeWechatShow}}" visible="{{isExChangeWechatShow}}" bind:updatewechat="onUpdateWechatShow" bind:close="onExChangeWechatClose"/>
<!-- 修改添加微信号 -->
<update-wechat wx:if="{{isUpdateWechatShow}}" visible="{{isUpdateWechatShow}}" bind:close="onUpdateWechatClose" />
