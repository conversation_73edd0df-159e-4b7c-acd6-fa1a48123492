/*
 * @Date: 2022-02-07 09:44:03
 * @Description: 公共方法
 */

import { messageQueue, storage } from '@/store/index'
import { getShareInfoByType, getShareInfo, getShareTimeLineInfo, getShareInfoByTypeV2 } from '@/utils/helper/share/index'
import { SHARE_CHANNEL } from '@/config/share'
import { IAddressParams, IMapParams, OpenAddress, OpenLocation, OpenMap } from './index.d'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { registerLoginCallback, toLogin, unLoginCallback } from '@/utils/helper/common/toLogin'

export function init(options: any = {}) {
  // ! 扩展onLoad 生命周期
  const oldOnLoad = options.onLoad
  options.onLoad = function (opt) {
    const isShowPopup = storage.getItemSync('isShowPopup')
    if (isShowPopup) {
      storage.setItemSync('isShowPopup', '')
    }
    if (opt) {
      if (opt.scene) {
        const scene = decodeURIComponent(opt.scene)
        scene.split('&').forEach((item) => {
          const [key, value] = item.split('=')
          opt[key] = value
        })
      }
      if (opt.q) {
        const q = decodeURIComponent(opt.q)
        q.split('&').forEach((item) => {
          const [key, value] = item.split('=')
          opt[key] = value
        })
      }
      const urlepc = wx.$.u.getUrlAllParams(decodeURIComponent(opt.url)).epc || ''
      const pages = wx.$.r.getCurrentPage()
      let currPagePath = pages && pages.route
      if (!currPagePath) {
        const launchOptions = wx.getLaunchOptionsSync()
        currPagePath = launchOptions && launchOptions.path
      }
      const pageCode = getPageCode()
      const url = pageCode || decodeURIComponent(opt.url) || currPagePath || ''
      if (opt.epc || urlepc || opt.myepc) {
        wx.$.collectEvent.event('universal_exposure', {
          bis: 'ypzp',
          s_t: opt.s_t || '-99999',
          p_c: opt.epc || urlepc || opt.myepc || '-99999',
          url,
          code: opt.r_s_code || opt.tid || '-99999',
        })
      }
      messageQueue((state) => !state.user.silentLoginIng).finally(() => {
        const { login } = storage.getItemSync('userState')
        const isShowServicePrivacyV5 = storage.getItemSync('isShowServicePrivacyV5')
        if (!isShowServicePrivacyV5 && !login && opt.isLogin && !['subpackage/userauth/auth/index', 'subpackage/web-view/index', 'subpackage/recruit/listSearchResultsPage/index'].includes(currPagePath)) {
          toLogin(true)
        }
      })
    }

    const showRealTel = storage.getItemSync('showRealTel')
    if (showRealTel) {
      storage.setItemSync('showRealTel', '')
      storage.setItemSync('showRealTelState', null)
    }

    if (options.onShow) {
      registerLoginCallback(options.onShow, this)

    }

    // 开发环境调试setData使用次数
    // if (ENV_MODE === 'dev') {
    //   const { setData } = this
    //   this.setData = function (value, callback) {
    //     const beginTime = new Date().getTime()
    //     setData && setData.call(this, value, () => {
    //       callback && callback()
    //     })
    //   }
    // }
    if (ENV_IS_WEAPP) {
      // * 若有隐藏需求判断路由进行隐藏分享
      const blackShareList = [
        'pagesfactory/index/index', // 工厂招工
        'pagesfactory/resume/list/index', // 工厂找活
        'subpackage/member/integral/list/index', // 积分记录
        'subpackage/member/myContactHistory/index', // 联系记录
        'subpackage/member/b_collect/index', // 收藏牛人
        'subpackage/member/c_collect/index', // 收藏职位/老板
        'subpackage/member/evaluation/index', // 评价列表
        'subpackage/recruit/my-wait-detail/index', // 待开放详情
      ]
      const currentPage = wx.$.r.getCurrentPage().route
      if (blackShareList.includes(currentPage)) {
        wx.hideShareMenu({
          menus: ['shareAppMessage', 'shareTimeline'],
        })
      }
    }
    // 原有页面onload正常执行
    oldOnLoad && oldOnLoad.call(this, opt)
  }

  // ! 扩展onShareAppMessage 生命周期
  const originalOnShareAppMessage = options.onShareAppMessage
  options.onShareAppMessage = function (options) {
    let shareOptions: any = {}
    const pages = wx.$.r.getCurrentPage()
    // 如果页面有分享就调用页面的
    if (originalOnShareAppMessage) {
      shareOptions = originalOnShareAppMessage.call(this, options)
    } else if (pages.data.screenshotShareData && !!pages.data.screenshotShareData.sharePage && options.from != 'button') {// 截图分享
      shareOptions = getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, ext: {}, from: options.from })
    } else {
      // 没有就用公有的分享方法
      const shareInfo = getShareInfoByType(SHARE_CHANNEL.SHARE_WECHAT_FRIEND)
      shareOptions = getShareInfo({ ...shareInfo, from: options.from })
    }

    return shareOptions
  }

  // ! 扩展onShareTimeline 生命周期
  const oldOnShareTimeline = options.onShareTimeline
  options.onShareTimeline = function (options) {
    // 如果页面有分享就调用页面的
    let shareOptions: any = {}
    if (oldOnShareTimeline) {
      shareOptions = oldOnShareTimeline.call(this, options)
    } else {
      const shareInfo = getShareInfoByType(SHARE_CHANNEL.SHARE_WECHAT_MOMENTS)
      shareOptions = getShareTimeLineInfo({ query: `track_seed=${shareInfo.track_seed}&source=${shareInfo.source}&refid=${shareInfo.refid}` })
    }
    return shareOptions
  }

  // ! 扩展onUnload 生命周期
  const oldOnUnload = options.onUnload
  options.onUnload = function () {
    options.onShow && unLoginCallback()
    oldOnUnload && oldOnUnload.call(this)
  }

  return options
}

/** 跳转到获取积分页面 区分ios环境 ios不能充值，只能去获取积分页面 */
export function toGetIntegral(query = {}, params = {}) {
  const routes = wx.$.r.getCurrentPage()
  const isIos = wx.getSystemInfoSync().platform.toLowerCase() === 'ios'
  if (isIos) {
    if (routes.route === "subpackage/member/getintegral/index") {
      return
    }
    wx.$.r.push({ path: '/subpackage/member/getintegral/index' })
  } else {
    if (routes.route === "subpackage/recharge/recharge/index") {
      return
    }
    wx.$.r.push({
      path: '/subpackage/recharge/recharge/index',
      query,
      params,
    })
  }
}

/** 设置百度seo TDK */
export function setTDK({ title, description, keywords }) {
  if (ENV_IS_SWAN) {
    wx.setPageInfo({
      title,
      description,
      keywords,
    })
  }
}

/** 打开地图map选择页 */
export async function openLocation(query: any, type = '') {
  if (type === 'recruit') {
    wx.$.r.push({
      path: '/subpackage/map/location/index',
      query,
    })
    return
  }
  wx.$.r.push({
    path: '/subpackage/map/history/index',
    query,
  })
}

/** 打开地区选择页 */
export const openAddress: OpenAddress = function (options, cb) {

  const params = {
    type: 'resume',
    title: '选择城市',
    maxNum: 1,
    level: 3,
    areas: [],
    disabledIds: [],
    hideNation: true,
    selectType: '',
    headType: '',
    hasAllType: 'all',
    forceCityOnly: false,
    publishCity: undefined,
    point: { source_id: '' },
    ...options,
  } as IAddressParams
  wx.$.nav.push(
    '/subpackage/map/address/index',
    {},
    (data) => cb && cb(data),
    {
      ...params,
    },
  )
}

/** 打开地图选择页 */
export const openMap: OpenMap = function (options, cb) {
  const params = {
    disabledIds: [],
    title: '选择地址',
    showManage: false,
    ...options,
  } as IMapParams
  wx.$.nav.push(
    '/subpackage/map/addr-info/index',
    {},
    (data) => cb && cb(data),
    {
      ...params,
    },
  )
}

/** toast提示 */
export function msg(title: string | any, duration = 2500, mask = false) {
  return new Promise((resolve) => {
    if (title !== undefined) {
      wx.showToast({ title, duration, mask, icon: 'none' })
    }
    setTimeout(resolve, duration)
  })
}

/** loading提示 */
export function showLoading(title: string | any = '加载中...', mask = true) {
  return new Promise((resolve) => {
    wx.showLoading({
      title,
      mask,
      success(res) {
        resolve({ error: false, res })
      },
      fail(res) {
        resolve({ error: true, res })
      },
    })
  })
}

/** 选择组件 */
export function selectComponent(selector: string): Promise<any> {
  return new Promise((resolve, reject) => {
    // 百度和微信同步的
    if (ENV_IS_WEAPP || ENV_IS_SWAN) {
      const component = this.selectComponent(selector)
      component ? resolve(component) : reject()
    }
  })
}

/**
 * 页面销毁后，清除定时器副作用
 */

export function clearTimeoutEffect() {

}
