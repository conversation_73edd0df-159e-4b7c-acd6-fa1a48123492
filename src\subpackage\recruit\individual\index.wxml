<custom-header title="{{scrollTop/30 > 1 ? info.userName:' '}}" fixed="{{false}}" customStyle="background:rgba(255, 255, 255, {{scrollTop/140}});color:{{scrollTop/30 > 1 ? '#000':'#fff'}};position: fixed;z-index:10;top:0;"></custom-header>
<view class="bgImg">
    <image class="img" src="https://cdn.yupaowang.com/yp_mini/images/gyf/individual-bg-img-{{num}}.png"></image>
    <view class="mask"></view>
</view>
<view class="body">
    <view class="card base-info">
        <block wx:if="{{login && !info.isMine && !info.isX}}">
            <view class="btn1" wx:if="{{!focusStatus}}" catch:tap="onAttention">
                <icon-font type="yp-tianjia1" size="28rpx" color="rgb(0, 146, 255)" />
                <text>收藏</text>
            </view>
            <view class="btn2" wx:elif="{{focusStatus}}" catch:tap="onAttention">
                <icon-font custom-class="yes" type="yp-cls_g" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
                <text>已收藏</text>
            </view>
        </block>
        <view class="avatarBox">
            <image mode="aspectFill" class="img" src="{{info.avatarUrl}}"></image>
        </view>
        <view class="item">
            <view class="name">{{info.userName}}</view>
            <view class="localtion">IP归属地：{{ipAddress || info.ipTerritory || '未知'}}</view>
        </view>
        <view wx:if="{{info.checkDegreeStatus == 2}}" class="companyName">
            {{info.companyName}}
        </view>
    </view>
    <view wx:if="{{ companyCardInfo.name && info.checkDegreeStatus === 2}}" class="card">
        <view class="title" style="margin-bottom: 24rpx;">目前任职</view>
        <!-- 企业卡片 -->
        <company-card wx:if="{{companyCardInfo.name}}" source_page="个人主页" jobId="{{info.jobId}}" info="{{companyCardInfo}}" />
        <view class="content" bind:tap="onCardClick">
            <view class="contentText">
                <view class="contentIcon">
                    <image class="img" src="https://cdn.yupaowang.com/yp_mini/images/gyf/gwb-Icon.png"></image>
                </view>
                <view>查看</view>
                <view class="blueText">{{companyCardInfo.name}}</view>
                <view>在招职位</view>
            </view>
            <view>
                <icon-font type="yp-resume_pub_nod" size="40rpx" color="#00000073"></icon-font>
            </view>
        </view>
    </view>
    <view id="titleR" class="card title {{isTitleTop==1?'':'titleT'}}" style="top:{{top + 36}}px">
        发布职位<text> · {{jobCount || 0}}</text>
    </view>
    <view wx:if="{{list.length}}" class="list">
        <view wx:for="{{list}}" class="recruit-item" wx:key="index" data-index="{{index}}" data-item="{{item}}">
            <recruit-card-v4 item="{{item}}" isFreeFirst isShowLookend="{{false}}" isShowTop data-index="{{index}}" fromOrigin="recruitListIndex" isBeforeClick bind:beforeClick="onClickDetail" bind:clickTopBtn="onClickTopBtn" bind:callphonebtn="onCallPhoneBtn" listSource="{{listSource}}" isNeedJump="{{1}}" joblisttype="{{recruitSort.value}}" pageShowAuth="{{pageShowAuth}}" isFromMainRecruitList="{{isFromMainRecruitList}}" isCallBtn="{{isCallBtn}}" isFromOccVList="{{isFromOccVList}}" isShowContactBtn="{{isShowContactBtn}}" isShowFreeContactBtn="{{isShowFreeContactBtn}}" />
        </view>
    </view>
</view>