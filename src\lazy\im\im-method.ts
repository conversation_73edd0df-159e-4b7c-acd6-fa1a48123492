import { actions, dispatch, storage, store } from '@/store/index'
import { getTimObj } from './im-init'
import { CallBackObj } from './type.d'
import { toLogin } from '@/utils/helper/common/toLogin'
import { tryPromise } from '@/utils/tools/common/index'
import dayjs from '@/lib/dayjs/index'
import { initTabPosition } from '@/pages/resume/components/position-tab/utils'
import { toJSON } from '@/utils/tools/formatter/index'

/** 鱼泡用户登录后 im开始登录 */
export const timLogin = async (callback?) => {
  if (!ENV_IS_WEAPP) return
  wx.$.collectEvent.event('imLogin_zdy', { type: 3 })
  // /** 获取用户的 userSig*/
  const { data, code } = await wx.$.javafetch['POST/reach/v2/im/account/getImToken']()
  if (code == 0) {
    dispatch(actions.storageActions.setItem({ key: 'userImAcc', value: data.imAccount }))
    // 开始im登录
    if (wx.$.u.isEmptyObject(wx.$.tim)) {
      await getTimObj()
      wx.$.tim.login({ userID: `${data.imAccount}`, userSig: data.imToken }).then((imResponse) => {
        if (imResponse.data.repeatLogin === true) {
          // 标识账号已登录，本次登录操作为重复登录。
          // console.log(imResponse.data.errorInfo)
        } else {
          timLoginAfterHandle()
          callback && callback()
        }
      })
    } else {
      wx.$.tim.login({ userID: `${data.imAccount}`, userSig: data.imToken }).then((imResponse) => {
        if (imResponse.data.repeatLogin === true) {
          // 标识账号已登录，本次登录操作为重复登录。
          // console.log(imResponse.data.errorInfo)
        } else {
          timLoginAfterHandle()
          callback && callback()
        }
      })
    }
  }
}

/** 鱼泡用户已登录情况，im重新登录 */
// eslint-disable-next-line consistent-return
export const reTimLogin = async (callback?) => {
  if (!ENV_IS_WEAPP) return {}
  const { reLoginState } = store.getState().storage.common
  const isReady = wx.$.tim && wx.$.tim.isReady()
  if (reLoginState && isReady) return {}
  await dispatch(actions.messageActions.setState({ imlogin: false, isSyncCompleted: false }))
  dispatch(actions.storageActions.setCommonItem({ reLoginState: true }))
  setTimeout(() => {
    dispatch(actions.storageActions.setCommonItem({ reLoginState: false }))
  }, 2000)
  const user = storage.getItemSync('userState')
  wx.$.collectEvent.event('imLogin_zdy', { type: 1 })
  if (!!user && !!user.userId) {
    wx.$.collectEvent.event('imLogin_zdy', { type: 2 })
    if (isReady) {
      wx.$.tim && await wx.$.tim.logout()
    }
    await timLogin(callback)
  }
}

/** 重新请求im数据 */
export const reGetImData = () => {
  if (!ENV_IS_WEAPP) return
  const currentPage = wx.$.r.getCurrentPage()
  if (currentPage.route.indexOf('pages/msg-page/index') >= 0) {
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 1]
    if (!prevPage.data?.reGetImDataNum) {
      prevPage.getImData(true)
      prevPage.setData({ reGetImDataNum: 1 })
    }
  }
}

/** 退出后重登 */
export const timLogoutAndReLogin = () => {
  if (!ENV_IS_WEAPP) return
  const isReady = wx.$.tim && wx.$.tim.isReady()
  if (isReady) {
    wx.$.tim && wx.$.tim.logout().then(() => {
      timLogin()
    }).catch(() => {
      timLogin()
    })
  } else {
    timLogin()
  }
}

/** 退出登录 */
export const timLogout = () => {
  const isReady = wx.$.tim && wx.$.tim.isReady()
  if (isReady) {
    wx.$.tim.logout()
  }
}

/** IM登录后需要执行的方法 */
const timLoginAfterHandle = () => {
  if (!ENV_IS_WEAPP) return
  setTimeout(() => {
    dispatch(actions.messageActions.fetchImMessageNumber())
  }, 500)
}

/**
 * 发起聊一聊 判断会话是否存在，不存在创建会话，存在就返回会话基本信息
 * @param id 信息id(招工找活id)（注意：找活传子名片id）
 * @param infoType 信息类型（1：招工；2：找活）
 * */
export const initGroup = async (infoId, infoType: 1 | 2 = 1, ext = {}) => {
  if (!ENV_IS_WEAPP) return {}
  const user = storage.getItemSync('userState')
  if (!user.login) {
    toLogin(true)
    dispatch(actions.timmsgActions.setState({ resumesInfo: {}, recruitInfo: {} }))
    return null
  }
  wx.showLoading({ title: '加载中...' })
  const { relatedInfoId, fromType, postType, resumeAttachUuid, isNoPush } = ext || {} as any
  const params: any = { infoType, infoId }
  if (fromType) {
    params.fromType = fromType
  }
  if (postType) {
    params.postType = postType
  }
  if (resumeAttachUuid) {
    params.resumeAttachUuid = resumeAttachUuid
  }
  if (Number(relatedInfoId) && infoType === 2) {
    params.relatedInfoId = relatedInfoId
  }
  const res = await wx.$.javafetch['POST/reach/v2/im/chat/init'](params)
  if (!res) {
    wx.hideLoading()
    wx.$.msg('服务忙,请稍后重试')
    dispatch(actions.timmsgActions.setState({ resumesInfo: {}, recruitInfo: {} }))
    return null
  }
  const { code, message, data } = res || {}
  if (code != 0) {
    wx.hideLoading()
    wx.$.msg(message)
    const ky = infoType == 1 ? 'recruitInfo' : 'resumesInfo'
    dispatch(actions.timmsgActions.setState({ [ky]: {} }))
    return null
  }
  dispatch(actions.messageActions.setState({ isCreateSession: true }))
  setTimeout(() => {
    dispatch(actions.messageActions.setState({ isCreateSession: false }))
  }, 5000)
  const { conversationId, toUserImId } = data || {}
  wx.hideLoading()
  if (!isNoPush) {
    wx.$.r.push({
      path: '/subpackage/tim/groupConversation/index',
      query: {
        conversationId,
        toUserImId,
      },
    })
  }
  return data
}

/**
 * 发送简历附件的 email 给 老板的邮箱(海投网)
 * @param jobId 信息id(招工找活id)
 * @param resumeAttachUuid 文件唯一ID
 */
export const sendResumeEmail = async (jobId, resumeAttachUuid) => {
  return await wx.$.javafetch['POST/reach/v2/im/chat/sendResumeEmail']({ jobId, resumeAttachUuid }).then((res) => {
    if (res.code == 0) {
      wx.$.msg('投递成功')
    }
    return res
  })
}

/** 重发消息 */
export const resendMessage = async (message, callback?: CallBackObj) => {
  const promise = wx.$.tim.resendMessage(message) // 传入需要重发的消息实例
  const { pre, success, fail } = callback || {}
  pre && await pre(message)
  promise.then((imResponse) => {
    // console.log('imResponse:', imResponse)
    const { data } = imResponse || {}
    const { message: nMsg } = data || {}
    success && success(nMsg)
  }).catch((imError) => {
    // console.log('imError:', imError)
    // if (imError.code == 80001) {
    //   wx.$.msg('该内容包含敏感词,暂不支持发送')
    // } else {
    //   wx.$.msg('网络忙，请稍后重试或检查网络状态')
    // }
    fail && fail(message, imError)
  })
}

/** 发送整个会话已读回执 */
export const sendConverReaded = (to) => {
  return wx.$.tim && wx.$.tim.setMessageRead({ conversationID: to })
}

/** 已读回执 */
export const msgReadReceipt = (msgList: Array<any>) => {
  return wx.$.tim && wx.$.tim.sendMessageReadReceipt(msgList)
}

/**
   * 判断是否显示聊一聊按钮
   * @param user_id 用户ID
   * @param occIds 工种id数组
   * @param infoType 信息类型（1：招工；2：找活）
  */
export const isShowChat = async (user_id: string, ext = { occIds: [], infoType: 1 }) => {
  if (!ENV_IS_WEAPP) {
    return false
  }
  const user = storage.getItemSync('userState')
  if (!user.login) return true
  if (!Number(user_id)) return false
  const routers = getCurrentPages()
  if (routers) {
    const router = routers.find((item) => item.route.indexOf('subpackage/tim/groupConversation/index') >= 0 && item.__hasReportHide__)
    if (router) return false
  }
  const params: any = { userId: user_id }
  const { occIds, infoType } = ext || {}
  if (occIds) {
    params.occIds = occIds
  }
  if (infoType) {
    params.infoType = infoType
  }

  const res = await wx.$.javafetch['POST/reach/v2/im/account/checkIsShowChatEntry'](params)
  const { code, data } = res || {}
  const { isShowChatEntry } = data || {}
  return code == 0 && isShowChatEntry
}

/** 删除单一会话, 并清空会话历史消息 */
export const deleteConversation = (conversationID, clearHistoryMessage = true) => {
  return wx.$.tim.deleteConversation({ conversationIDList: [conversationID], clearHistoryMessage })
}

/** 黑名单 */
export const addToBlacklist = (userIDList = []) => {
  return wx.$.tim.addToBlacklist({ userIDList })
}
/** 解除黑名单 */
export const removeFromBlacklist = (userIDList = []) => {
  return wx.$.tim.removeFromBlacklist({ userIDList })
}

/** 离线消息-查询当前用户头像 */
export const getOfflineMessageAvatar = async () => {
  const offlineMessageAvatar = storage.getItemSync('offlineMessageAvatar')
  const { headImg } = offlineMessageAvatar || {}
  if (headImg) {
    return offlineMessageAvatar
  }

  const res = await tryPromise(wx.$.javafetch['POST/reach/v2/im/user/currentUserImg']({}), {})
  const { code, data } = res || {}
  if (code == 0) {
    const { expireTime } = data || {}
    let outTime = -1
    if (expireTime) {
      outTime = dayjs().add(Number(expireTime), 'second').valueOf()
    }
    storage.setItemSync('offlineMessageAvatar', data, { outTime })
    return data
  }
  return null
}

// 系统透传消息transmitType=2，type=999999处理
export const handleTransmitType = (msg) => {
  const { payload } = msg || {}
  const { data } = payload || {}
  const dataJson = toJSON(data)
  const { content } = dataJson || {}
  const { applicableRole, content: tContent, eventType } = content || {}
  const contentJson = toJSON(tContent)
  // B端更新职位信息的状态
  if (eventType == 'JOB_STATUS_CHANGE') {
    console.log('im触发tab更新')
    initTabPosition(false, true)
  }
}
