/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 换电话
 */

import { actions, dispatch, storage, store } from '@/store/index'
import { toResumeDetail } from '../../utils'
import { guid } from '@/utils/tools/common/index'

Component(class extends wx.$.Component {
  useStore(state: StoreRootState) {
    const { timmsg, user } = state
    return {
      // 单聊会话基本信息
      conversation: timmsg.conversation,
      userInfo: user.userInfo,
    }
  }

  properties = {
    msgInfo: { type: Object, value: {} },
  }

  observers = {
    conversation(v) {
      if (v) {
        // eslint-disable-next-line sonarjs/no-gratuitous-expressions
        const { toUserAvatar } = v || {}
        this.setData({ toUserAvatar })
      }
    },
    userInfo(v) {
      if (v) {
        const fromUserAvatar = wx.$.u.getObjVal(v, 'userBaseObj.userHeadPortraitObj.headPortrait')
        this.setData({ fromUserAvatar })
      }
    },
  }

  data = {
    defalutImg: 'https://static-test-public.cdqlkj.cn/r/9c8b/108/pb/p/20231127/5719ddfe3429409187708e7165a1a99d.png',
    // 用户手机号
    tel: '',
    fromUserAvatar: '',
    toUserAvatar: '',
  }

  onError(e) {
    const { type } = e.currentTarget.dataset
    // eslint-disable-next-line sonarjs/no-all-duplicated-branches
    if (type == 'isself') {
      this.setData({ fromUserAvatar: '' })
    } else {
      this.setData({ fromUserAvatar: '' })
    }
  }

  // 点击头像，跳转用户信息页面
  onAvaterClick() {
    const { msgInfo } = this.data as DataTypes<typeof this>
    const { isSelf } = msgInfo || {} as any
    if (isSelf) {
      return
    }
    const role = storage.getItemSync('userChooseRole')
    const { conversation } = store.getState().timmsg
    const { conversationId, infoDetail, telRightsInfo, toUserId } = conversation || {}
    const { relatedInfoType, cardInfo, relatedInfoId } = infoDetail || {}
    const { isCheck } = cardInfo || {}
    const { telType, infoId: telInfoIdl, infoType: telInfoType } = telRightsInfo || {}
    if (isCheck && Number(relatedInfoId) && relatedInfoType == 2 && role == 1) {
      const buryingPoint = {
        info: {
          request_id: guid(),
          source_id: '20',
        },
      }
      toResumeDetail(relatedInfoId, false, { buryingPoint: JSON.stringify(buryingPoint) })
    } else if (isCheck && Number(relatedInfoId) && relatedInfoType == 1 && role == 2) {
      // 详情页埋点使用字段
      const buryingPoint = {
        id: relatedInfoId,
        info: { source: 'IM头像', source_id: '19' },
      }
      dispatch(actions.recruitDetailActions.setState({ buryingPoint }))
      wx.$.r.push({
        path: '/subpackage/recruit/details/index',
        query: {
          id: relatedInfoId,
          type: 'groupConversation',
          telType,
          infoId: telInfoIdl,
          infoType: telInfoType,
          source: 'IM头像',
          source_id: '19',
          toUserId,
        },
      })
    } else {
      wx.$.r.push({
        path: '/subpackage/tim/otherInfo/index',
        query: {
          conversationId,
        },
      })
    }
  }
})
