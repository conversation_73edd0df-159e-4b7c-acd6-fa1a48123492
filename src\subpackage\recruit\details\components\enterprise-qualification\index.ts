/*
 * @Date: 2023-11-29 15:21:47
 * @Description: 企业资质
 */

Component(class extends wx.$.Component {
  // externalClasses = []

  // options = {}

  properties = {
    zIndex: {
      type: Number,
      value: 9999,
    },
    jobId: {
      type: String,
      value: '',
    }, // 详情用户id
  }

  // pageLifetimes = {}

  // observers: {}

  options = {
  }

  pageLifetimes = {
  }

  lifetimes = {
    ready() {
      this.getQualification()
    },
  }

  data = {
    qualificationInfoList: [], // 资质信息
  }

  //  获取资质详情
  async getQualification() {
    const { jobId } = this.data as DataTypes<typeof this>
    if (!jobId) return
    const res = await wx.$.javafetch['POST/enterprise/v1/qualification/infoByJobId']({ jobId })
    const { data, code } = res || {}
    if (code == 0) {
      const { list } = data || {}
      this.setData({
        qualificationInfoList: list,
      })
    }
  }

  closeModal() {
    this.setData({
      openVisible: false,
    })
  }

  openModal() {
    this.setData({ openVisible: true })
  }

  onRefresh() {
    this.setData({ a: this.data.a + 1 })
  }
})
