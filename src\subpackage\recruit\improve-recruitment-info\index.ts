/*
 * @Date: 2024-04-18 16:15:29
 * @Description: 完善信息
 */

import { tryPromise } from '@/utils/tools/common/index'

import { actions, dispatch, storage } from '@/store/index'
import { app } from '@/config/index'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { judgeFeatureChargeComplete, transformDescriptionV2 } from '../utils/index'
import { getAgreementUrl } from '@/config/app'

const Bury = wx.$.l.bury()

Page(class extends wx.$.Page {
  data = {
    list: [],
    controlInfoList: [], // 全部的数据
    portraits: [], // 画像ID
    currentList: [], // 当前页面需要填的数据
    otherList: [], // 职位关键词需要填的页面
    otherListRequire: null,
    info: {},
    keyWordsLabel: '', // 职位关键词展示的值
    keyWordsValue: {}, // 职位关键词选中的值
    chargesInfoList: [], // 收费项
    chargesInfoListRequire: null, // 收费项是否需要填
    chargesLabel: '', // 收费项显示的值
    chargesValue: {}, // 收费项选中的值
    publishBtnText: '提交',
    currentControlInfoList: [], // 当前页面模板数据
    otherControlInfoList: [], // 其他页面模板数据
    templateInfoName: '', // 模板名称
    otherControlInfoListRequire: false, // 其他页面模板数据是否必填
    showBiznameDialog: false,
    dialogData: {},
  }

  descriptions: any[]

  handleBizDialogHidden() {
    this.setData({
      showBiznameDialog: false,
    })
  }

  onBack() {
    const { type } = this.options
    if (this.options.jobId && (type !== 'manualImprovement' && type !== 'edit')) {
      wx.$.r.reLaunch({
        path: '/subpackage/recruit/published/index',
        success: () => {
          setTimeout(() => {
            wx.$.l.existJobTopSet(this.options.jobId, { jumpMethod: 'push' })
          }, 1000)
        },
      })
    } else {
      wx.$.r.back()
    }
  }

  async onShow() {
    /** 竞招会员页返回后需要直接发布 */
    const jobVieCheckType = storage.getItemSync('jobVieCheckType')
    storage.removeSync('jobVieCheckType')

    if (jobVieCheckType) {
      /** 购买竞招会员之后直接清理待开放缓存数据 */
      storage.removeSync('toPurchaseJobVieData')
      this.onSubmit({}, jobVieCheckType === 'COMMON' ? { publishType: 1, ignoreDialog: ['wjzqytc'] } : { ignoreDialog: ['wjzqytc'] })
    } else {
      /** 未购买竞招会员，返回发布页|完善页。将缓存待开放数据存入待开放 */
      const data = storage.getItemSync('toPurchaseJobVieData')
      storage.removeSync('toPurchaseJobVieData')
      if (data && Object.keys(data).length) {
        await wx.$.l.saveDraft(data, '信息已存入草稿箱，可在管理职位-待开放中继续发布')
        wx.$.r.reLaunch({
          path: '/subpackage/recruit/published/index',
          query: { activeTab: 'waiting' },
        })
      }
    }
  }

  onJumpRule() {
    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        url: `${app.MOBILE_TERMINAL_URL}index/otherzhaogongruler/`,
      },
    })
  }

  onGoToPrivacyPolicyPage(e) {
    const { type } = e.target.dataset
    wx.$.r.push({ path: `/subpackage/web-view/index?url=${getAgreementUrl(type)}` })
  }

  async onLoad(options) {
    const pagesList = getCurrentPages()
    const { routeParams: params = {} } = wx.$.nav.getDataPK(this) || {}
    const { recruitType = 1 } = params
    /** 当前页面是落地页，且origin是发布招工页，则从定向到发布招工页 */
    if (pagesList.length == 1 && options.origin == 'fast_issue' && !Object.keys(params).length) {
      wx.$.r.reLaunch({
        path: '/subpackage/recruit/fast_issue/index/index',
      })
      return
    }
    // 曝光埋点
    wx.$.collectEvent.event('Improve_information_page_expose')
    storage.removeSync('jobVieCheckType')
    storage.removeSync('toPurchaseJobVieData')
    const occId = Number(options.occId)
    const { jobId, type } = options
    const { data: temData } = await wx.$.javafetch['POST/cms/occTemplate/v1/listTemplateByOccIdsV3']({
      occInfoList: [{
        occId,
        portrait: '',
      }],
      displayScenes: ['JOB_PERFECT_DISPLAY', 'JOB_FEES_DISPLAY'],
      controlNatureCodes: [recruitType == 1 ? 'FULL_TIME' : 'PART_TIME'],
      businessDomain: 'job',
    })
    // 找到当前工种匹配的数据
    const template = temData.find((item) => item.occId == occId)
    // 完善页可用的数据
    const { currentControlInfoList, otherControlInfoList, chargesInfoList } = await wx.$.l.transformControlInfoList(template.templateInfo.controlInfoList) as any
    let name = await wx.$.l.getClassifyByIds([occId], params.recruitType || 1)
    if (!Array.isArray(name) || !name.length) {
      name = await wx.$.l.getClassifyByIds([occId], (params.recruitType || 1) === 1 ? 2 : 1)
    }

    this.setData({
      userAcq: params.userAcq,
      type: options.type,
      isPublish: options.isPublish == 'true',
      // eslint-disable-next-line no-nested-ternary
      publishBtnText: options.isPublish == 'true' ? (params.publishBtnStatus == 1 ? '发布职位' : '免费发布职位') : '提交',
      templateInfoName: name[0]?.name || '',
      controlInfoList: template.templateInfo.controlInfoList,
      portraits: template.portraits || [],
      currentControlInfoList,
      otherControlInfoList,
      otherControlInfoListRequire: !!otherControlInfoList.find((item) => item.ifJobPerfectMust),
      chargesInfoList,
      chargesInfoListRequire: !!chargesInfoList.find((item) => item.controlAttrObj.required),
    })
    const { values } = wx.$.nav.getDataPK(this) || {}
    let jobCompleteInfo
    if (values?.perfectInfo || type === 'manualImprovement' || params.copyId) {
      // 如果是管理招工的完善过来，需要查询接口进行数据回显
      if (type === 'manualImprovement' || params.copyId) {
        const { data: { jobCompleteV3: { completes } } } = await wx.$.javafetch['POST/job/v3/manage/job/complete/jobCompleteInfo']({
          isPublish: type === 'manualImprovement' ? false : !!params.copyId,
          copyJobId: params.copyId || undefined,
          occIds: [occId],
          jobId: jobId || undefined,
        })
        const list = await wx.$.l.transformTemplateInfo([template])
        jobCompleteInfo = (await wx.$.l.getImproveRecruitmentInfo(list, completes))[0].perfectInfo
      }
      const perfectInfo = values?.perfectInfo || jobCompleteInfo
      const form = await wx.$.selectComponent.call(this, '#form')
      if (perfectInfo.values && Object.keys(perfectInfo.values).length) {
        form.setValues(perfectInfo.values)
      }
      this.getLabel(perfectInfo.keyWordsValue)
      this.getChargesLabel(perfectInfo.chargesValue)
    }
  }

  getLabel(data) {
    const list = this.data.otherControlInfoList.reduce((arr, item) => [...arr, ...item.controlAttrObj.labelList], [])
    const value = Object.keys(data).reduce((arr, item) => [...arr].concat(data[item]), [])
    const label = value.map((item) => list.find((i) => i.code === item)?.name).filter(Boolean).join('、')
    this.setData({ keyWordsLabel: label, keyWordsValue: data })
  }

  // 跳转去完善
  onTap() {
    wx.$.nav.winPush(
      wx.$.nav.getThisPage(this),
      '/subpackage/recruit/improve-recruitment-word/index',
      { occId: Number(this.options.occId) },
      (data) => this.getLabel(data),
      {
        otherControlInfoList: this.data.otherControlInfoList,
        keyWordsValue: this.data.keyWordsValue,
      },
    )
  }

  getChargesLabel(data) {
    this.setData({ chargesLabel: Object.keys(data || {}).length ? '收费项信息已填写' : '', chargesValue: data })
    // const list = this.data.chargesInfoList.reduce((arr, item) => [...arr, ...item.controlAttrObj.labelList], [])
    // const value = Object.keys(data).reduce((arr, item) => arr.concat(data[item] ? data[item].value : []), [])
    // const label = value.map((item) => list.find((i) => i.code === item)?.name).filter(Boolean).join('、')
    // this.setData({ chargesLabel: label, chargesValue: data })
  }

  onTapCharges() {
    const { occId, jobId, type } = this.options
    const { chargesInfoList, controlInfoList, chargesValue, portraits } = this.data
    wx.$.nav.winPush(
      wx.$.nav.getThisPage(this),
      '/subpackage/recruit/improve-recruitment-charges/index',
      {},
      (data) => {
        this.getChargesLabel(data)
      },
      {
        portraitId: portraits[0],
        occId,
        controlInfoList,
        chargesInfoList,
        chargesValue,
        jobId: type === 'manualImprovement' || type === 'edit' ? jobId : '',
      },
    )
  }

  // 提交
  async onSubmit(_, extra: Record<string, any> = {}) {
    await wx.$.u.waitAsync(this, this.onSubmit, [_, extra], 300, {})
    try {
      const { currentControlInfoList, otherControlInfoList, chargesInfoList, chargesValue, keyWordsValue, publishBtnText } = this.data
      // 点击完善职位页面的提交按钮--埋点
      wx.$.collectEvent.event('post_job_click', { button_name: publishBtnText })
      const form = await wx.$.selectComponent.call(this, '#form')

      const msg = '必填项完成之后才能提交'
      // 当前页面验证必填项
      const values = await form.verify([...currentControlInfoList].reduce((obj, item) => {
        return { ...obj, [item.controlCode]: { required: item.controlAttrObj.required, message: msg } }
      }, {}))

      // 校验职位关键词 和 校验付费项
      await form.verify([...otherControlInfoList, ...chargesInfoList].reduce((obj, item) => {
        return { ...obj, [item.controlCode]: { required: item.controlAttrObj.required, message: msg } }
      }, {}), { msg: true }, { ...keyWordsValue, ...chargesValue })

      // 如果是要返回上一级就返回s
      const { isBack, routeParams = {} } = wx.$.nav.getDataPK(this) || {}
      if (isBack) {
        wx.$.nav.event({ values, keyWordsValue, chargesValue })
        wx.$.nav.back()
      } else if (routeParams.jobId) {
        await this.addComplete({ ...values, ...keyWordsValue, ...chargesValue }).catch(console.error)
      } else {
      /** 发布完善 */
        await this.publish({ ...values, ...keyWordsValue, ...chargesValue }, extra)
      }
    } catch (error) {
      console.error(1231, error)
    }
  }

  async publish(values: Record<number, any>, extra: Record<string, any> = {}) {
    // 没有就直接走提交接口
    const { chargesValue } = this.data
    const { routeParams = {} } = wx.$.nav.getDataPK(this)
    const { portraits, controlInfoList } = this.data
    const occId = Number(this.options.occId)
    const [{ chargeCode }] = await judgeFeatureChargeComplete([{
      perfectInfo: { chargesValue },
      occId,
      portraits,
      templateInfo: {
        controlInfoList,
      },
    }])
    const completes = [transformDescriptionV2(values, occId, portraits[0], chargeCode)]

    /** 组装预检接口数据 */
    const params = await wx.$.l.deleteNullProp({
      areaId: routeParams.areaId,
      occV2: routeParams.occV2,
      title: routeParams.title,
      detail: routeParams.detail,
      mobile: routeParams.mobile,
      address: routeParams.address,
      location: routeParams.location,
      code: routeParams.code,
      closeInfoId: routeParams.closeInfoId,
      verifyToken: routeParams.verifyToken,
      recruitType: routeParams.recruitType,
      promoteCityId: routeParams.promoteCityId,
      jobCompleteV3: {
        completes,
      },
      // complete: [{
      //   occId: routeParams.occV2[0].occIds[0],
      //   info,
      // }],
      ...extra,
    })
    Bury.then((bury) => {
      bury.operation({
        data: JSON.stringify(params),
        functionName: 'publish',
        name: '发布招工',
      })
    })

    return wx.$.l.prevCheckWithDraft(
      params,
      { serial_number: routeParams.serial_number,
        reportData: routeParams.reportData,
        ...extra },
      async (temporary, response) => {
        const { isSMSPublish, serial, reportData } = routeParams
        if (Number(serial) === 2) { /** 如果来自流程2 */
          dispatch(actions.storageActions.removeItem('fastIssueContent'))
        }

        this.reportReleaseRecruitment(reportData, response)
        const jobId = wx.$.u.getObjVal(response, 'data.data.id', '')
        if (!temporary && isSMSPublish) {
          const { data } = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/publish/checkFirst']({}))
          if (data && data.status) {
            const classifyId = await wx.$.l.transformOccV2ToHidClsId(params.occV2)
            wx.$.r.replace({
              path: '/subpackage/recruit/fast_issue/follow_wechat/index',
              query: {
                classifyId,
                areaId: params.areaId,
                jobId,
              },
            })
            return
          }
        }
        /** 临时发布点击去认证，不需要跳转到置顶页 */
        if (temporary) return
        wx.$.l.jumpToRecruitTopSet(jobId)
      },
    ).catch(async (error) => {
      /** 如果抛出错误是个对象，且存在dialogIdentify字符串包含在特殊弹窗标识列表中 */
      if (error && ['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(error.dialogIdentify)) {
        const published = error.dialogIdentify == 'Guidetofillafterpublishing'
        if (published && Number(routeParams.serial) === 2) { /** 如果来自流程2 */
          dispatch(actions.storageActions.removeItem('fastIssueContent'))
        }

        if (published && routeParams.isSMSPublish) {
          const { data } = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/publish/checkFirst']({}))
          if (data && data.status) {
            const classifyId = await wx.$.l.transformOccV2ToHidClsId(params.occV2)
            wx.$.r.replace({
              path: '/subpackage/recruit/fast_issue/follow_wechat/index',
              query: {
                classifyId,
                areaId: params.areaId,
                jobId: error.jobId,
              },
            })
          }
        }

        if (published && error.jobId) {
          const newReportData = { ...(routeParams.reportData || {}), info_id: error.jobId }
          wx.$.collectEvent.event('releaseRecruitment', newReportData)
        }
        // TODO 获取弹窗配置，唤起弹窗
        const popup = await dealDialogRepByApi(error.dialogIdentify)
        if (popup) {
          /** 弹出弹窗 */
          this.setData({
            showBiznameDialog: true,
            dialogData: {
              ...popup,
              ...error,
            },
          })
        }
      }
    })
  }

  /** 订单类完善 */
  async addComplete(values: Record<number, any>) {
    const occId = this.options.occId || ''
    const complete = transformDescriptionV2(values, occId)

    if (complete.items?.length) {
      wx.$.loading('正在完善...')
      await wx.$.javafetch['POST/job/v3/manage/job/complete/addComplete']({
        jobId: this.options.jobId,
        jobCompleteV3: {
          completes: <any>[complete],
        },
      })
      wx.hideLoading()
      await wx.$.msg('完善信息成功', 1500, true)
    }
    /** TODO 完善埋点 */
    // this.perfBuryPoint([{ occId, info: completes }])
    // 跳转
    wx.$.l.jumpToRecruitTopSet(this.options.jobId)
  }

  // perfBuryPoint(selected: any[] = []) {
  //   const result = { jobId: this.options.jobId, operationType: '新增' }
  //   Array.isArray(selected) && selected.reduce((result, current, index) => {
  //     // eslint-disable-next-line no-param-reassign
  //     result[`class_work2_${index}`] = current ? current.occId : ''
  //     // eslint-disable-next-line no-param-reassign
  //     const { list = [], currentList = [], otherList = [] } = this.data

  //     // eslint-disable-next-line no-param-reassign
  //     result[`improve_information_${index}`] = current ? this.formatImproveInfo(current, [...list, ...currentList, ...otherList]) : ''
  //     return result
  //   }, result)

  //   wx.$.collectEvent.event('perfectRecruitmentInformation', result)
  // }

  formatImproveInfo(selectedItem: { occId: string, info: { descriptionId: number, trendsSetting?: string }[] }, origins: any[]) {
    const { info = [] } = selectedItem

    this.descriptions = this.descriptions || origins.reduce((target, current) => {
      if (current && Array.isArray(current.info)) {
        // eslint-disable-next-line no-param-reassign
        target = target.concat(current.info)
      }
      return target
    }, [])
    return info.length ? info.reduce((list, current) => {
      const { descriptionId, trendsSetting } = current
      const { descName } = this.descriptions.find(item => item.descId == descriptionId) || { descName: '' }
      if (trendsSetting !== undefined) {
        list.push((trendsSetting || '0') + descName)
      } else {
        list.push(descName)
      }

      return list
    }, <any[]> []).join('/') : ''
  }

  /**
   * 发布上报
   * @param {any} data 上报数据
   * @param response 发布成功接口返回
   */
  reportReleaseRecruitment(data: Record<string, any>, response: Record<string, any>) {
    const newData = { ...data }
    /** 鱼泡快招上报 */
    if (ENV_SUB === 'findjzgr') {
      newData.info_id = wx.$.u.getObjVal(response, 'data.data.id', '')
    }
    wx.$.collectEvent.event('releaseRecruitment', newData)
  }
})
