/*
 * @Date: 2022-02-28 13:49:02
 * @Description: 工具库-修改职位信息
 */

import { tools } from '@/utils/index'
import { store } from '@/store/index'
import { tryPromise } from '@/utils/tools/common/index'
import { nwSaveFilterStoreByIds, getImageAndVideo, handlerImageAndVideo } from '@/utils/helper/common/index'
import { removeIncompleteUnicode } from '@/utils/tools/formatter/index'
import { getObjVal } from '@/lib/mini/utils/utils'

/** 弹框显示的标识 */
export const modalStatus = {
  /** 发布成功工种替换弹窗 */
  classifiesReplace: 'classifies-replace',
  /** 完善工种信息弹框 */
  jobPerfect: 'job-perfect',
}

/** 通用弹框处理 */
async function handlerDialogCall(popup, res, oldRes?) {
  // eslint-disable-next-line no-param-reassign
  oldRes = oldRes || { code: -1 }
  wx.hideLoading()
  const { recruitInfo } = this.data
  const formData = await wx.$.selectComponent.call(this, '#jisu-form').then(async (comp) => comp.getValues())
  const { areaId, detail, title, address } = await handlerSubmitParams.call(this, formData, false)
  const areaObj: any = await wx.$.l.getAreaById(areaId)
  const { id: cityId } = areaObj.city || areaObj.province || {}
  let mlocation = formData.selectArea && formData.selectArea.location
  const { location: loca } = recruitInfo || {}
  const { longitude, latitude } = loca || {}
  if (!mlocation && longitude && latitude) {
    mlocation = [longitude, latitude].join(',')
  }
  const dialogIdentify = wx.$.u.getObjVal(res.dialogData, 'dialogIdentify')

  return new Promise(async (resolve, reject) => {
    if (dialogIdentify == 'wjzqytc') {
      const tabType = wx.$.u.getObjVal(res.dialogData, 'template.tabType')
      const comp = await wx.$.selectComponent.call(this, '#jisu-form')
      const values = comp.getValues().classifies
      const occupationV2Ids = values.map(i => {
        return i.id
      }).join(',')
      const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}&occupationV2Ids=${occupationV2Ids}&cities=${cityId}`)
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      reject({ ...res })
      return
    }

    //  若用户未完成实名认证，引导实名认证
    if (dialogIdentify === 'RealNameHaveNoTimes') {
      wx.$.r.push({ path: '/subpackage/member/realname/index' })
      reject({ ...res })
      return
    }

    if (dialogIdentify === 'permit_due_popup') {
      const url = encodeURIComponent('/enterprise-verify?tabType=0')
      wx.$.r.push({
        path: `/subpackage/web-view/index?url=${url}`,
      })
      reject({ ...res })
      return
    }

    wx.$.showModal({
      ...popup,
      success: async (result) => {
        // eslint-disable-next-line no-param-reassign
        result = result || { btnIndex: -1, routePath: '' }
        const { routePath } = result

        switch (`${routePath}`) {
          case 'modify2publish':
            wx.$.r.replace({
              path: '/subpackage/recruit/fast_issue/index/index',
              query: {
                flag: routePath,
                areaId,
                occV2: (Array.isArray(formData.classifies) ? formData.classifies.map((it) => it.id) : []).join(','),
                content: detail,
                address,
                adCode: formData.city.adcode,
                location: mlocation,
                title,
              },
            })
            break
          case 'toEditInfo': // 修改职位的付费弹框点击的【去修改】按钮
            break
          case 'top': // 点击【去置顶】按钮
            // 跳转到置顶页
            wx.$.l.existJobTopSet(recruitInfo.id, { jumpMethod: 'replace' })
            resolve({ ...res, routePath, resultBtn: result })
            return
          case 'toEarnPoints': // 去赚积分按钮标识
            wx.$.toGetIntegral({
              isFromPage: 'CLRefreshRecruitSet',
              jobId: recruitInfo.id,
            })
            break
          case 'toapply': {
            const url = encodeURIComponent('/enterprise-verify?tabType=1')
            wx.$.r.push({
              path: `/subpackage/web-view/index?url=${url}`,
            })
            break
          }
          case 'viewDetails': // 账号异常的跳转处理
            wx.$.r.push({ path: '/subpackage/systips/index' })
            resolve({ ...res, routePath, resultBtn: result })
            return
          case 'toResume': // 跳转到工友信息列表
          case 'workerList': // 跳转到工友信息列表
            // 处理页面传入页面的路由参数
            await nwSaveFilterStoreByIds(`${cityId}`, formData.classifies || [], 'resume')
            wx.$.r.push({ path: '/pages/resume/index' })
            resolve({ ...res, routePath, resultBtn: result })
            return
          case 'toCompanyAuth': { /** publishEnterpriseAuthOnlinePromp 企业认证弹窗 立即认证按钮 */
            wx.$.r.reLaunch({ path: '/subpackage/member/firmAuth/index', query: { origin: 'publishRecruitPop' } })
            resolve({ ...res, routePath, resultBtn: result })
            return
          }
          case 'return': {
            const cPages = getCurrentPages() || []

            if (cPages && cPages.length > 1) {
              const prevPageRoute = cPages[cPages.length - 2].route
              if (prevPageRoute.includes('resume/detail')) {
                wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' })
                resolve({ ...res, routePath, resultBtn: result })
                return
              }
            }

            wx.$.r.back()
            resolve({ ...res, routePath, resultBtn: result })
            return
          }
          case 'toRealName': {
            wx.$.r.replace({ path: '/subpackage/member/realname/index' })
            resolve({ ...res, routePath, resultBtn: result })
            return
          }
          default: {
            /** 其他按钮事件  */
            if (dialogIdentify === 'customer_due_popup') {
              wx.$.r.back()
            }
            if (routePath && routePath.indexOf('/') > -1) {
              // 这里主要是为了处理跳转到其他页面的情况，然后阻止后续的逻辑
              resolve({ code: -1, resultBtn: result })
            } else {
              // 走到这里代表点击了取消按钮或者没有匹配到对应的按钮
              resolve({ ...oldRes, resultBtn: result })
            }
            return
          }
        }
        resolve({ ...res, resultBtn: result })
      },
    })
  })
}

/** 组装表单数据  */
export async function rinseFormData(data: YModels['POST/job/v3/manage/job/modify/info']['Res']['data']) {
  // 获取城市信息
  let areaInfo = <any>{ ad_code: '', id: '' }
  // 工种数据
  const classifies = await wx.$.u.getClassify2ByIds(data.occV2 || [])
  const address = (data.address || '').includes('@@@@@') ? `${data.address || ''}`.split('@@@@@')[0] : ' '
  const area = await wx.$.l.getAreaById(data.countyId || data.cityId || data.provinceId)
  /** 地址选择器兼容竞招模式 */
  if (data.isCompete && area.city) {
    areaInfo.cityId = area.city.id
    areaInfo.cityAdcode = area.city.ad_code
    areaInfo.city = area.city.name || area.city.ad_name
  }
  area.current && (areaInfo = { ...areaInfo, ...area.current, adcode: area.current.ad_code })
  if (data.location && data.location.latitude) {
    areaInfo.location = `${data.location.longitude},${data.location.latitude}`
  }

  return {
    title: data.title,
    userName: data.userName,
    tel: data.userMobile,
    city: {
      ...areaInfo,
      name: data.addressTitle || '',
      highlight: data.highlightAddress || '',
    },
    classifies,
    /** 工友实名标签的选项 */
    realNameSwitch: {
      /** 是否显示工友实名组件 */
      realNameShare: data.realNameShare,
      /** 实名工友专享：0=关闭；1=开启 */
      realNameShareSelect: data.realNameShareSelect ? 1 : 0,
    },
    image: handlerImageAndVideo(data.mediaResource),
  }
}

/** 添加完善职位信息, 这个必须是在修改职位信息成功之后才能调用 */
async function addCompleteCall(params: any, res) {
  // const { isChangeClassify } = this.data
  // const select = this.data.perfectSelect
  const completes = getObjVal(params, 'jobCompleteV3.completes', [])
  if (res.code == 0 && completes.length) {
    wx.$.javafetch['POST/job/v3/manage/job/complete/addComplete'](params)
  }
}

/** 是否是招聘类的工种 */
export async function getJobClassify(occV2) {
  const classifyByData = await wx.$.u.getClassify2ByIds(occV2)
  return classifyByData.some((item) => item.mode == 2)
}

/** 获取工种id集合，返回，12,123,45345工种id字符串以,隔开 */
export function getClassifiesIdStr(occV2): string {
  if (wx.$.u.isArrayVal(occV2)) {
    const ids = []
    occV2.forEach((item) => {
      item.occIds.forEach((occId) => {
        ids.push(+occId)
      })
    })
    ids.sort()
    return ids.join(',')
  }
  return ''
}

/** 获取职位信息 */
export async function getRecruitInfo(jobId) {
  try {
    const res = await wx.$.javafetch['POST/job/v3/manage/job/modify/info']({ jobId })

    if (res.code == 0) {
      const { data } = res
      data.title = `${data.title || ''}`.slice(0, 45)
      const formData = await rinseFormData(data)
      const oldClassifies = getClassifiesIdStr(data.occV2)
      return { data, formData, oldClassifies }
    }
    throw res
  } catch (err) {
    wx.$.alert({
      content: err.message ? err.message : '获取职位信息失败',
      confirmText: '知道了',
    }).then(() => {
      wx.$.r.back()
    })
    return null
  }
}

/** 获取职位完善配置 */
export async function getSettingDescription(params: YModels['POST/job/v3/manage/job/complete/jobCompleteInfo']['Req']) {
  // 工种数据需要去重
  // eslint-disable-next-line no-param-reassign
  params.occIds = wx.$.u.uniqueArr(params.occIds)
  const res = await tryPromise(wx.$.javafetch['POST/job/v3/manage/job/complete/jobCompleteInfo']({
    ...params,
    isPublish: false,
  }), { data: {} })
  return res.data
}

/** 合规词实名检验  */
export async function fetchComplianceWords(params: YModels['POST/job/v2/manage/job/publish/complianceWords']['Req']): Promise<any> {
  const res = await wx.$.javafetch['POST/job/v2/manage/job/publish/complianceWords'](params, {
    showErrCodes: ['10001'],
  })
  if (res.popup) {
    return handlerDialogCall.call(this, res.popup, res)
  }
  return res
}

/** 修改职位信息的预检查 */
export async function setRecruitInfoCheck(params: YModels['POST/job/v3/manage/job/modify/preCheck']['Req'], ext = {}): Promise<any> {
  const { ignore = [] } = <any>ext

  const res = await wx.$.javafetch['POST/job/v3/manage/job/modify/preCheck'](params, {
    showErrCodes: ['10001'],
  })
  if (ignore && ignore.includes(wx.$.u.getObjVal(res, 'dialogData.dialogIdentify'))) {
    return Promise.reject('静默失败')
  }
  if (res.popup) {
    return handlerDialogCall.call(this, res.popup, res)
  }
  return res
}

/** 修改职位信息 */
export async function setRecruitInfo(params: YModels['POST/job/v2/manage/job/modify/job']['Req'], ignore = []): Promise<any> {
  const { componentsParams, ...resParams } = <any>params
  const res = await wx.$.javafetch['POST/job/v3/manage/job/modify/job'](resParams, {
    showErrCodes: ['10001'],
  })

  if (ignore && ignore.includes(wx.$.u.getObjVal(res, 'dialogData.dialogIdentify'))) {
    return Promise.reject('静默失败')
  }
  tryPromise(addCompleteCall(componentsParams, res), null)
  if (res.popup) {
    return handlerDialogCall.call(this, res.popup, res, res)
  }
  return res
}

/** 验证提交的数据 */
export function verificationField(formData, recruitInfo, isVaildPhone = false, isMustBePerfected = false, isDraft = false) {
  const { userState } = store.getState().storage
  // 不为空 或者为必填时 校验中文和长度
  if ((formData.title || (formData.templates.jobTitle?.ifJobPostMust && formData.templates.jobTitle?.status)) && !tools.validator.isVaildVal(formData.title, 2, 45)) {
    wx.$.msg('请正确输入2-45字职位标题，必须含有汉字')
    return false
  }

  if (!formData.detail) {
    wx.$.msg('请输入职位详情')
    return false
  }
  if (!tools.validator.isVaildVal(formData.detail, 3, 1500)) {
    wx.$.msg('请正确输入3~1500字职位详情，必须含有汉字')
    return false
  }
  if (!formData.city || !formData.city.id) {
    wx.$.msg('请选择项目地点')
    return false
  }
  if (!formData.classifies || formData.classifies.length == 0) {
    wx.$.msg('请选择职位')
    return false
  }
  if (isMustBePerfected) {
    wx.$.msg('完善项必填项完成之后才能提交')
    return false
  }
  if (!tools.validator.allChinese(formData.userName) && !isDraft) {
    wx.$.msg('请输入2-5中文汉字姓名')
    return false
  }
  if (!formData.tel) {
    wx.$.msg('请输入手机号')
    return false
  }
  if (!tools.validator.isPhone(formData.tel)) {
    wx.$.msg('请正确输入11位手机号')
    return false
  }
  /**
   * 手机号不为登录手机号，
   * 1. 待开放每次修改都要展示验证码
   * 2. 空号展示验证码
   * 3. 修改招工时，手机号修改过展示验证码
   */
  if (((formData.tel != recruitInfo.userMobile || isDraft) && formData.tel != userState.tel) || isVaildPhone) {
    if (!formData.verifyToken) {
      wx.$.msg('请先获取手机验证码')
      return false
    }
    if (!formData.code) {
      wx.$.msg('请输入验证码')
      return false
    }
  }
  if (formData.image.some((item) => item.uploadState !== 1)) {
    wx.$.msg('请在视频或图片上传完成后操作')
    return false
  }
  return true
}

/** 处理提交的address值 */
function getSubmitAddress(selectArea, address) {
  let newAddress = address
  if (selectArea && selectArea.name) { // 已选择地址的情况
    const district = selectArea.district || ''
    const address = selectArea.address || ''
    newAddress = selectArea.name
    if (district || address) {
      newAddress += `@@@@@${district}${address}`
    }
  }
  return newAddress
}

/** 处理提交的数据 */
export async function handlerSubmitParams(values, isVaildPhone = false) {
  const { userState } = store.getState().storage
  if (!values.city) {
    return {} as any
  }
  const mediaResource = getImageAndVideo(values.image)
  const { recruitInfo, detail, tel, templates } = this.data
  const { selectArea = {} } = values.city
  let areaId = recruitInfo.countyId || recruitInfo.cityId || recruitInfo.provinceId

  // 处理地址
  if (selectArea.areaInfo) {
    areaId = selectArea.areaInfo.id
  }

  /** 经纬度 */
  let location = selectArea.location || recruitInfo.location
  if (!location) {
    location = undefined
  }
  if (wx.$.u.typeOf(location) == 'string') {
    // 处理经纬度
    location = {
      longitude: location.split(',')[0],
      latitude: location.split(',')[1],
    }
  }

  // 地址信息
  const params = <any>{
    jobId: recruitInfo.id,
    userName: values.userName,
    areaId,
    address: getSubmitAddress(selectArea, recruitInfo.address),
    occV2: await wx.$.l.getBtmClassifyChild(values.classifies),
    // title: values.title,
    detail: removeIncompleteUnicode(detail),
    mobile: values.tel,
    location,
    code: values.code,
    verifyToken: values.verifyToken,
    /** 上传的项目资料 */
    mediaResource,
    /** 实名工友专享：0=关闭；1=开启, 为undefined时不提交 */
    realNameSwitch: undefined,
    // images: values.images ? values.image.filter((img) => img.url).map((item) => item.url).join(',') : '',
    /**  工厂专区新版本中标识：职位信息包含的工种类型：1-只含有工厂类工种 2-只含有工程类工种 3-包含工程+工厂，老版本中该字段为空 */
    // fac_occupation_type: recruitInfo.model.fac_occupation_type || '',
  }

  if (templates.jobTitle?.status) {
    params.title = values.title
  }

  // 处理是否需要设置实名工友字段
  if (values.realNameSwitch && values.realNameSwitch.realNameShare) {
    params.realNameSwitch = values.realNameSwitch.realNameShareSelect
  }

  // 处理验证码
  if ((tel == recruitInfo.userMobile || tel == userState.tel) && !isVaildPhone) {
    params.code = undefined
    params.verifyToken = undefined
  }

  return params
}
