/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

import { insertHelper } from '@/pages/utils'

Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    info: {
      type: Object,
      value: {},
    },
    /** 页面参数 type = 'whoContacted'代表谁联系过我，'myContacted'我联系的人 */
    query: {
      type: Object,
      value: { type: '' },
    },
    /** 当前页面是否是百度seo分享页面 */
    isSeoPage: {
      type: Boolean,
      value: false,
    },
    /** 会员横幅 */
    vipBanner: {
      type: Object,
      value: {},
    },
    operatorVisible: {
      type: Boolean,
      value: true,
    },
    btnObj: {
      type: null,
      value: {},
    },
    rightsInfo: {
      type: Object,
      value: {},
    },
    isAndroid: {
      type: Boolean,
      value: false,
    },
    loginTextGroup: {
      type: null,
      value: {},
    },
    login: {
      type: <PERSON>olean,
      value: false,
    },
  },
  observers: {
    vipBanner(vipBanner) {
      if (vipBanner.showBanner && !this.data.btnObj?.chat?.isShow) {
        wx.$.collectEvent.event('vipCardExposure', {
          content_texts: vipBanner.bannerContent,
          page_name: '招工详情',
          // eslint-disable-next-line no-nested-ternary
          button_name: vipBanner.bannerType == 1 ? '开通会员' : vipBanner.bannerType == 2 ? '续费会员' : '',
        })
      }
    },
    btnObj(v) {
      const { info, query } = this.data
      // 是否可以显示拨打电话和免费聊
      const isShowCallChatBtn = (info.isEnd.code == 1 || query.type == 'whoContacted' || query.type == 'myContacted')
      const { phone, chat, delivery } = v || {}
      const btnObjCur: any = {}
      if (phone) {
        btnObjCur.phone = { ...phone, isShow: phone.isShow && isShowCallChatBtn }
      }
      if (chat) {
        btnObjCur.chat = { ...chat, isShow: chat.isShow && isShowCallChatBtn && ENV_IS_WEAPP }
      }
      if (delivery) {
        btnObjCur.delivery = { ...delivery }
      }
      this.setData({ btnObjCur })
    },
    rightsInfo(v) {
      const { btnObjCur, info } = this.data
      const { delivery, phone, chat } = btnObjCur || {}
      const { aboutInfoSpecifics } = info || {}
      const { postInfo } = aboutInfoSpecifics || {}
      const { hasPost } = postInfo || {}
      if (!hasPost && delivery && phone && chat) {
        let isOK = false
        if ((delivery.isShow && phone.isShow && chat.isShow) || (delivery.isShow && chat.isShow && !phone.isShow)) {
          isOK = true
        }
        const { topping, urgent } = v || {}
        if (isOK && (topping || urgent)) {
          wx.$.collectEvent.event('apply_button_impression', { topping: `${topping}`, urgent: `${urgent}` })
        }
      }
    },
  },
  data: {
    btnObjCur: {
      phone: {
        type: 'phone',
        btnText: '拨打电话',
        isShow: true,
      },
      chat: {
        type: 'chat',
        btnText: '聊一聊',
        isShow: true,
      },
    },
  },
  lifetimes: {

  },
  methods: {
    /** 联系老板功能 */
    async onContactBoss() {
      await wx.$.u.waitAsync(this, this.onContactBoss, [], 2000)
      if (await wx.$.l.isShowBlackModel()) return
      insertHelper.trigger(3)
      wx.setStorageSync('recruit_detail_back_source', 'call')
      this.triggerEvent('contactBoss', { click_entry: '1' })
    },
    /** 查看更多招工 */
    onLookMore() {
      wx.$.r.push({ path: '/pages/index/index' })
    },
    /** 一键投递功能 */
    async onGoToDelivery(e) {
      await wx.$.u.waitAsync(this, this.onGoToDelivery, [e], 2000)
      const click_entry = wx.$.u.getObjVal(e, 'currentTarget.dataset.click_entry', '8')
      if (await wx.$.l.isShowBlackModel()) return
      insertHelper.trigger(3)
      this.triggerEvent('goToDelivery', click_entry)
    },
    /** 聊一聊功能 */
    async onGoToChat() {
      await wx.$.u.waitAsync(this, this.onGoToChat, [], 2000)
      if (await wx.$.l.isShowBlackModel()) return
      wx.setStorageSync('recruit_detail_back_source', 'chat')
      this.triggerEvent('goToChat')
    },
    /** 拨打记录 */
    onCallHistory() {
      wx.$.r.push({ path: '/subpackage/member/myContactHistory/index' })
    },
    /** 跳转会员购买页 */
    toVip() {
      const { vipBanner } = this.data
      const url = encodeURIComponent(`/member-vip-new/detail?vipType=${vipBanner.vipType}&type=0`)
      wx.$.collectEvent.event('vipCardClick', {
        content_texts: vipBanner.bannerContent,
        page_name: '招工详情',
        // eslint-disable-next-line no-nested-ternary
        button_name: vipBanner.bannerType == 1 ? '开通会员' : vipBanner.bannerType == 2 ? '续费会员' : '',
      })
      wx.$.r.push({ path: `/subpackage/web-view/index?isLogin=true&url=${url}` })
    },
    /** 点击分享按钮-埋点 */
    onPointer() {
      wx.$.collectEvent.event('share_click', {
        page_name: '招工详情页',
      })
    },
    onConfirmCancel() {
      const { info } = this.data
      const { userName } = info || {}
      wx.$.confirm({
        title: '取消不感兴趣',
        content: `取消设置不感兴趣后，系统将继续提示来自“${userName}”的消息，是否要取消设置？`,
        cancelText: '我再想想',
        confirmText: '确定',
      }).then(async () => {
        this.onCancelDislike()
      })
    },
    onCancelDislike() {
      const { info } = this.data
      const { jobId } = info || {}
      wx.showLoading({ title: '请求中...' })
      wx.$.javafetch['POST/clues/v1/inappropriate/removeByJobId']({ jobId }).then((res) => {
        const { code, message } = res || {}
        if (code != 0) {
          wx.hideLoading()
          wx.$.msg(message || '请求失败,请稍后重试')
          return
        }
        const pages = getCurrentPages()
        if (wx.$.u.isArrayVal(pages)) {
          const page = pages.find(item => item.route.indexOf('subpackage/tim/groupConversation/index') >= 0)
          if (page) {
            page.onRefreshGroupInfo && page.onRefreshGroupInfo()
          }
        }
        wx.$.msg('已取消不感兴趣')
        this.triggerEvent('refresh')
      }).catch((err) => {
        wx.hideLoading()
        const { error, message } = err || {}
        let msg = '请求异常,请稍后重试'
        if (error && message) {
          msg = message
        }
        wx.$.msg(msg)
      })
    },
  },
})
