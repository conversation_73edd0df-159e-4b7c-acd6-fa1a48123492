/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 追聊消息修改弹框
 */

import { actions, dispatch } from '@/store/index'

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    maxContent: { type: Number, value: 120 },
    placeholder: { type: String, value: '请输入内容' },
    isFocus: { type: Boolean, value: true },
    conversation: { type: Object, value: {} },
    value: { type: String, value: '' },
  }

  observers = {
    value(v) {
      this.setData({ content: v })
    },
  }

  data = {
    content: '',
    /** 键盘弹起高度 */
    keyboardHeight: 0,
  }

  /** 抽屉组件已收起 */
  onClose() {
    this.setData({ content: '' })
    this.triggerEvent('close')
  }

  async onSend() {
    await wx.$.u.waitAsync(this, this.onSend, [], 2000)
    const { content, conversation } = this.data as DataTypes<typeof this>
    const { conversationId } = conversation || {} as any
    const value = content.trim()
    if (!value) {
      wx.$.msg('请输入追聊消息')
      return
    }
    wx.$.collectEvent.event('quick_greet_click', { click_button: '发送' })
    dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, isShowSayHelloAgain: false } }))
    const msg = '发送失败，请稍后重试'
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/batchCommit']({ conversationIds: [conversationId], helloWord: value }).then((res) => {
      wx.hideLoading()
      const { code, data } = res || {}
      const { successNum } = data || {}
      if (code == 0) {
        successNum == 0 && wx.$.msg(msg)
        this.setData({ content: '' })
        this.triggerEvent('close')
        return
      }
      wx.$.msg(msg)
    }).catch(() => {
      wx.hideLoading()
      wx.$.msg(msg)
    })
  }

  /** 输入框input事件 */
  onInput(e) {
    const { maxContent, content } = this.data as DataTypes<typeof this>
    const { value } = e.detail
    const valueArr = Array.from(value)
    if (content.length >= maxContent && valueArr.length > maxContent) {
      this.setData({ content })
      return
    }
    const nValue = this.getVal(valueArr, maxContent)
    this.setData({ content: nValue })
  }

  getVal(valueArr, num) {
    const { maxContent } = this.data as DataTypes<typeof this>
    const nValue = valueArr.slice(0, num).join('')
    if (nValue.length > maxContent) {
      return this.getVal(valueArr, num - 1)
    }
    return nValue
  }

  onFocus() {
    this.setData({ isFocus: true })
  }

  /** 收起键盘 */
  onHideKey() {
    this.setData({ isFocus: false, keyboardHeight: 0 })
  }

  // 键盘高度发生变化
  bindkeyboardheightchange(e) {
    if (e?.detail?.height == 0) {
      return
    }
    const height = (e?.detail?.height || 0)
    this.setData({ keyboardHeight: height })
  }

  /** 禁止滚动操作 */
  onCatch() { }
})
