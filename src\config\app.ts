/*
 * @Date: 2021-12-31 13:59:43
 * @Description: 小程序的基本配置
 */

import miniConfig from '@/miniConfig/index'

export const ENV_DEVELOPMENT_KEY = ENV_DEVELOPMENT != 'PRO'
  ? wx.getStorageSync('ENV_DEVELOPMENT_KEY') || ENV_DEVELOPMENT
  : ENV_DEVELOPMENT

// * 即时通信 IM 应用的 SDKAppID 列表
export const SDKAppID_LIST = {
  DEVELOP: '1600011650',
  DEV: '1600011650',
  DEV2: '1600011650',
  PRE: '1600019200',
  PRE2: '1600019200',
  REL: '1600019200',
  PRO: '1600019200',
}

// * 全局请求接口域名
export const REQUEST_LIST = {
  // * 开发站
  DEVELOP: 'https://yupao-test.yupaowang.com/', // 'https://yupao-test.vrtbbs.com/',
  // * 测试站
  DEV: 'https://yupao-test.yupaowang.com/',
  DEV2: 'https://yupao-test.yupaowang.com/',
  // * 预发布
  PRE: 'https://yupao-master.kkbbi.com/',
  PRE2: 'https://yupao-master.kkbbi.com/',
  // * 预发布正式站
  REL: 'https://yupao-release.kkbbi.com/',
  // * 正式站
  PRO: 'https://yupao-prod.54xiaoshuo.com/',
}

// * JAVA全局请求接口域名
export const REQUEST_JAVA_LIST = {
  // * 开发站
  DEVELOP: 'https://yupao-develop.yupaowang.com',
  // 测试站
  DEV: 'https://yupao-test.yupaowang.com',
  DEV2: 'https://yupao-test.yupaowang.com',
  // 预发布
  PRE: 'https://yupao-master.yupaowang.com',
  PRE2: 'https://yupao-master.yupaowang.com',
  // * 预发布正式站(JAVA的预正用预发布的域名)
  REL: 'https://yupao-master.yupaowang.com',
  // * 正式站
  PRO: 'https://yupao-prod.yupaowang.com',
}

// * 保险业务域名
export const INSURANCE_LIST = {
  // * 开发站
  DEVELOP: 'https://insurance.cdqlkj.cn/',
  // * 测试站
  DEV: 'https://insurance.cdqlkj.cn/',
  DEV2: 'https://insurance.cdqlkj.cn/',
  // * 预发布
  PRE: 'https://insurance-master.yupaowang.com/',
  PRE2: 'https://insurance-master.yupaowang.com/',
  // 预发布正式站
  REL: 'https://insurance.55ty.com.cn/',
  // 正式站
  PRO: 'https://insurance.55ty.com.cn/',
}

// * h5业务域名
export const H5_HYBRID = {
  // * 开发站
  DEVELOP: 'https://h5hybridtest.yupaowang.com',
  // * 测试站
  DEV: 'https://h5hybridtest.yupaowang.com',
  DEV2: 'https://h5hybrid-test-v2.yupaowang.com',
  // * 预发布
  PRE: 'https://h5hybridmaster.yupaowang.com',
  PRE2: 'https://h5hybrid-master-v2.yupaowang.com',
  // * 预发布正式站
  REL: 'https://h5hybridmaster.yupaowang.com',
  // * 正式站
  PRO: 'https://h5hybridprod.yupaowang.com',
}

// * M端地址（用于小程序直接打开H5协议规则页面）---版本1
//! 勿删,线上也在用
export const MOBILE_TERMINAL_LIST: { [key: string]: string } = {
  // * 开发站
  // eslint-disable-next-line sonarjs/no-duplicate-string
  DEVELOP: 'https://m-test.yupaowang.com/',
  // * 测试站
  DEV: 'https://m-test.yupaowang.com/',
  DEV2: 'https://m-test.yupaowang.com/',
  // * 预发布
  PRE: 'https://rm.kkbbi.com/',
  PRE2: 'https://rm.kkbbi.com/',
  // * 预发布正式站
  REL: 'https://mrelease.kkbbi.com/',
  // * 正式站
  PRO: 'https://m.yupao.com/',
}

// * 埋点应用ID
export const REPORT_APP_ID_LIST: { [key: string]: number } = {
  // * 开发站
  DEVELOP: 10000016,
  // * 测试站
  DEV: 10000002,
  DEV2: 10000002,
  // * 预发布
  PRE: 10000017,
  PRE2: 10000017,
  // * 预发布正式站
  REL: 10000017,
  // * 正式站
  PRO: 10000001,
}

// * M端地址（用于小程序直接打开混合开发的落地页）---版本2 这个用于m端的混合开发落地页
export const MOBILE_TERMINAL_NEW_LIST: { [key: string]: string } = {
  // * 开发站
  DEVELOP: 'https://h5hybridtest.yupaowang.com/',
  // * 测试站
  DEV: 'https://h5hybridtest.yupaowang.com/',
  DEV2: 'https://h5hybrid-test-v2.yupaowang.com/',
  // * 预发布
  PRE: 'https://h5hybridmaster.yupaowang.com/',
  PRE2: 'https://h5hybrid-master-v2.yupaowang.com/',
  // * 预发布正式站
  REL: 'https://h5hybridmaster.yupaowang.com/',
  // * 正式站
  PRO: 'https://h5hybridprod.yupaowang.com/',
}

// * 极验-设备验appid
export const GUARD_APP_ID_LIST: { [key: string]: string } = {
  // * 开发站
  DEVELOP: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  // * 测试站
  DEV: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  DEV2: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  // * 预发布
  PRE: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  PRE2: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  // * 预发布正式站
  REL: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
  // * 正式站
  PRO: 'vb2dgif5pwwfq4fz010kgsvsgdn6y9in',
}

// * 当前程序使用的请求地址
export const REQUEST_URL: string = REQUEST_LIST[ENV_DEVELOPMENT_KEY]

// * 当前程序使用的请求地址
export const REQUEST_JAVA_URL: string = REQUEST_JAVA_LIST[ENV_DEVELOPMENT_KEY]

// * 即时通信 IM 应用的
export const SDKAppID: string = SDKAppID_LIST[ENV_DEVELOPMENT_KEY]

// * 保险业务链接地址
export const INSURANCE_URL: string = INSURANCE_LIST[ENV_DEVELOPMENT_KEY]

// * h5业务链接地址
export const H5_HYBRID_URL: string = H5_HYBRID[ENV_DEVELOPMENT_KEY]

// * M端地址（用于小程序直接打开H5协议规则页面）
export const MOBILE_TERMINAL_URL: string = MOBILE_TERMINAL_LIST[ENV_DEVELOPMENT_KEY]

// * M端地址（用于小程序直接打开H5协议规则页面 ---版本2
export const MOBILE_TERMINAL_NEW_URL = MOBILE_TERMINAL_NEW_LIST[ENV_DEVELOPMENT_KEY]

// * 上传图片加水印地址
export const UPLOAD_WATER_IMG_URL = `${REQUEST_LIST[ENV_DEVELOPMENT_KEY]}file/file/upload`

// * 公司默认客服电话
export const SERVER_PHONE = '************'

// * 小程序当前版本号(关联本地存储)
export const VERSION = miniConfig.isSingleOcc ? '4.1.0' : '3.5.1'

// * 后端当前版本号
export const REQUEST_VERSION = '9.9.0'

// * 高德地区key
export const MAP_KEY = '20f12aae660c04de86f993d3eff590a0'

// * 新版本小程序标识，接口每次需要携带
export const versionmini = 1

// * 小程序加密密钥
export const nonceKey = (ENV_DEVELOPMENT_KEY === 'DEVELOP' || ENV_DEVELOPMENT_KEY === 'DEV')
  ? 'ZYEr8cHa4U4Lq2f70Ccjl9VFmbbmCTDA'
  : 'TJViHTOMPexr7OG0W8qhStZy42H7Fikw'

// * 火山SDK APP_ID
export const FINDER_APPLICATION_ID = REPORT_APP_ID_LIST[ENV_DEVELOPMENT_KEY]

// * 水印相机使用的环境（微信：ENV_IS_WEAPP，百度：ENV_IS_SWAN ）
export const SHOW_WATERMARK = ENV_IS_WEAPP

// * 设备验APPID
export const GUARD_APP_ID = GUARD_APP_ID_LIST[ENV_DEVELOPMENT_KEY]

/** 隐私协议 */
const agreement_privacy = 'https://h5hybridprod.yupaowang.com/article-out?id=1601060983336411219'
/** 服务协议 */
const agreement_service = 'https://h5hybridprod.yupaowang.com/article-out?id=1643075036946485318'
export const getAgreementUrl = (type: string) => encodeURIComponent((type === 'user' ? agreement_service : agreement_privacy))

/**
 * @name 找活列表筛选最大可选工种数量
 */
export const RESUME_OCC_FILTER_MAX_SELECT_NUM = 3

/** B端会员图标 */
export const VIP_ICON_B = 'https://cdn.yupaowang.com/yp_mini/images/jl/yp-mini-vip-b.png'
/** C端会员图标 */
export const VIP_ICON_C = 'https://cdn.yupaowang.com/yp_mini/images/jl/yp-mini-vip-c.png'

/** 埋点加密key */
export const REPORT_ENCRYPT_KEY = ['DEVELOP', 'DEV2', 'DEV'].includes(ENV_DEVELOPMENT_KEY) ? 'SF0CnPJFI/d9QtMejRYIlZ8ZrHBUdWKYMdKl3lH1uts=' : 'm814LuAycbx7jya9mTN89R7Jgp+rT2E1sQnEZz6ho9k='
/** 埋点加密iv */
export const REPORT_ENCRYPT_IV = ['DEVELOP', 'DEV2', 'DEV'].includes(ENV_DEVELOPMENT_KEY) ? 'sVR2zn37coeFmiqeRxEwIA==' : 'jAKXmO0pLXCWOlnVezX5Lw=='
