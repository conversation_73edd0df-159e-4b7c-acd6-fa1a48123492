{"component": true, "usingComponents": {"m-stripes": "/components/base/m-stripes/index", "submit-rule-tips": "../submit-rule-tips/index", "sensitive-textarea": "/subpackage/recruit/components/sensitive-textarea/index", "m-form-city-choose": "/components/base/m-form/components/m-form-city-choose/index", "m-form": "/components/base/m-form/index", "m-form-classify-choose": "/components/base/m-form/components/m-form-classify-choose/index", "verification-code": "/subpackage/components/index/widget/verification-code/index", "logo-footer": "/components/base/logo-footer/index", "drawer": "/components/base/drawer/index", "ripple-btn": "/subpackage/components/index/base/feedback-btn/ripple/index", "hiring-client-select": "/subpackage/recruit/components/hiring-client-select/index", "city-promote": "/subpackage/recruit/components/city-promote/index", "promote-tips": "/subpackage/recruit/components/promote-tips/index"}, "componentPlaceholder": {"verification-code": "view", "ripple-btn": "view"}}