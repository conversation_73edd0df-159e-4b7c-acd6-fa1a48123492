<view class="header" style="height:{{paddingTop + height + 8}}px;padding-top:{{paddingTop - 8}}px">
    <view class="htab-v">
        <block wx:for="{{hTabs}}" wx:key="id">
            <view catch:tap="onHtabChange" data-item="{{item}}" class="h-tab {{hTabSlted == item.id ? 'h-tab-selected' : ''}}" data-type="chat">
                {{item.name}}
                <badge wx:if="{{isLogin && item.id == 'chat' && msgBadge > 0}}" value="{{msgBadge}}" type="{{redDotObjs.msg_chat.badgeType}}" top="-4rpx" right="-12rpx" />
                <badge wx:if="{{isLogin && item.id == 'inter' &&  redDotObjs[role==1?'interaction_b':'interaction_c'].show}}" value="{{redDotObjs[role==1?'interaction_b':'interaction_c'].number}}" type="{{redDotObjs[role==1?'interaction_b':'interaction_c'].badgeType}}" top="-4rpx" right="-12rpx" />
            </view>
        </block>
    </view>
    <view wx:if="{{width && isLogin}}" catch:tap="onMsgSetShow" style="margin-right: {{width  + 24}}px">
        <icon-font type="yp-im_list_set" size="48rpx" color="rgba(0, 0, 0, 0.85)" />
    </view>
</view>
<view id="header-v" class="header-v" style="height:{{paddingTop + height + 8}}px"></view>
<view wx:if="{{isLogin && hTabSlted == 'inter'}}" class="c-tabs-inter" style="top:{{paddingTop + height + 8}}px" id='c-tabs'>
    <view class="tab-left">
        <block wx:for="{{interTabs}}" wx:key="id">
            <view catch:tap="onCinterChange" data-item="{{item}}" class="inter-tab {{item.id == cinter ? 'inter-tab-slted' : ''}}">
                <text>{{item.name}}</text>
                <badge wx:if="{{ isLogin && item.id == 'LOOK_ME' &&  redDotObjs[role==1?'browse_me_b':'browse_me_c'].show}}" value="{{redDotObjs[role==1?'browse_me_b':'browse_me_c'].number}}" type="{{redDotObjs[role==1?'browse_me_b':'browse_me_c'].badgeType}}" top="-4rpx" right="-14rpx" />
                <badge wx:if="{{ isLogin && item.id == 'EVALUATE' &&  redDotObjs[role==1?'not_comment_b':'not_comment_c'].show}}" value="{{redDotObjs[role==1?'not_comment_b':'not_comment_c'].number}}" type="{{redDotObjs[role==1?'not_comment_b':'not_comment_c'].badgeType}}" top="-4rpx" right="-14rpx" />
            </view>
        </block>
    </view>
    <view class="tab-right"></view>
</view>
<block wx:if="{{isLogin && hTabSlted == 'chat'}}">
    <batch-follow-chat-card id="batchFollowChatCard"  wx:if="{{role == 1}}" bind:bfccardchange="onBfcCardChange"/>
    <view class="c-tabs" id='c-tabs'>
        <scroll-view scroll-x class="c-tabs-scroll">
            <view class="tab-left tab-height">
                <block wx:for="{{chatTabs}}" wx:key="id">
                    <view catch:tap="onCtabChange" data-item="{{item}}" class="tab {{item.id=='ALL' ? 'ml-32' : ''}} {{item.numKy ? 'tab-v' : ''}} {{item.children.length > 0 ? 'tab-icon' : ''}}">
                        <view class="txt-v {{item.id == cTabSlted ? 'txt-tab-black' : ''}} {{item.children.length > 0 && item.id == cTabSlted ? 'txt-tab-slted' : ''}}">
                            {{item.name}}
                        </view>
                        <!-- <view wx:if="{{item.numKy}}" class="tab-num">
                            99+
                        </view> -->
                        <view wx:if="{{item.numKy && msgNumObj[item.numKy] > 0}}" class="tab-num">
                            {{msgNumObj[item.numKy] > 99 ? '99+' : msgNumObj[item.numKy]}}
                        </view>
                        <view wx:if="{{item.id == cTabSlted && !item.children}}" class="c-tab-selected"></view>
                        <icon-font wx:if="{{item.children.length > 0}}" type="yp-chat-jt-bak" size="8rpx" custom-class="chatjt-icon" color="{{item.id == cTabSlted ? 'rgba(0, 146, 255, 1)' : 'rgba(0, 0, 0, 0.65)'}}" />
                    </view>
                </block>
            </view>
        </scroll-view>
        <view class="tab-right" bind:tap="onClearMsgCount">
            <view class="clear">清除未读</view>
            <icon-font type="yp-chat-clear" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
        </view>
    </view>
</block>
<view wx:if="{{isTabPopShow}}" catch:touchmove="onDisableMove" class="tab-pop" style="height:calc(100vh - {{cHeaderHeight + ctabsHeight}}px)" catch:tap="onPopTabClose">
    <view class="pop-content" catch:tap="onDisableClick">
        <view class="pop-title">消息类型</view>
        <view class="pop-tabs">
            <block wx:for="{{popTabsChildren}}" wx:key="id">
                <view catch:tap="onPoptabChange" data-item="{{item}}" class="pop-tab {{item.id == popTabSlted ? 'pop-tab-slted' : ''}}" catch:tap="">
                    {{role == 2?(item.lbname || item.name) : item.name}}
                </view>
            </block>
        </view>
    </view>
</view>
<msg-set visible="{{msgSetVisible}}" catch:close="onMsgSetClose"/>
