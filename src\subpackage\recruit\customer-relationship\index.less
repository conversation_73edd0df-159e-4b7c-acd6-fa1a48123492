/* 发布招工-选择公司页面样式 */
.customer-relationship-page {
  min-height: 100vh;
  background-color: #fff;

  .page-content {
    padding: 0 32rpx;
    padding-bottom: 120rpx; // 为底部按钮留出空间

    .page-title {
      padding: 32rpx 0 24rpx 0;

      .title-text {
        font-size: 50rpx;
        font-weight: 600;
        color: #333;
        line-height: 56rpx;
      }
    }

    .page-desc {
      padding-bottom: 32rpx;

      .desc-text {
        font-size: 26rpx;
        line-height: 40rpx;
        color: #666;
      }
    }

    .company-list-container {
      .company-scroll-view {
        height: calc(100vh - 300rpx);
        background-color: #fff;
        border-radius: 16rpx;
        overflow: hidden;

        .company-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx 0rpx;
          border-bottom: 1rpx solid rgba(233, 237, 243, 1);
          background-color: #fff;
          transition: background-color 0.2s;
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &:active {
            background-color: #f8f8f8;
          }

          .company-info {
            flex: 1;
            margin-right: 64rpx;

            .company-content {
              .company-text-wrapper {
                font-size: 34rpx;
                font-weight: 500;
                color: #333;
                line-height: 44rpx;
                max-height: 88rpx; // 两行的高度
                overflow: hidden;
                word-break: break-all;

                .company-name {
                  font-size: 34rpx;
                  font-weight: 500;
                  color: #333;
                  line-height: 44rpx;
                }

                .tag {
                  display: inline;
                  padding: 4rpx 12rpx;
                  border-radius: 8rpx;
                  font-size: 20rpx;
                  line-height: 32rpx;
                  margin-left: 8rpx;
                  white-space: nowrap;
                  vertical-align: middle;
                  border: 1rpx solid rgba(0, 146, 255, 1);
                  

                  &.agent-tag {
                    background-color: rgba(224, 243, 255, 1);
                    color: #0099ff;
                  }

                  &.outsource-tag {
                    background-color: rgba(224, 243, 255, 1);
                    color: #1890ff;
                  }
                }
              }
            }
          }
          .select-icon {
            position: absolute;
            right: 0rpx;
            top: 36rpx;
            width: 40rpx;
            height: 40rpx;
          }
        }

        .loading-more {
          padding: 32rpx;
          text-align: center;

          .loading-text {
            font-size: 28rpx;
            color: #999;
          }
        }

        .no-more {
          padding: 32rpx;
          text-align: center;

          .no-more-text {
            font-size: 28rpx;
            color: #999;
          }
        }
      }
    }

    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;
      text-align: center;

      .no-data-text {
        margin-top: 24rpx;
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.45);
        text-align: center;
      }

      .custom-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 200rpx;
        height: 200rpx;
      }
      
    }


  }
}


.add-company-btn {
  margin-top: 24rpx;
  border-radius: 16rpx;
  width: 200rpx;
  height: 80rpx;
  border-radius: 16rpx !important;
  font-size: 30rpx;
}
