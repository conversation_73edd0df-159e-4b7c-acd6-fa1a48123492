Component(
  class HiringClientSelect extends wx.$.Component<HiringClientSelect> {
    externalClasses = [
      'custom-container',
      'custom-title',
      'custom-content',
      'custom-suffix',
      'custom-text',
    ]

    properties = {
      title: {
        type: String,
        value: '公司',
      },
      placeholder: {
        type: String,
        value: '请选择职位所属公司',
      },
      value: {
        type: Object,
        value: {} as HiringClient,
      },
      disabled: {
        type: Boolean,
        value: false,
      },
    }

    async handleSelect() {
      if (!this.data.disabled) {
        await wx.$.u.waitAsync(this, this.handleSelect, [], 1000)
        wx.$.nav.push(
          '/subpackage/recruit/customer-relationship/index',
          {},
          (evt: { hiringClient?: HiringClient }) => {
            if (evt && evt.hiringClient && evt.hiringClient.hiringClientId) {
              this.setData({
                value: evt.hiringClient,
              })
              this.triggerEvent('change', evt.hiringClient)
            }
          },
          { hiringClient: this.data.value },
        )
      }
    }
  },
)

/**
 * 跳转页面传参hiringClient
 */
type HiringClient = {
  /** 招聘模式
   * 枚举备注: DIRECT_HIRING :自招 AGENCY_HIRING :代招 HEADHUNTING :猎头
   */
  hiringMode: 'DIRECT_HIRING' | 'AGENCY_HIRING' | 'HEADHUNTING';
  /** 公司ID */
  hiringClientId: string;
  /** 公司名称 */
  hiringClientName: string;
};
