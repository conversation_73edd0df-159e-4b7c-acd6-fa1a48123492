/*
 * @Date: 2024-10-15 09:34:22
 * @Description: 管理招工
 */

import resource from '@/components/behaviors/resource'
import { actions, dispatch, storage, store } from '@/store/index'
import { getListData, getItemData, prevCheckDraft, fetchRepublish, fetchRepublishCheck } from './utils'
import { dealDialog, dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { LiteralText } from '../fast_issue/index/type'
import { streamerModelArr } from '@/utils/helper/pop-list/index'
import { helper } from '@/utils/index'
import { rpxToPx } from '@/utils/tools/common/index'
import { resourceJump } from '@/utils/helper/common/index'

const systemInfo = wx.$.u.sInfo()

Page(class extends wx.$.Page {
  behaviors = !ENV_IS_SWAN ? [resource] : undefined

  data = {
    nearby: wx.$.u.getRandomNumber(150000, 180000),
    systemInfo,
    /** 路由参数 */
    query: {
      /** job_id：招工置顶时给置顶页面带入的招工id */
      job_id: '',
      /** defaultTopArea: 招工置顶时携带的城市id,供发布招工跳转至招工置顶页面使用，默认选中的城市 */
      defaultTopArea: '',
      /** 可选值：member_first，招工置顶页面重定向回页面时携带的参数，供当前页面返回到会员中心使用 */
      tipType: '',
      /** classify_id: 招工置顶时携带的工种id，供发布招工跳转至招工置顶页面使用，多个以逗号隔开 */
      classify_id: '',
      /** 是否是用户第一次发布招工，0代表不是，1代表是 */
      member_first: '',
      /** 默认选中tab */
      activeTab: '',
    },
    /** 当前选中的tab */
    activeTab: 'allV2',
    /** 菜单数组-列表类型: all=全部, recruiting=正在招, auditing=审核中, auditFail=未通过, stopRecruiting=已招满 */
    tabs: [
      { id: 'allV2', name: '全部', label: '全部', buriedPointName: '管理招工_全部列表' },
      { id: 'recruiting', name: '正在招', label: '正在招', buriedPointName: '管理招工_正在招列表' },
      { id: 'waiting', name: '待开放', label: '待开放', buriedPointName: '管理招工_待开放列表' },
      { id: 'auditFail', name: '未通过', label: '未通过', buriedPointName: '管理招工_未通过列表' },
      { id: 'stopRecruiting', name: '已招满', label: '已招满', buriedPointName: '管理招工_已招满列表' },
    ],
    /** 分页控制器 */
    pages: {
      index: 1, // 当前分页
      pageSize: 15, // 每页条数
      loading: true, // 加载状态
      bottomLoadingType: '', // 底部加载中和没有更多的状态
    },
    /** 列表数据存储 */
    list: [],
    isList: false, // 是否有数据
    listDone: false, // 列表是否加载完毕
    /** 弹出框的传入的数据 */
    popData: {},
    visiblePop: '',
    /** 竞招缓存的招工卡片序号，刷新单条数据用 */
    jobVieTargetIndex: '',
    /** 竞招发布的卡片类型 ’draft‘, 'card' */
    jobVieType: '',
    /** 竞招缓存的招工卡片信息， 购买竞招会员后回到页面直接重新发布 */
    jobVieTarget: undefined,
    /** tipTop显隐控制器 */
    showTipTop: false,
    /** 谁联系过我按钮 */
    showContactBtn: false,
    /** 页面红点数据 */
    redDotObjs: {},
    /** 资源位标识对应的数据key */
    resourceKeys: {
      float_recruit_published_under: 'buoyUnder',
      float_recruit_published_top: 'buoyTop',
      news_findworker_lefttop: 'news_findworker_lefttop', // 招工提示
      icon_findworker_top: 'icon_findworker_top', // 金刚区
      icon_findworker_lefttop: 'leftTopIcon', // 顶部左上角的图标
    },
    /** 顶部左上角的图标 */
    leftTopIcon: {},
    /** 资源位——浮标上 */
    buoyTop: null,
    /** 资源位——浮标下 */
    buoyUnder: null,
    /** 点击某条招工信息的数据 */
    clickData: {
      index: -1,
      jobId: '',
    },
    /** 管理招工弹窗， */
    showPublishWorkPop: false,
    /** 业务弹窗 */
    currDialog: {},
    /** 业务弹窗数据 */
    currDialogConfig: {},
    /** tabs吸顶距离 */
    navHeight: getNavHeight(),
    /** tabs栏背景颜色 */
    tabColor: 'transparent',
    /** 是否展示企业名称弹窗 */
    showBiznameDialog: false,
    /** 弹窗配置 */
    dialogData: {},
    /** 显示关注公众号弹窗 */
    showFollowModal: false,
    /** 客服400是否显示 */
    jobPointSwitch: false,
  }

  useStore(state: StoreRootState) {
    const { storage } = state
    return {
      hasIssue: storage.common.hasIssue,
      login: storage.userState.login,
    }
  }

  // 更新tabs数量
  updateTabCount() {
    if (!this.data.login) {
      this.setData({ listDone: true })
      return
    }
    wx.$.javafetch['POST/job/v3/manage/job/listPro/count/tab']().then(({ data }) => {
      const { tabs } = this.data
      tabs[0].name = `${tabs[0].label}${data.all > 99 ? '99+' : (data.all || '')}`
      tabs[1].name = `${tabs[1].label}${data.recruiting > 99 ? '99+' : (data.recruiting || '')}`
      tabs[2].name = `${tabs[2].label}${data.draft > 99 ? '99+' : (data.draft || '')}`
      tabs[3].name = `${tabs[3].label}${data.auditFail > 99 ? '99+' : (data.auditFail || '')}`
      tabs[4].name = `${tabs[4].label}${data.stopRecruiting > 99 ? '99+' : (data.stopRecruiting || '')}`
      this.setData({
        tabs: [...tabs],
        isList: Object.keys(data).reduce((sum, i) => sum + data[i], 0) > 0,
        listDone: true,
      })
    })
  }

  // 更新 hasIssue
  async updatePostAJobText() {
    if (!this.data.login) {
      return
    }
    const hasIssueResp = await wx.$.javafetch['POST/job/v2/manage/job/publish/check']({})
    const hasIssue = (hasIssueResp?.data?.published || hasIssueResp?.data?.draftedCount > 0) || false
    dispatch(actions.storageActions.setCommonItem({ hasIssue }))
  }

  onShow() {
    const { clickData, list, login, activeTab } = this.data as DataTypes<typeof this>
    wx.$.collectEvent.event('rnPageExposure', {
      page_name: 'my_find_worker',
    })
    this.getJoinGroupParams()
    /** 竞招页返回，直接发布 */
    const jobVieCheckType = storage.getItemSync('jobVieCheckType')
    if (jobVieCheckType && this.data.jobVieType == 'card') {
      storage.removeSync('jobVieCheckType')
      this.onResend({}, true)
      this.setData({ jobVieType: '' })
    }

    if (jobVieCheckType && this.data.jobVieType == 'draft') {
      storage.removeSync('jobVieCheckType')
      this.onPublishDraft(<any>{}, jobVieCheckType)
      this.setData({ jobVieType: '' })
    }
    storage.removeSync('jobVieCheckType')
    if (activeTab === 'allV2') {
      this.getData(true)
    } else if (clickData.jobId && clickData.index > -1 && login) {
      getItemData({ jobId: clickData.jobId }).then(res => {
        if (res.code == 0) {
          list[clickData.index] = res.data
          this.setData({ list })
        }
      })
      this.setData({
        clickData: { index: -1, jobId: '' },
      })
    } else {
      this.getData(false, { index: this.data.pages.index })
    }
    // this.getData(true)
    // this.getData(activeTab === 'waiting' ? true : false, { index: this.data.pages.index })
    this.updateTabCount()
    this.updatePostAJobText()
  }

  /** 根据滚动位置调整tabs的背景颜色 */
  onPageScroll(options) {
    this.setData({
      navColor: options.scrollTop > this.data.navHeight + rpxToPx(154) ? 'linear-gradient(to bottom, rgb(244,247,252), rgb(255,255,255))' : '#F5F6FA',
    })
  }

  onLoad(options: any = {}) {
    wx.$.collectEvent.event('rnManageRecruitmentExposure', { page_name: '管理招工_全部列表' })
    /**
     * 目前job_id, defaultAreaId，只有发布招工页面传递过来
     */
    const { job_id, defaultTopArea, classify_id, tipType, is_pop, activeTab = 'allV2' } = options
    this.setData({
      activeTab,
      query: {
        job_id,
        defaultTopArea,
        tipType,
        classify_id,
        is_pop,
        activeTab,
      },
    })
    wx.$.javafetch['POST/crm/v1/CustomerSupport/config']().then((res) => {
      this.setData({
        jobPointSwitch: res.data.jobPointSwitch,
      })
    })
    /** 获取聚合弹窗 */
    this.aggregation()
  }

  /** 发布职位 */
  onJumpFastIssue() {
    // 埋点
    wx.$.collectEvent.event('rnPageClickButton', {
      page_name: 'my_find_worker',
      click_button: '发布职位',
    })
    wx.$.r.push({ path: '/subpackage/recruit/fast_issue/index/index' })
  }

  /** 聚合弹窗 */
  async aggregation() {
    const result = await dealDialog('my_find_worker')
    if (!result) {
      return
    }
    const { currDialog, dialogKey, dialog_identify, currDialogConfig } = result
    const currentPage = wx.$.r.getCurrentPage()
    if (currentPage.route !== 'subpackage/recruit/published/index') {
      return
    }
    if (streamerModelArr.includes(dialog_identify)) {
      const { logicTransferData } = currDialog || {}
      wx.$.model.streamerModel({ isQueue: true, dialogKey: dialog_identify, logicTransferData })
      return
    }
    /** 智能推荐弹窗 */
    if (dialog_identify === 'zhinengtuijiangangwei') {
      this.setData({ currDialog, showRecommendPop: true })
      return
    }

    /** 管理招工弹窗 */
    if (dialog_identify === 'refresh_job_new1') {
      this.setData({ currDialog, showContinuePop: true })
      return
    }

    /** 关注公众号弹窗 */
    if (dialog_identify === 'Guide_blue_follow_gzh' || dialog_identify == 'Guide_white_follow_gzh') {
      this.setData({ currDialog, showFollowModal: true })
      return
    }

    /** 发布招工弹窗 */
    if (['fast_job_mini', 'fast_job_mini1'].includes(dialog_identify)) {
      this.setData({ currDialog, showPublishWorkPop: true })
      return
    }
    /** 图片弹窗：管理职位私域 */
    if (dialog_identify === 'guanlizhiwei_siyu1' || dialog_identify === 'guanlizhiwei_siyu2' || dialog_identify === 'guanlizhiwei_zhizhao') {
      this.setData({ currDialogConfig, currDialog, showImageModal: true })
      return
    }
    if (dialogKey) {
      // 通用弹框
      wx.$.showModal({
        ...result,
      })
    }
  }

  onTapCard(e) {
    const { index, item } = e.currentTarget.dataset
    this.setData({
      clickData: { index, jobId: item.jobId },
    })
  }

  onTapDraftCard(e) {
    wx.$.nav.push(
      '/subpackage/recruit/my-wait-detail/index',
      {
        id: e.detail.draftId,
        urlType: 'waitOpen',
      },
      () => {
        this.onPullDownRefresh()
      },
    )
  }

  /** 下拉刷新 */
  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 200)
  }

  /** 上啦加载更多 */
  onReachBottom() {
    this.getData(false)
  }

  /** 监听顶部返回事件 */
  onBack() {
    /** 跳转至会员中心页面 */
    const pages = getCurrentPages()
    /** 只有一个页面栈的时候返回会员中心 */
    if (pages.length == 1) {
      const isSourcePath = store.dispatch(actions.otherActions.goToSourcePath())
      if (!isSourcePath) {
        wx.$.r.reLaunch({ path: '/pages/ucenter/index' })
      }
    } else {
      /** 执行正常返回流程 */
      wx.$.r.back()
    }
  }

  onUpgrade(e) {
    const { item, index } = e.detail
    const { jobId } = item || {}
    if (jobId) {
      this.setData({ clickData: { index, jobId } })
    }
  }

  /** 默认进入时tabs会自动触发一次 */
  onTabChange({ detail: { item: { id }, item } }) {
    this.setData({ activeTab: id, clickData: { ...(this.data.clickData || {}), index: -1 } })
    this.getData(true)
    this.updateTabCount()
    // 管理招工曝光上报
    const page_name = `管理招工_${item.label}列表`
    wx.$.collectEvent.event('rnManageRecruitmentExposure', { page_name })
  }

  /** 待开放数据捞回 跳转到全部tab
   * @warn 此方法在发布草稿时 通过 curPage.jumpToTotal() 调用, 修改时请注意影响点位
   */
  async jumpToTotal() {
    await wx.$.u.waitAsync(this, this.jumpToTotal, [], 500)
    if (this.data.tabs && this.data.tabs[0]) {
      this.onTabChange({ detail: { item: this.data.tabs[0] } })
    }
  }

  /** 供外部方法调用, 用于配置activeTab值和初始化拉取数据 */
  getActiveData(type) {
    type && this.setData({ activeTab: type })
    this.getData(true)
  }

  /** 刷新列表方法 */
  refreshData() {
    this.getData(true)
    this.updateTabCount()
  }

  /** 更新单条数据 */
  onRefreshItem(e) {
    const { item, index } = e.currentTarget.dataset
    const { list } = this.data
    getItemData({ jobId: item.jobId }).then(res => {
      if (res.code == 0) {
        list[index] = res.data
        this.setData({ list })
      }
    })
  }

  /**
   * @description 获取列表数据
   * @param init 代表是否初始化分页数据，默认: false
   */
  async getData(init = false, pages = {}, activeTab = '') {
    if (!this.data.login) {
      return
    }
    let newPages: any = {}
    if (init) {
      /** 初始化 */
      newPages = {
        ...this.data.pages,
        index: 1,
        loading: true,
        bottomLoadingType: '',
        ...pages,
      }
      this.setData({
        pages: newPages,
        list: [],
      })
    } else {
      /** 下拉加载更多数据 */
      /** 没有更多数据，则不执行后续函数 */
      if (this.data.pages.bottomLoadingType === 'finish' && pages.index !== this.data.pages.index) {
        return
      }
      /** 下一页自动加1 */
      newPages = {
        ...this.data.pages,
        index: this.data.pages.index + 1,
        bottomLoadingType: 'loading',
        ...pages,
      }
      this.setData({
        pages: newPages,
      })
    }

    /** 生成请求参数 */
    const params = {
      pageSize: newPages.pageSize || 15,
      currentPage: newPages.index,
      type: activeTab || this.data.activeTab,
    }

    /** 进行数据请求 */
    getListData(params).then((resData: any) => {
      const { paginator = {} } = resData
      /** fix: 弱网情况下会造成数据错乱显示的问题, 进行逻辑中止操作 */
      if (params.type !== this.data.activeTab) {
        return
      }
      const list = this.data.list.concat(resData.data).filter((item, j, arr) => {
        if (item.jobId) {
          return arr.findIndex((i) => i.jobId === item.jobId) === j
        }
        if (item.draftId) {
          return arr.findIndex((i) => i.draftId === item.draftId) === j
        }
        return true
      })
      let bottomLoadingType = ''
      if (paginator.totalRecord <= paginator.currentPage * paginator.pageSize) {
        bottomLoadingType = 'finish'
      }
      this.setData({
        list,
        // list: list.map((i) => ({ ...i, vieBanner: { showBanner: true, bannerType: 12, bannerContent: '体验版x天后到期，升级正式版享更多权益' } })),
        pages: {
          ...newPages,
          loading: false,
          bottomLoadingType,
        },
        showContactBtn: list.length > 0,
      })
    })
  }

  /** 关闭弹窗 */
  onClose({ target }) {
    console.log('onClose', target)
    const visibleKey = target.dataset && target.dataset.visible
    this.setData({
      currDialog: {},
      ...(visibleKey ? { [visibleKey]: false } : {}),
    })
  }

  handleBizDialogHidden() {
    this.setData({
      showBiznameDialog: false,
    })
  }

  async onPublishDraft({ target }: IPublishDraftEvent, jobVieCheckType = '') {
    await wx.$.u.waitAsync(this, this.onPublishDraft, [{ target }, jobVieCheckType], 500)
    let draftId
    let item
    const ignore = []
    if (jobVieCheckType) {
      item = this.data.jobVieTarget
      draftId = item.draftId
      ignore.push('wjzqytc')
    } else {
      const { dataset } = target
      draftId = dataset.draftId
      item = dataset.item
    }

    /** 发布草稿箱，前置接口，用于校验必填项未填的逻辑 */
    const { data } = await wx.$.javafetch['POST/job/v3/manage/draft/isAbsentRequiredComplete']({ draftId, completeV3SourceFlag: true })
    if (data && data.absentRequiredComplete) {
      await wx.$.msg(data.absentRequiredCompleteTip)
      wx.$.r.push({
        path: '/subpackage/recruit/edit-draft/index',
        query: { draftId },
      })
      return
    }

    this.setData({
      jobVieTarget: item,
      jobVieType: jobVieCheckType ? '' : 'draft',
    })

    let occ_v2 = []
    let area_id = ''
    try {
      const { occV2, areaId } = JSON.parse(item.ext)
      occ_v2 = occV2
      area_id = areaId
      if (!Array.isArray(occV2)) throw new Error('JSON反序列化失败')
    } catch (e) {
      occ_v2 = []
      console.error('数据异常')
      /** noop */
    }
    const extra = <any>{}

    if (jobVieCheckType == 'COMMON') {
      extra.publishType = 1
    }

    const classify_id = await wx.$.l.transformOccV2ToHidClsId(occ_v2)
    prevCheckDraft(draftId, { classify_id, area_id, ignore, extra }, (response) => {
      const jobId = wx.$.u.getObjVal(response, 'data.data.id')
      const toComplete = wx.$.u.getObjVal(response, 'data.data.toComplete', false)
      const toCompleteOcc = wx.$.u.getObjVal(response, 'data.data.toCompleteOcc', [])
      if (toComplete) {
        wx.$.msg('发布成功')
        wx.$.l.jumpToComplete({
          occIds: toCompleteOcc,
          isPublish: false,
          origin: 'fast_issue',
          jobId,
        }, {})
        return
      }
      if (jobId) wx.$.l.existJobTopSet(jobId, {
        jumpMethod: 'push',
        pageFrom: 'publish',
      })
    }).catch(async (error) => {
      /** exception */
      console.error('Exception', error)
      /** 如果抛出错误是个对象，且存在dialogIdentify字符串包含在特殊弹窗标识列表中 */
      if (error && ['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(error.dialogIdentify)) {
        // TODO 获取弹窗配置，唤起弹窗
        const popup = await dealDialogRepByApi(error.dialogIdentify)
        if (popup) {
          /** 弹出弹窗 */
          this.setData({
            showBiznameDialog: true,
            dialogData: {
              ...popup,
              ...error,
            },
          })
        }
      }
    })
  }

  /** 获取加群组件的请求参数 */
  async getJoinGroupParams() {
    const areaId = helper.location.getLocalCity('resume')?.id
    const { province } = await wx.$.l.getAreaById(areaId)
    const params = {
      provinceName: province.name,
    }
    setTimeout(() => {
      this.setData({ joinGroupParams: params })
    }, 100)
  }

  /** 重新发布 */
  async onResend(event: any, jobVieCheckType = false) {
    const currentJob = jobVieCheckType ? this.data.jobVieTarget : wx.$.u.getObjVal(event, 'currentTarget.dataset.item', '')
    const index = jobVieCheckType ? this.data.jobVieTargetIndex : wx.$.u.getObjVal(event, 'currentTarget.dataset.index', '')
    const ignore = []
    if (jobVieCheckType) {
      ignore.push('wjzqytc')
    }
    if (currentJob.jobId) {
      this.setData({ jobVieTarget: currentJob, jobVieTargetIndex: index, item: currentJob, jobVieType: jobVieCheckType ? '' : 'card' })
      const checkRes = await fetchRepublishCheck.call(this, { jobId: currentJob.jobId }, { ignore })
      if (!checkRes || checkRes.code != 0) return
      const { data } = checkRes.data
      fetchRepublish.call(this, { jobId: currentJob.jobId, free: data.free, republishCheckId: data.republishCheckId }, { ignore }).then(async res => {
        if (res && res.code == 0) {
          this.refreshData()
        }
      })
    }
  }

  /** 点击图标 */
  onClickIcon() {
    const item = this.data.leftTopIcon?.list[0]
    if (item) {
      resourceJump(item)
    }
  }
})

function getNavHeight() {
  const { menuRect: m, menuPadding } = wx.$.u.sInfo()
  return m.top + m.height + menuPadding - 1
}

type IPublishDraftEvent = {
  target: IPublishDraftEventTarget,
  currentTarget: IPublishDraftEvent
} & Record<string, any>

type IPublishDraftEventTarget = {
  dataset: {
    draftId: LiteralText,
    index: number,
    item: {
      draftId: number,
      detail: string,
      draftReason: string,
      ext: string
    }
  }
}
