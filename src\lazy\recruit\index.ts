/*
 * @Date: 2024-12-11 09:52:32
 * @Description: 招工相关
 */

import { storage, store } from '@/store/index'
import dayjs from '@/lib/dayjs/index'

/** 判断是薪资选择器 */
const salaryCode = ['1404', '8001', '8002', '8003', '8004']

/* *除了【薪资】、【人数】、【最低学历】，其他配置的选填项字段，都收到职位关键词页面中 */
export const currentPerfectItems = [...salaryCode, '1402', '1415']

/** 转换整个模板数据 */
export function transformTemplateInfo(list) {
  return list.map((item) => {
    return {
      ...item,
      templateInfo: {
        ...item.templateInfo,
        controlInfoList: transformControlInfoList(item.templateInfo.controlInfoList).allControlInfoList,
      },
    }
  })
}

/** 转换完善模板数据 */
function transformControlInfoListV3(controlInfoList) {
  // 获取输入框配置
  function getInputConfig(record) {
    if (record?.controlTypeCode == 'INPUT_STRING') {
      const dataList = wx.$.u.getObjVal(record, 'controlNatureList.0.controlAttrList.0.dataList', [])
      return dataList.reduce((obj, item) => ({ ...obj, [item.code]: item.value }), { controlCode: record.controlCode })
    }
    return null
  }

  function getControlAttrObj(record) {
    const controlAttrList = wx.$.u.getObjVal(record, 'controlNatureList.0.controlAttrList', [])
    const labelList = wx.$.u.getObjVal(controlAttrList, '0.labelList', []).sort((a, b) => a.orderAsc - b.orderAsc)
    const dataList = wx.$.u.getObjVal(controlAttrList, '0.dataList', [])
    // 是否多选
    const ifMulti = dataList ? dataList.find((item) => item.code == 'ifMulti')?.value == 'true' : false
    // 多选限制
    const limitNum = dataList.find((item) => item.code == 'limitNum')?.value
    // 是否必填
    const currentDisplayScene = record.scenes?.find((i) => i.displayScene == 'JOB_FEES_DISPLAY')
    const required = currentDisplayScene?.ifMust || false
    // 获取输入框配置
    const inputConfig = getInputConfig(record)
    return {
      required,
      ifMulti,
      inputConfig,
      limitNum: Number(limitNum || 1),
      labelList: labelList.map((record) => {
        const r = record.controlConfigList?.[0]
        return {
          ...record,
          controlAttrObj: r ? getControlAttrObj(r) : null,
        }
      }),
    }
  }
  const list = controlInfoList.filter((item) => item.status == 1 && item.controlTypeCode == 'LABEL' && item.scenes.find((i) => i.displayScene == 'JOB_FEES_DISPLAY'))
  return list.map((record) => {
    return {
      ...record,
      controlAttrObj: getControlAttrObj(record),
    }
  })
}

function getIsCharge(item) {
  return !!item.scenes?.find((item) => item.displayScene == 'JOB_FEES_DISPLAY')
}

/** 转换完善模板数据 */
export function transformControlInfoList(controlInfoList) {
  // 发布和完善的必填判断key
  const requireKey = 'ifJobPerfectMust'
  const list = controlInfoList.filter((item) => {
    // 是收费项的数据
    const isCharge = getIsCharge(item)
    return item.status == 1 && (item.jobDisplayPage == 'jobPerfect' || isCharge)
  })
  const currentControlInfoList = []
  const otherControlInfoList = []
  const allControlInfoList = list.map((record) => {
    // 判断是不是薪资选择器
    const isSalary = salaryCode.includes(record.controlCode)
    const controlAttrList = wx.$.u.getObjVal(record, 'controlNatureList.0.controlAttrList', [])
    // 如果是薪资选择器
    if (isSalary) {
      return {
        ...record,
        isSalary,
        controlAttrObj: {
          required: record[requireKey],
          salaryOptions: controlAttrList.map((controlItem) => {
            const obj = controlItem.dataList.reduce((acc, item) => {
              return { ...acc, [item.code]: { name: item.name, value: item.value } }
            }, {})
            return {
              label: obj.unitName.value,
              value: obj.unitCode.value,
              config: obj,
              leftList: controlItem.leftList,
              rightList: controlItem.rightList,
            }
          }),
        },
      }
    }

    // 是收费项的数据
    const isCharge = getIsCharge(record)
    if (isCharge) {
      return record
    }

    const labelList = wx.$.u.getObjVal(controlAttrList, '0.labelList', [])
    const dataList = wx.$.u.getObjVal(controlAttrList, '0.dataList', [])
    // 是否多选
    const ifMulti = dataList ? dataList.find((item) => item.code == 'ifMulti').value == 'true' : false
    // 多选限制
    const limitNum = dataList.find((item) => item.code == 'limitNum')?.value
    return {
      ...record,
      isSalary,
      controlAttrObj: {
        required: record[requireKey],
        ifMulti,
        limitNum: Number(limitNum || 1),
        labelList: labelList.sort((a, b) => a.orderAsc - b.orderAsc) || [],
      },
    }
  })
  // 过滤是当前的，还是职位结构化的页面
  allControlInfoList.forEach((item) => {
    if (currentPerfectItems.includes(item.controlCode)) {
      currentControlInfoList.push(item)
    } else if (!getIsCharge(item)) {
      otherControlInfoList.push(item)
    }
  })
  return {
    allControlInfoList: allControlInfoList.sort((a, b) => a.orderAsc - b.orderAsc),
    currentControlInfoList: currentControlInfoList.sort((a, b) => a.orderAsc - b.orderAsc),
    otherControlInfoList: otherControlInfoList.sort((a, b) => a.orderAsc - b.orderAsc),
    chargesInfoList: transformControlInfoListV3(controlInfoList),
  }
}

// 收费项的数据转换
export const transformChargesForm = (occId, list, initValues) => {
  const items = initValues?.find((v) => v.occupationId == occId)?.items
  return (items && list?.reduce((obj, item) => {
    const current = items.find((i) => i.itemCode == item.controlCode)
    if (current) {
      const { controlAttrObj } = item
      const v = controlAttrObj.ifMulti ? current.itemValues.map((i) => i.valueCode) : current.itemValues[0].valueCode
      obj[item.controlCode] = {
        value: v,
        inputValue: current.itemValues.reduce((obj, item) => {
          const cv = item.cascadeItems.reduce((o, i) => {
            return { ...o, [i.itemCode]: i.itemValues[0]?.attachedValue || '' }
          }, {})
          return { ...obj, [item.valueCode]: cv }
        }, {}),
      }
    }
    return obj
  }, {})) || {}
}

/** 完善遍历选中的值 */
export function getImproveRecruitmentInfo(list, initValues?: any) {
  // 获取label
  const getLabel = (perfectInfo, item) => {
    const { templateInfo: { controlInfoList } } = item
    function getStr(values) {
      const keys = Object.keys(values || {})
      return keys.reduce((arr, key) => {
        const current = controlInfoList.find(r => r.controlCode == key)
        if (!current) {
          return arr
        }
        if (Array.isArray(values[key])) {
          values[key].forEach((v) => {
            const name = current.controlAttrObj.labelList?.find((i) => i.code == v)?.name
            arr.push(name)
          })
        } else {
          // 如果是薪资选择器
          // eslint-disable-next-line no-lonely-if
          if (values[key]?.tab) {
            const { salaryOptions } = current.controlAttrObj
            const unitName = salaryOptions.find((s) => s.value == values[key].tab)?.label
            arr.push(values[key]?.tab === 'negotiable' ? '面议' : `${values[key].one || ''}${values[key].two ? '-' : ''}${values[key].two || ''}${unitName || ''}`)
          } else {
            const name = current.controlAttrObj.labelList?.find((i) => i.code == values[key])?.name
            arr.push(name)
          }
        }
        return arr
      }, []).filter(Boolean).join('、')
    }
    return [getStr(perfectInfo?.values), getStr(perfectInfo?.keyWordsValue)].filter(Boolean).join('、')
  }

  // 转换后端的数据为前端表单需要的数据
  const transformForm = (occId, list) => {
    const items = initValues?.find((v) => v.occupationId == occId)?.items
    return (items && list?.reduce((obj, item) => {
      const current = items.find((i) => i.itemCode == item.controlCode)
      if (current) {
        const { isSalary, controlAttrObj } = item
        if (isSalary) {
          const tab = current.itemValues[0].valueCode
          const [one, two] = current.itemValues[0].attachedValue?.split('-') || []
          // 说明有选项卡值
          const tabObj = item.controlAttrObj.salaryOptions?.find((i) => i.value == tab)
          const isOne = tabObj.leftList?.find((i) => i.code == one)
          const isTwo = tabObj.rightList?.find((i) => i.code == two)
          // 有命中的数据才设置进去
          if (tab == 'negotiable') {
            obj[item.controlCode] = {
              tab,
              one: undefined,
              two: undefined,
            }
          } else if (tabObj && isOne && isTwo) {
            obj[item.controlCode] = {
              tab,
              one,
              two,
            }
          }
        } else {
          // 如果是多选
          // eslint-disable-next-line no-lonely-if
          if (controlAttrObj.ifMulti) {
            obj[item.controlCode] = current.itemValues.map((i) => i.valueCode)
          } else {
            obj[item.controlCode] = current.itemValues[0].valueCode
          }
        }
      }
      return obj
    }, {})) || {}
  }

  /** 根据列表获取选中的值 */
  const getFilterValues = (list, values) => {
    return list.map((item) => values?.[item.controlCode]).filter((item) => (Array.isArray(item) ? item.length : item))
  }

  return list.map((item) => {
    const { templateInfo } = item
    // 完善页可用的数据
    const { currentControlInfoList, otherControlInfoList, chargesInfoList } = transformControlInfoList(templateInfo.controlInfoList)
    const perfectInfo = item.perfectInfo || {
      values: transformForm(item.occId, currentControlInfoList),
      keyWordsValue: transformForm(item.occId, otherControlInfoList),
      chargesValue: transformChargesForm(item.occId, chargesInfoList, initValues), // 收费项选中的值
    }
    // 完善的必填有哪些
    const currentControlInfoListRequire = currentControlInfoList.filter((i) => i.controlAttrObj.required)
    // 完善必填已经填的值
    const currentControlInfoValues = getFilterValues(currentControlInfoListRequire, perfectInfo.values)
    // 完善不必填已经填的值
    const currentControlInfoListNORequire = currentControlInfoList.filter((i) => !i.controlAttrObj.required)
    // 完善不必填已经填的值
    const currentControlInfoNOValues = getFilterValues(currentControlInfoListNORequire, perfectInfo.values)
    // 职位关键词是否必填
    const otherControlInfoListRequireList = otherControlInfoList.filter((i) => i.controlAttrObj.required)
    // 职位关键词填了多少
    const otherControlInfoListRequireValuesLength = getFilterValues(otherControlInfoListRequireList, perfectInfo.keyWordsValue).length
    // 收费项是否必填
    const chargesValueRequire = chargesInfoList.filter((i) => i.controlAttrObj.required)
    // 收费项填了多少
    const chargesValueLength = getFilterValues(chargesInfoList, perfectInfo.chargesValue).length ? 1 : 0
    // 收费项没有填，并且没有值，只算1项未完善
    // eslint-disable-next-line no-nested-ternary
    const chargesValueRequireLength = chargesValueRequire.length ? (chargesValueLength ? 1 : 0) : 0
    // 如果职位关键词必填，并且没有值，只算1项未完善
    // eslint-disable-next-line no-nested-ternary
    const l = otherControlInfoListRequireList.length ? ((otherControlInfoListRequireList.length - otherControlInfoListRequireValuesLength) == 0 ? 1 : 0) : 0
    // 如果职位关键词有，并且没有值，只算1项未完善
    const keyWordsLength = otherControlInfoList.length && Object.keys(perfectInfo.keyWordsValue).length === 0 ? 1 : 0
    // 如果收费项没有有，并且没有值，只算1项未完善
    const chargeLength = chargesInfoList.length && Object.keys(perfectInfo.chargesValue).length === 0 ? 1 : 0
    // 还要必须完善的数量
    const mustBePerfected = (chargesValueRequire.length ? 1 : 0) + currentControlInfoListRequire.length + (otherControlInfoListRequireList.length ? 1 : 0) - currentControlInfoValues.length - l - chargesValueRequireLength
    // 还需要选填完善的数量
    const perfectable = currentControlInfoListNORequire.length - currentControlInfoNOValues.length + keyWordsLength + chargeLength
    return {
      ...item,
      occName: item.templateInfo.name.replace('招聘偏好', '').replace('职位偏好', ''),
      showLabel: getLabel(perfectInfo, item),
      perfectable,
      mustBePerfected,
      perfectInfo,
    }
  })
}

// 判断首页页面自动刷新,如果存在就不进行弹框
export const autoRefresh = () => {
  const { _cacheExpiredSecond } = store.getState().index
  if (_cacheExpiredSecond && _cacheExpiredSecond > 0) {
    const now = new Date().getTime()
    if (now > _cacheExpiredSecond) {
      return true
    }
  }
  return false
}

/** 根据传入的状态判断需要跳转到那个招工详情页面，是我的还是别人的 */
export const getRecruitDetailsPath = (isMy = false) => {
  const { login } = store.getState().storage.userState
  const role = storage.getItemSync('userChooseRole')
  if (login && isMy && role == 1) {
    return '/subpackage/recruit/my_detail/index'
  }
  return '/subpackage/recruit/details/index'
}

/** 预加载招工详情 */
export const preJobInfo = ({ occIds, id, list_time, source_id }) => {
  /** 判断是时间戳还是日期值 */
  const isTimestampOrDate = (value) => {
    // 正则匹配 是否value是0-9的数字
    const numberReg = /^[0-9]*$/
    if (numberReg.test(value) && value.length >= 10) {
      return dayjs(value * (value.length === 10 ? 1000 : 1)).format('YYYY-MM-DD HH:mm:ss')
    }
    // 正则匹配 是否value是时间YYYY-MM-DD HH:mm:ss格式
    const timeReg = /^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/
    if (timeReg.test(value)) {
      return value
    }
    return undefined
  }
  const interfaceAddress = ENV_IS_SWAN ? 'POST/job/v2/job/infoSeo' : 'POST/job/v2/job/info'
  const params:any = {
    occIds,
    jobId: Number(id),
    sortTime: isTimestampOrDate(decodeURIComponent(list_time)),
  }
  // 首页跳转获取详情判断是否是推荐tab
  if (source_id == 1) {
    const { selectClassifyTabId } = store.getState().storage
    const { isRecommend } = selectClassifyTabId || {}
    params.recommend = !!isRecommend
  }
  return wx.$.javafetch[interfaceAddress](params, { hideErrCodes: true })
}

/** 百度seo数据 */
export async function getBaiduSeoDate(areaId, occIdList = [], pageSource, id = undefined) {
  const { data } = await wx.$.javafetch['POST/seo/v1/tab/info']({ pageSource, areaId: areaId || 1, occIdList, id: Number(id) })
  if (data.tabDTOList.length) {
    this.setData({
      tabDTOList: data.tabDTOList,
    })
  }
}

/** 面包屑 */
export async function getCrumb({ areaId, occV2 = [], pageSource, infoType = 1, infoId = undefined }) {
  const res = await wx.$.javafetch['POST/seo/v1/crumb/info']({ pageSource, areaId: areaId || 1, occV2, infoType, infoId })
  if (res.code == 0) {
    this.setData({
      breadCrumbs: res.data,
    })
  }
}
