/*
 * @Date: 2021-12-29 10:07:28
 * @Description: 找活详情
 * @Path-query: {uuid: 查询找活详情,resumeSubUuid:查询找活详情, id=, origin="来源", type="类型" } uuid或者resumeSubUuid 必传
 *    origin:
 *      'certificate' 如果在找活大列表点击了技能证书，则跳转到详情获取数据后自动滚动到对应部分
 *      'factory' 如果是从工厂专区跳转过来不展示底部推荐
 *      'factory_certificate' 如果是从工厂专区点击了技能证书跳转过来不展示底部推荐
 *      'factory_recommend' 如果是从工厂专区专属人才推荐跳转过来不展示底部推荐
 *      'myContactHistory' 联系记录页
 *      'myContactHistory_factory' 联系记录页工厂专区类型
 *      ‘logistics’ 物流专区
 *      'myContactHistory_logistics' 联系记录页物流专区类型
 *    type:
 *      'whoContacted' 谁联系过我
 *      'myContacted' 我联系的人
 *      'groupConversation' 从聊一聊详细进入（为这个时不显示聊一聊按钮）
 *      'myGoback' 点击聊一聊返回到上一页
 */

import { dispatch, actions, store, storage, connectPage, MapStateToData, messageQueue } from '@/store/index'
import { isToComplaintOrClassify } from '@/utils/helper/common/index'
import { getRecruitOrResumeShareTitle, getShareInfoByTypeV2 } from '@/utils/helper/share/index'
import { getFilterData, getGlobalDefaultConfig } from '@/utils/helper/resume/index'
import { cardViewHistory, SOURCE_ID_NAME_DATA } from '@/utils/helper/list/index'
import { dealGuideLogin } from '@/utils/helper/login/index'
import { helper, tools } from '@/utils/index'
import dayjs from '@/lib/dayjs/index'
import resource from '@/components/behaviors/resource'
import { getActivePoint, getResumeDetailQuery, getResumeListDom, preload, setListPopData, setWechatTDK } from './utils'
import * as resumeUtils from './utils'
import { communicate } from '@/utils/helper/member/index'
import { isIos } from '@/utils/tools/validator/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { isShareParam } from '@/utils/init'
import { dealDialogApi } from '@/utils/helper/dialog/index'
import { toLogin } from '@/utils/helper/common/toLogin'
import { SHARE_CHANNEL } from '@/config/share'
import { tryPromise } from '@/utils/tools/common/index'

const mapStateToData: MapStateToData = (state) => {
  return {
    /** 找活免费查看名片剩余次数-- 工程类老板 */
    imGlobalSwitch: state.message.imGlobalSwitch,
    contactDataCont: state.myContact.dataCont,
    login: state.storage.userState.login,
    // 简历详情的未登录文案
    loginTextGroup: state.config.loginTextGroup,
  }
}

const slideAB = {}

/**
 * @name 找活详情
 * @param publishPage onShow 主要是与子组件 v4-recommend-workers 进行通信处理 已查看工人👷🏻‍♂️卡片 标题置灰
 * @param query.nearbyWorkerListApiSource string
 * 1、找活大列表-查看名片-名片详情页下方；  -- list OK
 * 2、找活搜索-查看名片-名片详情页下方；    -- search OK
 * 3、《附近适合您的工人》推荐列表-查看名片-名片详情页下方； -- detail OK
 * 4、IM-查看名片-名片详情页下方；   -- im OK
 * 5、收藏-收藏名片-查看名片-名片详情页下方；  -- collection 无收藏名片
 * 6、浏览记录-我看过谁（名片类）-查看名片-名片详情页下方； -- browse OK
 * @param query.isReturn 联系记录页面传入决定 拨打点按钮展示文案 1 已退费 | 0 未退费
 */
Page(
  connectPage(mapStateToData)({
    ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
    /** 找活的详情 resume/v1/recommend/detail 相应数据 */
    resDetail: {},
    data: {
      ...(ENV_IS_SWAN ? resource.data : {}),
      /** 是否显示简历已屏蔽的UI */
      isCloseEmpty: false,
      /** 顶部高度 */
      topHeight: tools.common.getHeaderHeight('0px', true),
      /** 底部悬浮按钮高度 */
      bottomHeight: '136rpx',
      /** 临时显示的数据 */
      preDetails: {},
      detail: { showTel: '' },
      /** 推荐的师傅列表展示相关参数 */
      showRecommendWorkers: false,
      resumeDetailNearbyListReqParams: {},
      /** 找活推荐列表，最下面跳转需要的路径 */
      url: '',
      /** scrollTop的滚动位置 */
      scrollTop: 0,
      /** 显示底部栏 */
      showBottom: true,
      /** 页面路由参数 {uuid: '找活详情的uuid【必须】', id: '', origin: '', scene: number} 
       * @property {number} scene
       * 1: 推荐牛人列表
      //  * 2: 推荐牛人列表详情页
       * 3: 搜索牛人列表
      //  * 4: 搜索牛人列表详情页
       * 5: 我沟通过列表
      //  * 6: 我沟通过列表详情页
       * 7: 沟通过我列表
      //  * 8: 沟通过我列表详情页
      //  * 9: 谁看过我列表详情页
      //  * 10: 我看过谁列表详情页
      //  * 11: 收藏列表详情页
      //  * 12: 电话直拨效果详情页
      //  * 13: 在线畅聊卡效果详情页
      //  * 14: 搜索畅聊卡效果详情页
      //  * 15: 置顶效果详情页
      //  * 16: 加急招效果详情页
       * 17: 评价邀约
       * 18: 简历推送
       * 19: IM站内信
      //  * 20: 分享简历详情页
       * 0: 未知
       */

      query: {},
      /** 判断聊一聊查看电话 chat，还是直接查看电话 call */
      callPhoneType: '',
      /** 是否隐藏骨架屏 */
      hideSkeleton: true,
      /** 是否显示聊一聊按钮 */
      isShowChat: true,
      /** 是否已执行过 */
      isImGlobalSwitch: false,
      /** 判断是否显示评价引导 */
      isShowGuidance: true,
      /** 找活详情评价内容控制器 */
      evaluateContentControl: {
        show: false,
        expression: 0,
      },
      isIos: isIos(),
      /** 判断当前页面是否隐藏了 */
      isHide: false,
      // * 中间号
      /** 中间号弹窗显示组合,call拨打电话,revise修改电话,write填写电话,none弹窗隐藏 */
      showMiddleVisible: '',
      /**  中间号弹窗类型, 1=可选直接拨号，2=必须使用安全号 0=安全号不弹窗 */
      popType: 1,
      /** 中间号弹窗文案 */
      popContent: [],
      newContent: [],
      /** 中间号弹窗额外数据 */
      popContentData: {
        /** 是否消耗积分查看该信息：0-未消耗，1-已消耗。备注：只用于中间号交流弹窗。 */
        is_expense_integral: 0,
        /* 标识本次查看电话动作，是否真实消耗了积分。0-未消耗，1-已消耗。 */
        has_expense_integral: 0,
      },
      /** onShow执行的次数 */
      onShowNum: 0,
      /** 评价弹窗是否加入中间号获取真实号码 */
      isMidGetTel: {
        isShow: false,
        tel: '',
        action: 2,
      },
      /** 埋点数据 */
      resumeStatistics: null,
      /** 拨打电话时间间隔-埋点使用 */
      startTime: null,
      /** 拨打电话点击入口记录-埋点使用 */
      click_entry: '',
      /** 来源是否是工厂 */
      isFactory: false,
      /** 是否点击拨打电话 */
      isCallPhone: false,
      /** 是否充值回来 */
      isRechargeBack: false,
      /** canvas绘制模版 */
      canvasTemplate: {},
      /** 生成出来对分享图 */
      canvasImg: '',
      /** 判断显示页面是否需要重新请求详情接口 */
      isYesState: false,
      /** 是否在播放语音 */
      isPlay: false,
      /** 语音路由 */
      remoteAudioUrl: '',
      // 登录状态是否已更新过数据
      isLoginUpdate: false,
      /** 资源位标识对应的数据key */
      resourceKeys: {
        float_resume_detail_under: 'buoyUnder',
        float_resume_detail_top: 'buoyTop',
      },
      /** 资源位——浮标上 */
      buoyTop: null,
      /** 资源位——浮标下 */
      buoyUnder: null,
      /** 联系按钮 */
      btnObj: {
        phone: {
          type: 'phone',
          btnText: '拨打电话',
          isShow: true,
        },
        chat: {
          type: 'chat',
          btnText: '聊一聊',
          isShow: true,
        },
      },
      /** 是否显示滑动提示 */
      showSlideTip: false,
      /** 是否显示空状态 */
      isEmpty: false,
      // 空状态文案
      emptyTitle: '',
      nextNum: 0,
      slideAB: false,
      /** 是否完全受限 */
      ifHideModule: false,
      // 关联职位信息ID
      relatedInfoId: '',
      selectPositionTabId: '',
      /** 是否开启活跃标签AB实验 */
      isActiveAb: false,
      /** 活跃标签AB实验埋点 */
      activeStatusAb: '',
      isAndroid: !isIos(),
      cpDialogIdentify: '', // 选择职位弹框1，弹框2
      cpConfirmImInit: false, // 选择职位后，点击确定是否跳转IM会话详情
      restrictLoading: true, // 是否在请求连接受限接口，默认为true，加载之后设置为false
      maskAndVisible: {
        hidden: {
          /** 是否隐藏VIP banner */
          vipBanner: false,
          /** 隐藏视频简历 */
          video: false,
          /** 隐藏附近工人 */
          nearbyWorkers: true,
        },
        mask: {
          /** 模糊工作经历 */
          workExp: false,
          /** 模糊教育经历 */
          edu: false,
          /** 模糊项目经验 */
          projExp: false,
          /** 模糊技能证书 */
          cert: false,
        },
        /** 连接受限 */
        restrict: false,
      },

    },
    ...(ENV_IS_SWAN ? resource.methods : {}),
    /** 初始化数据 */
    async init(isLoad?) {
      const { query } = this.data
      /** 获取找活名片数据 */
      const { info } = store.getState().resume

      if (!wx.$.u.isEmptyObject(info || {}) && (!!query.uuid || !!query.resumeSubUuid) && (info.uuid == query.uuid || info.uuid == query.resumeSubUuid)) {
        try {
          const [, state] = await messageQueue((state) => state.resume.requestData, true).catch((err) => [])
          const requestData = wx.$.u.getObjVal(state, 'resume.requestData') || {}
          if (requestData[query.uuid] && requestData[query.uuid].data && query.sourceId != '24') {
            const request = wx.$.u.deepClone(requestData[query.uuid])
            // 保存请求的响应数据
            this.resDetail = requestData[query.uuid].data
            this.handleDetailInfo(request, isLoad)
            // this.contactBtnText()
            this.judgeMaskAndVisible()
          } else {
            this.initResumeDetail(isLoad)
          }
          if (!['1', '2', '4', '24'].includes(query.sourceId)) {
            /** 清空缓存数据 */
            dispatch(actions.resumeActions.setRequestData({}))
          }
          dispatch(actions.resumeActions.setInfo({}))
        } catch (error) {
          this.initResumeDetail(isLoad)
        }
      } else {
        this.initResumeDetail(isLoad)
      }
    },

    /** 页面加载时触发 */
    async onLoad(opt) {
      if (isShareParam(opt, ['uuid', 'resumeSubUuid'])) {
        return
      }
      const options = { ...(opt || {}), ...(wx.$.r.getParams()) }
      const isWithStringFormatValue = typeof options?.isWithSearchHistory === 'string' ? options?.isWithSearchHistory == 'true' : (options?.isWithSearchHistory || false)
      const sData: any = { hideSkeleton: options.sourceId == '24' || false, query: { ...options, sceneV2: options.sceneV2 || resumeUtils.getQueryScene(options), isWithSearchHistory: isWithStringFormatValue }, relatedInfoId: options.relatedInfoId || options.jobId || '' }
      this.setData(sData)
      // 未登录才请求接口-显示未登录引导横幅
      const pageCode = getPageCode()
      dispatch(actions.configActions.getNoLoginCurrentText(pageCode))
      let showSlideTip = false
      const { userId } = store.getState().storage.userState || {}
      /** 获取找活的配置-已更新到model当中-必须更新 */
      if (['1', '2', '24'].includes(options.sourceId)) {
        if (typeof slideAB[`userId_${userId || ''}`] === 'undefined') {
          slideAB[`userId_${userId || ''}`] = await wx.$.u.getAbUi('resumePageTurner', 'withPageTurner')
          wx.$.u.cache('AB', 'resumePageTurner', slideAB[`userId_${userId || ''}`])
        }
        if (slideAB[`userId_${userId || ''}`]) {
          showSlideTip = store.getState().storage.common.showSlideTip
          showSlideTip && dispatch(actions.storageActions.setCommonItem({ showSlideTip: false }))
          getResumeListDom(options)
        }
        if (options.sourceId == '24' && options.showRecommendTip) {
          wx.$.msg('符合要求的牛人已展示完毕，为您推荐其他牛人')
        }
      }
      getGlobalDefaultConfig()
      const isFactory = ['factory', 'factory_certificate', 'myContactHistory_factory', 'factory_recommend'].includes(options.origin)
      // const isLogistics = ['logistics', 'myContactHistory_logistics', 'factory_recommend'].includes(options.origin)
      // eslint-disable-next-line no-nested-ternary
      // const type = isFactory ? 2 : isLogistics ? 3 : 1
      // this.tsdata.dfSpeciaArea = type

      this.setData({
        isFactory,
        slideAB: slideAB[`userId_${userId || ''}`],
        startTime: dayjs().unix(),
        showSlideTip,
      })
      /** 获取页面初始化数据 */
      this.init(true)
      /** 页面的一些初始化操作 */
      this.initActions()
      ENV_IS_SWAN && resource.lifetimes.attached.call(this)
      if (options.uuid || options.resumeSubUuid) {
        seo(options.resumeSubUuid || options.uuid)
      }
      options.origin === 'factory' && setListPopData(options.id)
      // 处理未登录引导弹窗，只处理招工列表来源
      const { uuid, origin, resumeSubUuid } = options
      setTimeout(() => {
        if (origin === 'default') {
          dealGuideLogin('resumeDetail', '', null, resumeSubUuid || uuid)
        }
      }, 500)
    },
    /** 下拉刷新 */
    onPullDownRefresh() {
      this.commonRefreshWorkerResumeDetails()
    },
    /** 简历详情页页面刷新-公共方法（也用于自动登录后手动触发一次） */
    commonRefreshWorkerResumeDetails() {
      this.init().finally(() => {
        wx.stopPullDownRefresh()
      })
    },
    /** 重新获取聊一聊按钮是否显示 */
    async againGetIsShowChat() {
      await messageQueue((state) => state.message.imGlobalSwitch)
      const { detail } = this.data
      const { userId } = detail || {}
      // 判断聊一聊按钮是否显示
      this.getIsShowChat(userId)
    },
    /** 生命周期函数--监听页面显示 */
    async onShow() {
      // this.fetchResumeInfoVisible()
      const query = wx.$.r.getQuery()
      if (isShareParam(query, ['uuid', 'resumeSubUuid'])) {
        return
      }
      // 用于处理进入A的找活详情，查看项目经验列表（技能证书），再查看B的项目经验列表（技能证书），返回A，此时项目经验展示的B的项目经验
      if (this.resumeDetailsOther) {
        dispatch(actions.resumeActions.setState({ resumeDetailsOther: this.resumeDetailsOther, preDetails: {} }))
      }
      const { isImGlobalSwitch, detail = {}, isCallPhone, isRechargeBack, isYesState, showMiddleVisible } = this.data
      if (isImGlobalSwitch && detail.userId) {
        this.againGetIsShowChat()
      }
      const isLogin = store.getState().storage.userState.login
      if (isLogin) {
        if (this.data.onShowNum >= 1 && !isYesState) {
          // 更新找活详情(主要处理登录之后重新更新状态)
          this.initResumeDetail()
          // this.checkResumeInfoVisible()
        }
        if (!this.data.isLoginUpdate) {
          this.setData({ isLoginUpdate: true })
          // 获取分享标题
          this.getShareTitle()
          // 获取登录用户的实名状态
          dispatch(actions.userActions.fetchUserInfo())
        }
      }
      ENV_IS_SWAN && resource.pageLifetimes.show.call(this)
      /** 中间号通话结束弹窗 */
      const isShowPopup = storage.getItemSync('isShowPopup')
      const currentPage = wx.$.r.getCurrentPage()
      if (isShowPopup == currentPage.route && detail.resumeSubUuid) {
        wx.$.l.showMidPopup(0, 'resume', detail.resumeSubUuid, null, { from: 1 }, detail.buriedPointData).then((res) => {
          const data: any = res
          if (data) {
            /** 收藏成功更新页面 */
            if (data.type == '2') {
              this.initResumeDetail()
            } else if (data.type == '3') {
              this.setData({
                evaluateContentControl: {
                  show: true,
                },
                /** 是否加入中间号获取真实号码 */
                isMidGetTel: {
                  isShow: true,
                  tel: data.tel,
                  action: 2,
                },
              })
            } else if (data.type == '4') {
              this.setData({
                evaluateContentControl: {
                  show: true,
                },
                isMidGetTel: {
                  isShow: false,
                  tel: '',
                  action: 2,
                },
              })
            }
          }
        })
      } else if (isShowPopup) {
        storage.setItemSync('isShowPopup', '')
      } else if (isCallPhone && !isRechargeBack && showMiddleVisible != 'call') {
        const showCooperation = storage.getItemSync('showCooperation')
        this.setData({
          evaluateContentControl: {
            show: !showCooperation[detail.resumeId],
          },
          isMidGetTel: {
            isShow: false,
            tel: '',
            action: 2,
          },
          isCallPhone: false,
        })
      }
      this.setData({ isHide: false, isYesState: isLogin, onShowNum: this.data.onShowNum + 1 })
    },

    onHide() {
      this.setData({ isHide: true })
    },
    onCpSelected(e) {
      const { jobId } = e.detail
      this.setData({ relatedInfoId: jobId || '' })
    },

    onCpConfirm(e) {
      const { jobId } = e.detail
      const { cpConfirmImInit } = this.data
      if (cpConfirmImInit) {
        this.onGetImChat({ relatedInfoId: jobId })
        this.setData({ cpConfirmImInit: false })
      }
    },
    onCpClose() {
      this.setData({ cpConfirmImInit: false })
    },
    onCpNoSend() {
      const { cpConfirmImInit } = this.data
      if (cpConfirmImInit) {
        this.onGetImChat()
        this.setData({ cpConfirmImInit: false })
      }
    },
    /** 页面的一些初始化操作 */
    initActions() {
      /**
       *  1、风控需求：当用户在绑定手机弹窗中没有绑定手机，点击了返回，
       *  这时需要展示一个让用户在剩余时间内绑定手机的弹窗，弹窗内需要展示剩余时间
       *  这个只会触发一次，后面不会再重复触发，所以使用storage
       */
      const { common: { showBindPhonePopup, expireTime, showBindPhoneTimeoutPopup } } = store.getState().storage
      if (showBindPhonePopup && !showBindPhoneTimeoutPopup) {
        // 将弹窗状态设置为已经展示过
        dispatch(actions.storageActions.setCommonItem({ showBindPhoneTimeoutPopup: true }))
        wx.$.confirm({
          content: `请您在${expireTime}前完成手机号绑定,以免影响您的正常使用`,
          confirmText: '我知道了',
        })
      }
    },

    async judgeMaskAndVisible() {
      const { maskAndVisible: { ...newMaskVisible }, query: { selectedTab = {} } } = this.data
      const sceneV2 = resumeUtils.getQueryScene(this.data.query)
      /** NOTE - 非正在招职位 */
      await tryPromise(this.fetchResumeInfoVisible())
      const { detail, hasNotEndAndCheckPasJob, ifHideModule } = this.data
      /** IM权益 */
      const hasImChatRight = detail && detail.hasImChatRight
      /** 电话权益 */
      const hasTelRight = detail && detail.hasTelRight
      /** 是否有权益 */
      const hasRight = hasImChatRight || hasTelRight
      const checkStatus = selectedTab && selectedTab.checkStatus
      /** 是否联系受限 */
      newMaskVisible.restrict = ifHideModule
      /** 非大列表，不展示长按钮 */
      if (sceneV2 != 1 && sceneV2 != 3) {
        newMaskVisible.restrict = false
      }
      /** NOTE - 大列表，非在招职位 */
      if (sceneV2 == 1 && !hasNotEndAndCheckPasJob) {
        newMaskVisible.hidden.vipBanner = true
        newMaskVisible.hidden.video = true
        newMaskVisible.hidden.nearbyWorkers = true
        newMaskVisible.mask.workExp = !hasRight
        newMaskVisible.mask.edu = !hasRight
        newMaskVisible.mask.projExp = !hasRight
        newMaskVisible.mask.cert = !hasRight
        if (selectedTab && selectedTab.checkStatus == 1) {
          newMaskVisible.restrict = false
          newMaskVisible.hidden.vipBanner = false
        }
      } else if ((sceneV2 == 1 && hasNotEndAndCheckPasJob)
      || [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 0].includes(Number(sceneV2))) {
        /** NOTE - 大列表正在招，谁看过我，我看过谁，收藏来源 */
        newMaskVisible.hidden.vipBanner = false
        newMaskVisible.hidden.video = false
        newMaskVisible.hidden.nearbyWorkers = false
        newMaskVisible.mask.workExp = false
        newMaskVisible.mask.edu = false
        newMaskVisible.mask.projExp = false
        newMaskVisible.mask.cert = false
        /** 大列表 审核中 */
        if (sceneV2 == 1 && checkStatus == 1) {
          newMaskVisible.restrict = false
          newMaskVisible.hidden.vipBanner = false
        }
      } else if (sceneV2 == 3) {
      /** NOTE - 来源搜索结果页 */
        newMaskVisible.hidden.vipBanner = true
        newMaskVisible.hidden.video = true
        newMaskVisible.hidden.nearbyWorkers = true
        /** NOTE - 无论是否有权益都不做模糊 */
        newMaskVisible.mask.workExp = false
        newMaskVisible.mask.edu = false
        newMaskVisible.mask.projExp = false
        newMaskVisible.mask.cert = false
        newMaskVisible.restrict = false
        /** NOTE -  搜索畅聊卡 */
      }
      this.setData({
        maskAndVisible: newMaskVisible,
      })
      this.contactBtnText()
    },

    /** 
     * 检查简历受限的可见性
     *  */
    async fetchResumeInfoVisible() {
      try {
        const { query } = this.data
        const { jobId } = query || {}
        /** 通过复合字段转换出最终的scene */
        const sceneV2 = resumeUtils.getQueryScene(query)
        const { selectedTab = {} } = query || {}

        const params: any = {}
        /** 来源大列表 搜索结果页 */

        if ((sceneV2 == 1 && selectedTab?.jobId) || (sceneV2 == 3 && selectedTab?.jobId)) {
          params.jobId = selectedTab.jobId
          /** 来源其他场景 */
        } else if (jobId) {
          params.jobId = jobId
        }

        const scene = resumeUtils.mapSceneV2ToScene(query)
        const queryObj: any = { ...params, scene, subUuid: query.resumeSubUuid || query.uuid }

        /** 简历连接受限 */
        const response = await tryPromise(wx.$.javafetch['POST/resume/v3/contact/checkModuleVisibility'](queryObj, { timeout: 5000 }), {})

        if (response.code === 0) {
          const { ifHideModule, hasImChatRight, hasTelChatRight, hasNotEndAndCheckPasJob } = response.data || {}

          const hasRight = [hasImChatRight, hasTelChatRight].some((item) => item)
          this.setData({ hasRight, ifHideModule, hasNotEndAndCheckPasJob, restrictLoading: false })
        } else {
          /** 接口报错 默认受限 */
          this.setData({ ifHideModule: true, restrictLoading: false })
        }
      } catch (e) {
        this.setData({
          ifHiddenModule: true,
          restrictLoading: false,
        })
      }
    },
    /** 初始化找活详情数据 */
    async initResumeDetail(isLoad?) {
      const { query, slideAB } = this.data
      if (query.uuid || query.resumeSubUuid) {
        const { selectedTab, cities } = query
        const jobCity = (selectedTab?.jobCity || selectedTab?.city) ? [selectedTab.jobCity || selectedTab.city] : []
        const selectedCity = Array.isArray(cities) && cities.length ? cities : jobCity
        const occupations = selectedTab?.occList || []

        const param: any = {
          resumeSubUuid: query.resumeSubUuid || query.uuid,
          purchaseBaseReq: {
            cities: selectedCity.length ? selectedCity : [],
            occupations: occupations.map(item => item.occId),
          },
          scene: query.sceneV2 == 3 ? 2 : 0,
          jobId: selectedTab?.jobId || query.jobId,
        }
        // origin
        // param.resumeSubUuid = query.resumeSubUuid || query.uuid
        const res = await resumeUtils.getResumeDetail(param)
        slideAB && dispatch(actions.resumeActions.setRequestData({ ...store.getState().resume.requestData, [query.uuid]: res }))
        // 保存请求的响应数据
        this.resDetail = res.data
        this.handleDetailInfo(res, isLoad)
        // this.contactBtnText()
        this.judgeMaskAndVisible()
      } else {
        await wx.$.alert({ content: '查询失败' })
        wx.$.r.back()
      }
    },

    /** 处理找活名片数据 */
    async handleDetailInfo(res, isLoad?) {
      const { query } = this.data
      const { selectedTab } = query || {}
      const { origin = '', sourceId, uuid, guid, nearbyWorkerListApiSource, dircetion, scene = 0 } = query || {}
      let buryingPoint = {}
      try {
        buryingPoint = JSON.parse(query.buryingPoint)
      } catch (error) {
        buryingPoint = {}
      }
      const { storage, classify } = store.getState()
      const { code, message } = res || {}
      switch (code) {
        case 0: {
          const data = res.data || {}
          const { hasImChatRight, browseResp = {}, basicResp = {} } = data || {}
          const { viewed } = browseResp || {}
          /** 设置浏览历史缓存 */
          cardViewHistory.setHistory('resume', basicResp.subs ? basicResp.subs[0].resumeSubUuid : uuid, true, viewed)
          let showBottom = true
          const detail = resumeUtils.formatResumeDetailData(data, query)
          this.setData({ detail, hasRight: this.data.hasRight || detail.hasImChatRight })
          if (hasImChatRight) {
            this.onRefreshListCard('chat')
          }
          if (viewed) {
            this.onRefreshListCard('call')
          }
          // 判断是否显示底部栏
          if (detail.resumeSubUuid) {
            showBottom = res.data.userInfoResp.userId != storage.userState.userId && res.data.userInfoResp.userId != '0'
            this.getIsShowChat(res.data.userInfoResp.userId)
          }

          const { resDetail } = this
          const sData: any = {
            detail,
            showBottom,
          }
          this.setData(sData)
          if (isLoad) {
            const { sourceId, source } = this.data.query || {}
            const sourceIdObj = {
              source_id: sourceId || undefined,
              /** 定价方案ID: 1、2（若是基础定价，则上报0） */
              fix_price_id: String(wx.$.u.getObjVal(resDetail, 'resumeCheckoutResp.pricingId', '0')),
              ...buryingPoint,
              sourceKey: source,
            }
            // ! 是否是特惠拨打
            // 这里会去拿 messageQueue 里面的配置，所以放到setTimeout里面去，避免阻塞代码
            setTimeout(async () => {
              const pageCode = getPageCode()
              await messageQueue((state) => !!state.config.btnConfigStatus[`${pageCode}_b`])
              setWechatTDK(detail)
              const apData = await getActivePoint(res.data)
              this.setData({
                isActiveAb: apData.isActiveAb,
                activeStatusAb: apData.pointData.active_status,
              })

              if (apData.isActiveAb) {
                Object.assign(sourceIdObj, apData.pointData)
              }
              workersStatistics.call(this, resDetail, sourceIdObj)
            }, 0)
          }
          // 用于处理进入A的找活详情，查看项目经验列表（技能证书），再查看B的项目经验列表（技能证书），返回A，此时项目经验展示的B的项目经验
          this.resumeDetailsOther = res.data
          dispatch(actions.resumeActions.setState({ resumeDetailsOther: this.resumeDetailsOther, preDetails: {} }))
          // 获取附近推荐的工人
          // 当不属于工厂页面进入时，获取附近推荐的工人
          const resumeDetailNearbyListReqParams = { resumeSubUuid: detail.resumeSubUuid }
          const newData = { showRecommendWorkers: true, resumeDetailNearbyListReqParams }
          if (scene && scene == 3) {
            newData.showRecommendWorkers = false
          } else if (scene && scene == 1) {
            if (selectedTab && ((selectedTab.jobId && [0, 1].includes(selectedTab.checkStatus)) || selectedTab.jobDraftId)) {
              newData.showRecommendWorkers = false
            }
          }
          this.setData(newData)

          // 隐藏骨架屏
          // ! 如果在找活大列表点击了技能证书，则跳转到详情获取数据后自动滚动到对应部分
          if (origin) {
            this.onScrollTop(origin)
          }
          break
        }
        case 28010001: {
          await wx.$.alert({
            content: message || '查询失败',
            confirmText: '确定',
            time: 3,
          })
          wx.$.r.back()
          break
        }
        case 28016001:
        case 28010002:
        case 28019003:
        case 28018004: {
          if (sourceId == '24') {
            this.setData({ nextNum: this.data.nextNum + 1 }) // 更新 nextNum
            // 如果是滑动进入的找活详情，跳过特殊状态
            const nextQuery = await getResumeDetailQuery(uuid, guid, { origin, nearbyWorkerListApiSource, dircetion }, 2)
            // 如果连续5次跳过特殊状态，返回上一页
            if (this.data.nextNum >= 5) {
              await wx.$.msg('简历列表有更新，请刷新页面')
              wx.$.r.back()
              return
            }
            if (nextQuery && nextQuery.uuid) {
              this.onLoad(nextQuery)
            }
            if (!nextQuery) {
              await wx.$.msg('简历列表有更新，请刷新页面')
              wx.$.r.back()
            }
            return
          }
          this.setData({ isEmpty: true, emptyTitle: message || '查询失败' })
          break
        }
        default: {
          await wx.$.alert({ content: message || '查询失败' })
          wx.$.r.back()
          break
        }
      }
      // 隐藏骨架屏
      setTimeout(() => {
        this.setData({ hideSkeleton: true })
      }, 100)
      // 牛人或搜简历索结果页进入预加载前后一条数据
      slideAB[`userId_${storage.userState.userId || ''}`] && preload(this.data.query)
    },

    /** 获取分享title */
    async getShareTitle() {
      /** 去请求获取分享title的接口 */
      const config = await getRecruitOrResumeShareTitle('resume')
      this.shareTitle = config.shareTitle
    },

    /** 如果在找活大列表点击了技能证书，则跳转到详情获取数据后自动滚动到对应部分 */
    async onScrollTop(origin) {
      if (origin.indexOf('certificate') !== -1) {
        setTimeout(async () => {
          const scrollTop = await resumeUtils.handleScrollTop.call(this)
          wx.pageScrollTo({ scrollTop: scrollTop as any })
        }, 260)
      }
    },

    /** 返回上一页 */
    onBack() {
      wx.$.r.back()
    },

    /** 点击底部拨打电话 */
    async clickBottomPhone(e) {
      await wx.$.u.waitAsync(this, this.clickBottomPhone, [e], 2000)
      this.setData({ isYesState: false })

      // 判断是否弹起特惠弹窗，
      // ! 是否是特惠信息
      // const { detail, btnObj } = this.data
      // if (
      //   detail.isSpecialTel
      //     && (!detail.browseResp.viewed)
      //     && (btnObj.phone && btnObj.phone.isShow)
      // ) {
      //   const popup = await dealDialogApi({ dialogIdentify: 'dialDiscount' })
      //   // 有配置，
      //   if (popup) {
      //     const l = store.getState().storage.resumeDisCountCallPhone
      //     const { userId } = store.getState().storage.userState
      //     if (!l.includes(userId)) {
      //       await wx.$.model.discountCall({
      //         success: () => Promise.resolve(true),
      //       })
      //     }
      //   }
      // }
      this.onCallPhone(e)
    },

    /** 点击拨打电话 */
    async onCallPhone(e?) {
      const { resDetail } = this
      const occupationIdList = wx.$.u.getObjVal(resDetail, 'occupationIdList', [])
      const occV2 = occupationIdList.join(',') || ''
      let { click_entry } = this.data
      if (e) {
        click_entry = wx.$.u.getObjVal(e, 'currentTarget.dataset.click_entry', '')
        click_entry && this.setData({ click_entry })
      }

      const { userState } = store.getState().storage
      const { detail = {}, query } = this.data
      // 判断是否登录
      if (!userState.login) {
        toLogin(true)
        return
      }
      // 加急拨打或者回拨接口
      const tsTypes = ['2', '3']
      const { type, telType, infoId, source, sceneV2, selectedTab, infoSource } = query || {}
      if (type == 'whoContacted' || (type == 'groupConversation' && infoId && tsTypes.includes(`${telType}`))) {
        this.handleWhoContactMe()
      } else {
        // 拨打电话前需要进行实名判断
        wx.showLoading({ title: '正在联系...', mask: true })
        this.setData({ callPhoneType: 'call', isCallPhone: true })
        const filterData = await getFilterData()
        const popParams = { occupations: filterData.classify_id, cities: filterData.area_id }
        const { resumeSubUuid, buriedPointData } = detail || {}

        const { jobId: stJobId, isDraft, jobDraftId } = selectedTab || {}
        const { jobId: qJobId } = query || {}
        const params: any = { uuid: resumeSubUuid, popParams, scene: source == 'INBOX' ? 9 : undefined }
        if (isDraft) {
          params.jobDraftId = jobDraftId
        } else if (stJobId && stJobId != '0') {
          params.jobId = stJobId
        } else if (qJobId && qJobId != '0') {
          params.jobId = qJobId
        }
        if (infoSource) {
          params.collectInfoSource = Number(infoSource)
        }
        switch (`${sceneV2}`) {
          case '1':
            params.sceneV2 = 2
            break
          case '3':
            params.sceneV2 = 4
            break
          case '5':
            params.sceneV2 = 6
            break
          case '7':
            params.sceneV2 = 8
            break
          default:
            params.sceneV2 = sceneV2
        }
        // 站内信进入的简历详情
        if (!sceneV2 && source == 'INBOX') {
          params.sceneV2 = 19
        }

        // 如果是从沟通记录（我沟通过的）- 带有 搜索畅聊卡场景
        if (query.isWithSearchHistory) {
          params.isChatSearch = query.isWithSearchHistory
        }

        wx.$.l.resumeTelV3(params, {
          pageName: '简历详情',
          hasShowPhone: detail.viewed,
          fastQuery: { occV2 },
        }, buriedPointData).then(async (res) => {
          wx.hideLoading()
          const { data } = res || {}
          if (!data) {
            this.phoneOrChatReport('0')
            return
          }
          /** 拨打电话埋点 */
          this.phoneOrChatReportRes = res // 缓存一下接口的值
          this.phoneOrChatReport(!['0', '200', '10000'].includes(`${res.code}`) ? '0' : `${!detail.viewed ? '1' : '2'}`)
          const { tel, dialogIdentifyDefault, dialogData } = data
          const { dialogIdentify = '' } = dialogData || {}
          if (tel || dialogIdentifyDefault || (await wx.$.l.newResumeMidPops()).includes(dialogIdentify)) {
            wx.$.l.operationMidCallV3(
              { ...res.data },
              {
                query,
                hasShowPhone: detail.viewed,
                fastQuery: { occV2 },
              },
              {
                callPopBack: (pop) => {
                  this.setData({ showMiddleVisible: 'call' })
                  wx.$.resumeMidModel({
                    ...pop,
                    zIndex: 10011,
                    source: '2',
                    infoId: resumeSubUuid,
                    origin: query.type == 'whoContacted' ? 'contact' : '',
                    pageName: '简历详情',
                    call: (e) => {
                      const { detail } = e || {}
                      if (detail == 2) {
                        const showRealTelState = storage.getItemSync('showRealTelState')
                        if (showRealTelState) {
                          const currentPage = wx.$.r.getCurrentPage()
                          storage.setItemSync('showRealTel', currentPage.route)
                        }
                      }
                      this.onCallMidPhone(e)
                    },
                    close: () => {
                      this.onMidPhonePopClose()
                    },
                  })
                  this.callPhoneOk(detail)
                },
                callRealPhone: (isPrivacy = 0) => {
                  this.callRealOrMidPhone(isPrivacy)
                },
                callPhone: () => {
                  this.onCallPhone()
                },
              },
            )
          } else {
            this.callRealOrMidPhone(0)
          }
        })
          .catch((res) => {
            wx.hideLoading()
            this.phoneOrChatReport('0')
          })
      }
    },
    /** 拨打电话(真实拨打或者中间号拨打) */
    callRealOrMidPhone(isPrivacy = 1) {
      const { detail = {}, query } = this.data
      const { resumeSubUuid, buriedPointData } = detail || {}
      wx.$.l.resumeTelV3({ uuid: resumeSubUuid, isPopup: 0, isPrivacy }, {}, buriedPointData).then(async (resTel) => {
        if (resTel.code == 0) {
          await wx.$.l.operationMidCallV3(
            { ...resTel.data },
            { query },
            {
              callPopBack: (pop) => {
                this.setData({ showMiddleVisible: 'call' })
                wx.$.resumeMidModel({
                  ...pop,
                  zIndex: 10011,
                  source: '2',
                  infoId: resumeSubUuid,
                  origin: query.type == 'whoContacted' ? 'contact' : '',
                  pageName: '简历详情',
                  call: (e) => {
                    const { detail } = e || {}
                    if (detail == 2) {
                      const showRealTelState = storage.getItemSync('showRealTelState')
                      if (showRealTelState) {
                        const currentPage = wx.$.r.getCurrentPage()
                        storage.setItemSync('showRealTel', currentPage.route)
                      }
                    }
                    this.onCallMidPhone(e)
                  },
                  close: () => {
                    this.onMidPhonePopClose()
                  },
                })
              },
            },
          )
          this.callPhoneOk(detail, resTel.data)
        }
      })
    },
    /** 拨打电话返回结果处理逻辑 */
    callPhoneOk(detail, data?) {
      helper.list.cardViewHistory.setHistory('resume', detail.resumeSubUuid, true, true)
      const sData: any = { 'detail.viewed': true, 'detail.browseResp.viewed': true, 'detail.hasImChatRight': true, 'detail.isShowComplainedBtn': true }
      if (!detail.viewed && !detail.complainAvailable) {
        sData['detail.complainAvailable'] = true
      }
      if (data && data.tel) {
        sData['detail.showTel'] = data.tel
      }
      this.setData(sData)
      // 更新按钮
      this.contactBtnText()
      this.onRefreshListCard('call')
      this.updateResumeData({
        complainResp: { complainAvailable: sData['detail.complainAvailable'] },
        browseResp: { viewed: true },
      })
    },
    /**
     * 更新列表页面的卡片按钮状态
     * @param type chat:聊一聊  call:拨打电话
     *  */
    async onRefreshListCard(type) {
      await wx.$.u.wait(500)
      const { detail = {} } = this.data
      const { resumeSubUuid } = detail || {}
      const pages = getCurrentPages()
      const page = pages[pages.length - 2]
      const routers = ['pages/resume/index', 'subpackage/resume/listSearchResultsPage/index']
      if (resumeSubUuid && page && routers.includes(page.route)) {
        page.onCardBtnRefresh && page.onCardBtnRefresh(resumeSubUuid, type)
      }
    },

    /** 中间号绑定手机号 */
    onBinnedPhone() {
      this.setData({ showMiddleVisible: '' })
      this.onCallPhone()
    },

    /** 关闭修改手机号 */
    onReviseMidPhoneClose() {
      this.setData({
        showMiddleVisible: 'call',
      })
    },

    /** 关闭中间号弹窗 */
    onMidPhonePopClose() {
      this.setData({
        showMiddleVisible: '',
      })
    },

    /** 组件回调拨打中间号 */
    async onCallMidPhone(e) {
      await wx.$.u.waitAsync(this, this.onCallMidPhone, [e], 1500)
      const { detail, query } = this.data

      // val.detail 为 2 代表拨打虚拟号
      const val = e ? e.detail : 2
      if (val == 2) {
        const showRealTelState = storage.getItemSync('showRealTelState')
        if (showRealTelState) {
          const currentPage = wx.$.r.getCurrentPage()
          storage.setItemSync('showRealTel', currentPage.route)
        }
      }
      wx.showLoading({ title: '正在联系...', mask: true })

      const params: any = {
        uuid: detail.resumeSubUuid, isPopup: 0, isPrivacy: val == 2 ? 1 : 0,
      }
      // 如果是从沟通记录（我沟通过的）- 带有 搜索畅聊卡场景
      if (query.isWithSearchHistory) {
        params.isChatSearch = query.isWithSearchHistory
      }
      if (query.infoSource) {
        params.collectInfoSource = Number(query.infoSource)
      }
      wx.$.l.resumeTelV3(params, {}, detail.buriedPointData)
        .then(async (resTel) => {
          wx.hideLoading()
          this.setData({ showMiddleVisible: '' })
          const { code, data } = resTel
          if (code == 0) {
            if (val == 2 && data.timeRemaining) {
              await wx.$.l.midPhoneTimeMsg(data.timeRemaining)
            }
            setTimeout(() => {
              wx.$.u.callPhone(data.tel)
              this.callPhoneOk(detail, data)
            }, 200)
          }
        })
        .catch(() => {
          wx.hideLoading()
          this.setData({ showMiddleVisible: '' })
        })
    },

    /** NOTE 01 whoContactMe 处理从谁联系过我的页面中跳转过来点击拨打电话的逻辑 */
    async handleWhoContactMe() {
      const { contactDataCont, query, detail } = this.data
      const { type, telType, pullBackSwitch, id, infoId, infoType } = query || {}
      const { userInfoResp } = detail || {}
      const { userId } = userInfoResp || {}
      wx.showLoading({ title: '正在联系...', mask: true })
      const { user_id, info_id } = contactDataCont || {}
      if (!pullBackSwitch || (type == 'groupConversation' && telType == 3)) {
        const toUserId = type == 'groupConversation' ? userId : user_id
        const nInfoId = type == 'groupConversation' ? infoId : info_id
        const nInfoType = type == 'groupConversation' ? (infoType || 1) : 1
        const params = {
          toUserId,
          infoId: nInfoId,
          infoType: nInfoType,
          getPrivacyTel: false,
        }
        wx.$.l.recall(params, {
          source: 2,
        })
      } else {
        const nId = type == 'groupConversation' ? infoId : id
        wx.$.l.recallV3(nId, userId, false, {
          pageName: '简历详情',
          infoId: nId,
        }, false)
      }
    },
    /** NOTE 04 判断拨打电话之前是否显示评分评价弹框 */
    judgeShowScoreEvaluation(tel, cb?) {
      wx.$.u.callPhone(tel).then(() => {
        cb && cb()
      })
      /** 请求判断是否弹出评分评价弹窗 */
      // this.getEvaluation()
    },
    onOpenDraft(evt: any) {
      const { draftId } = evt.detail || {}
      if (draftId) {
        wx.$.nav.push('/subpackage/recruit/edit-draft/index', { draftId })
      }
    },

    onModifyJob(evt: any) {
      const { jobId } = evt.detail || {}
      if (jobId) {
        wx.$.nav.push('/subpackage/recruit/jisu_issue/index', { id: jobId })
      }
    },

    /** 点击投诉按钮 */
    onComplain() {
      const { userState } = store.getState().storage
      // 判断是否登录
      if (!userState.login) {
        toLogin(true)
      }
      const { complainAvailable, isComplained, viewed, resumeSubUuid, userId } = this.data.detail || {}
      if (isComplained) {
        wx.$.msg('您已经投诉过这条信息，请勿重复投诉！')
        return
      }
      // if (this.data.query.type !== 'whoContacted') {
      //   wx.$.msg('请查看完整手机号码后再操作！')
      //   return
      // }
      if (!complainAvailable) {
        wx.$.msg('该信息已过期，无法投诉！')
        return
      }
      this.setData({ isYesState: false })
      isToComplaintOrClassify({ id: resumeSubUuid, projectId: viewed ? '1101' : '1106', targetUserId: userId, complaintSource: '1005' })
    },
    /** h5页面投诉成功后回调 */
    complaintOk() {
      const { detail } = this.data
      const { viewed } = detail || {}
      if (viewed) {
        this.setData({ detail: { ...detail, isComplained: true } })
      }
    },
    /** 用户点击右上角分享,需传入title */
    onShareAppMessage(options) {
      const { detail } = this.data
      const { resumeSubUuid } = detail
      // 截图分享
      if (this.data.screenshotShareData && !!this.data.screenshotShareData.sharePage && options.from != 'button') {
        return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, ext: { isCurPageImg: true, type: 'resume', detail_id: resumeSubUuid, title: this.shareTitle || '您的好友推荐了一位优质牛人' }, from: options.from })
      }
      return getShareInfoByTypeV2({ type: SHARE_CHANNEL.SHARE_WECHAT_FRIEND, sharePage: 'packageOther/pages/boss-look-card/lookcard', sharePath: 'resume_bottom_btn', ext: { isCurPageImg: true, type: 'resume', detail_id: resumeSubUuid, title: this.shareTitle || '您的好友推荐了一位优质牛人' }, from: options.from })
      // return resumeUtils.shareAppMessageContent({ ...options, canvasImg, title: this.shareTitle || '您的好友推荐了一位优质牛人' })
    },

    /** 分享朋友圈 */
    onShareTimeline() {
    },

    /** 获取聊一聊按钮是否显示 */
    async getIsShowChat(uId) {
      const { query, detail } = this.data
      let isShowChat = query.type !== 'groupConversation' // 如果不是从聊一聊页面进入的 则默认为true
      if (isShowChat) {
        isShowChat = await wx.$.l.isShowChat(`${uId}`, { occIds: detail.occupationIdList || [], infoType: 2 })
      }
      this.setData({
        isShowChat,
        isImGlobalSwitch: true,
      })
    },

    /** im直接扣费 */
    async onGetImChat(e) {
      await wx.$.u.waitAsync(this, this.onGetImChat, [e], 1000)
      const { detail, query, onShowNum } = this.data
      const { hasImChatRight, resumeSubUuid, userId } = detail || {}
      const { relatedInfoId: nRelatedInfoId } = e || {}
      const { selectedTab } = query || {}

      const { jobId: stJobId, isDraft, jobDraftId } = selectedTab || {}
      const { jobId: qJobId, sceneV2, source, infoSource } = query || {}
      const params:any = { uuid: resumeSubUuid, scene: source == 'INBOX' ? 9 : 1 }
      if (isDraft) {
        params.jobDraftId = jobDraftId
      } else if (stJobId && stJobId != '0') {
        params.jobId = stJobId
      } else if (qJobId && qJobId != '0') {
        params.jobId = qJobId
      } else if (nRelatedInfoId) {
        params.jobId = nRelatedInfoId
      }
      if (infoSource) {
        params.collectInfoSource = Number(infoSource)
      }
      switch (`${sceneV2}`) {
        case '1':
          params.sceneV2 = 2
          break
        case '3':
          params.sceneV2 = 4
          break
        case '5':
          params.sceneV2 = 6
          break
        case '7':
          params.sceneV2 = 8
          break
        default:
          params.sceneV2 = sceneV2
      }
      // 站内信进入的简历详情
      if (!sceneV2 && source == 'INBOX') {
        params.sceneV2 = 19
      }

      const extParams: any = { workerId: userId, hasImChatRight }
      // 如果是从沟通记录（我沟通过的）- 带有 搜索畅聊卡场景
      if (query.isWithSearchHistory) {
        extParams.isChatSearch = query.isWithSearchHistory
      }

      wx.$.l.getImChat(params, extParams, {
        success: (sucRes) => {
          wx.hideLoading()
          const { relatedJobId, isChatSearchPurchase } = sucRes || {}
          this.phoneOrChatReport(hasImChatRight ? '2' : '1', 3)
          dispatch(actions.timmsgActions.setState({ resumesInfo: detail }))
          this.setData({ 'detail.hasImChatRight': true, isYesState: false, onShowNum: onShowNum + 1 })
          this.updateResumeData({ hasImChatRight: true })
          this.onRefreshListCard('chat')
          wx.$.l.initGroup(resumeSubUuid, 2, { relatedInfoId: relatedJobId || (nRelatedInfoId && nRelatedInfoId != '0' ? nRelatedInfoId : '') || params.jobId || 0, fromType: isChatSearchPurchase ? 10 : '' })
        },
        fail: () => {
          setTimeout(() => {
            wx.hideLoading()
          }, 2000)
          this.phoneOrChatReport('0', 3)
        },
      })
    },
    /** 聊一聊 */
    async onGoToChat() {
      await wx.$.u.waitAsync(this, this.onGoToChat, [], 1000)
      const { userState } = store.getState().storage
      // 判断是否登录
      if (!userState.login) {
        toLogin(true)
        return
      }
      const { query } = this.data
      const { type, sceneV2 } = query || {}
      const pages = getCurrentPages()
      // 上一页是聊一聊点击聊一聊返回
      if (type == 'myGoback') {
        const idx = pages.findIndex((item) => item.route.indexOf('subpackage/tim/groupConversation/index') >= 0)
        if (idx >= 0) {
          const num = pages.length - idx - 1
          wx.$.r.back(num > 0 ? num : 1)
          return
        }
      }
      const prevPage = pages[pages.length - 2] || {} as any
      const { route } = prevPage || {}
      if (route && route.indexOf('subpackage/tim/groupConversation/index') >= 0) {
        wx.$.r.back()
        return
      }
      const { detail, onShowNum } = this.data
      const { selectedTab } = query || {}
      const { jobId, checkStatus: stCheckStatus } = selectedTab || {}
      const hasJobId = (jobId && stCheckStatus == 2 && jobId != '0')
      const { hasImChatRight, resumeSubUuid, userId } = detail || {}
      const occupationIdList = wx.$.u.getObjVal(detail, 'occupationIdList', [])
      const occV2 = occupationIdList.join(',') || ''
      wx.showLoading({ title: '正在联系...', mask: true })
      this.setData({ callPhoneType: 'chat' })
      let bossPickJobScene = 0
      if (query.type == 'whoContactedMeToResume') {
        bossPickJobScene = 1
      } else if (hasJobId) {
        bossPickJobScene = 3
      }
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      const { jobId: stJobId, isDraft, jobDraftId, checkStatus } = selectedTab || {}
      const { jobId: qJobId, infoSource, source, isWithSearchHistory } = query || {}
      const params: any = { uuid: resumeSubUuid, scene: query.source == 'INBOX' ? 9 : 1 }
      if (isDraft) {
        params.jobDraftId = jobDraftId
      } else if (stJobId && stJobId != '0') {
        params.jobId = stJobId
      } else if (qJobId && qJobId != '0') {
        params.jobId = qJobId
      }
      if (infoSource) {
        params.collectInfoSource = Number(infoSource)
      }
      switch (`${sceneV2}`) {
        case '1':
          params.sceneV2 = 2
          break
        case '3':
          params.sceneV2 = 4
          break
        case '5':
          params.sceneV2 = 6
          break
        case '7':
          params.sceneV2 = 8
          break
        default:
          params.sceneV2 = sceneV2
      }
      // 站内信进入的简历详情
      if (!sceneV2 && source == 'INBOX') {
        params.sceneV2 = 19
      }
      wx.$.l.getImChatPre(
        params,
        {
          workerId: userId,
          fastQuery: { occV2 },
          selectPositionTabId: hasJobId ? selectedTab.jobId : '',
          bossPickJobScene,
          cpShow: query.type != 'groupConversation',
          hasImChatRight,
          isChatSearch: isWithSearchHistory,
        },
        {
          selectpostionback: async (spRes) => {
            const { dialogIdentify } = spRes || {}
            that.setData({ cpDialogIdentify: dialogIdentify, cpConfirmImInit: true })
            const changePosition = await wx.$.selectComponent.call(that, '#changeRelatedJob')
            await changePosition.onSelectPostionBack()
          },
        },
      ).then(async (sucRes) => {
        wx.hideLoading()
        const { relatedJobId, isChatSearchPurchase } = sucRes || {} as any
        this.phoneOrChatReport(hasImChatRight ? '2' : '1', 3)
        dispatch(actions.timmsgActions.setState({ resumesInfo: detail }))
        this.setData({ 'detail.hasImChatRight': true, isYesState: false, onShowNum: onShowNum + 1 })
        this.updateResumeData({ hasImChatRight: true })
        this.onRefreshListCard('chat')
        wx.$.l.initGroup(resumeSubUuid, 2, { relatedInfoId: relatedJobId || (stJobId && checkStatus == 2 && stJobId != '0' ? stJobId : (params.jobId || 0)), fromType: isChatSearchPurchase ? 10 : '' })
      }).catch(() => {
        setTimeout(() => {
          wx.hideLoading()
        }, 2000)
        this.phoneOrChatReport('0', 3)
      })
    },

    /** 点击评价引导 */
    onGuidanceClick({ detail }) {
      const { evaluateContentControl } = this.data
      this.setData({
        evaluateContentControl: {
          show: !evaluateContentControl.show,
          expression: detail?.expression,
        },
        isMidGetTel: {
          isShow: false,
          tel: '',
          action: 1,
        },
      })
    },
    /** 评价提交成功 */
    onEvalSubmitSuccess() {
      this.setData({
        isShowGuidance: false,
      })
    },
    /** 评价弹框关闭 */
    onEvalClose() {
      this.setData({
        evaluateContentControl: {
          show: false,
        },
      })
      if (this.data.query.showPopType === 'evaluate') {
        wx.$.r.back()
      }
    },

    /**
     * 获取特惠拨打的状态，
     * @returns [特惠拨打状态，是否是聊一聊]
     */
    getDisCountCallState() {
      const { detail, btnObj } = this.data
      // 简历满足定价方案设置的信息标签“特惠拨打” -- 简历无生效中的打电话权益。
      const rule = detail.isSpecialTel && (!detail.browseResp.viewed)
      return [rule && btnObj.phone?.isShow, !btnObj.phone?.isShow && btnObj.chat?.isShow]
    },
    /**
     * @description 拨打电话和聊一聊埋点
     * @param {object} get_status 获取状态
     * @param {object} get_status 拨打电话返回的data数据
     * @param clickType 点击类型 1、2、3(1-页面底部、2-页面中间、3-聊一聊)
     */
    phoneOrChatReport(get_status, clickType = 1) {
      const { startTime, detail, query } = this.data
      let buryingPoint: any = {}
      try {
        buryingPoint = JSON.parse(query.buryingPoint)
      } catch (error) {
        buryingPoint = {}
      }
      const { info } = buryingPoint || {}
      const { pagination, source_id } = info || {}
      const { resumeCheckoutResp, buriedPointData, resumeId, activeStatusText, basicResp } = detail || {}
      const { subs } = basicResp || {}
      const { pricingId } = resumeCheckoutResp || {}
      const { sourceId } = query || {}
      const r = this.phoneOrChatReportRes

      let sourceIdFormSourceKey = ''
      const { source } = query || {}
      if (source) {
        sourceIdFormSourceKey = H5_SOURCE_DATA[source]
      }

      let occupations_v2 = ''
      let occupations_v2_name = ''
      let occupations_type = '-99999'
      if (wx.$.u.isArrayVal(subs)) {
        const occIds = subs.map(i => i.occupationInfo.occId)
        const occNames = subs.map(i => i.occupationInfo.occName)
        const modes = subs.map(i => `${i.occupationInfo.mode}`)
        occupations_v2 = occIds.join(',')
        occupations_v2_name = occNames.join(',')
        if (modes.includes('2')) {
          occupations_type = '招聘'
        } else if (modes.includes('1')) {
          occupations_type = '订单'
        }
      }
      const eventData: any = {
        ...(info || {}),
        info_id: `${resumeId}`,
        pagination: `${pagination || ''}`,
        source_id: `${sourceId || source_id || sourceIdFormSourceKey || '26'}`,
        source: SOURCE_ID_NAME_DATA[`${sourceId || source_id || sourceIdFormSourceKey || '26'}`] || '小程序分享',
        active_status: activeStatusText || '', // 活跃状态
        post_distance: '',
        click_entry: `${clickType || '-99999'}`,
        dialing_interval_duration: (dayjs().unix() - startTime).toString(), // 拨打间隔时长(秒) 从进入详情页面到点击拨打电话的时间间隔
        get_status: `${get_status == 0 ? '0' : (get_status || '-99999')}`, // 获取状态 0、1、2（0-获取失败、1-获取成功（首次）、2-获取成功（非首次））
        resume_uuid: detail.resumeSubUuid,
        occupations_v2,
        occupations_v2_name,
        occupations_type,
        ...(r ? {
          consumption_product_score: r.data.expenseIntegral,
          fix_price_id: String(r.data.pricingId),
        } : {}),
        ...(buriedPointData || {}),
      }
      if (!eventData.fix_price_id) {
        eventData.fix_price_id = Number(pricingId || '0')
      }
      wx.$.collectEvent.event('workersPhoneCalls', eventData)
      // 清空埋点的临时缓存数据
      this.phoneOrChatReportRes = null
      this.setData({ startTime: dayjs().unix() })
    },
    async onAttention() {
      await wx.$.u.waitAsync(this, this.onAttention, [], 800)
      const { detail, query } = this.data
      const { selectedTab } = query || {}
      const jobId = selectedTab && selectedTab.jobId ? selectedTab.jobId : query?.jobId
      const extraParams: any = { infoSource: 0 }
      if (jobId) {
        extraParams.sourceInfoId = jobId // 招聘详情页的收藏需要传入jobId
      }
      /** 如果来源是搜索结果页（或者来源是收藏，但是收藏的infoSource是1），需要传1，否则传0 */
      if (query && ((query.sceneV2 && query.sceneV2 == 3) || (query.sceneV2 && query.sceneV2 == 11 && query.infoSource == 1))) {
        extraParams.infoSource = 1 // 场景值为3时，表示从牛人列表进入
      }

      let res: any = false
      const shift_is_focus = !detail.collected ? 1 : 0
      let attentionStatus = ''
      if (!detail.collected) {
        // 收藏
        res = await communicate.asyncAttention(
          {
            collectInfoId: query.resumeSubUuid || query?.uuid || detail.resumeSubUuid, // 子简历ID
            collectType: 2, // 收藏类型「1=招工, 2=找活, 3=老板」
            ...extraParams,
          },
          {
            routeType: 'push',
            loadingText: '收藏中...',
            successMsg: '收藏成功 你可前往我的-收藏查看',
          },
        )
        attentionStatus = '1'
      } else {
        // 取消关注
        res = await communicate.asyncCancelAttention(
          {
            collectInfoId: query.resumeSubUuid || query?.uuid || detail.resumeSubUuid, // 子简历ID
            collectType: 2, // 收藏类型「1=招工, 2=找活, 3=老板」
          },
          {
            loadingText: '取消收藏',
            successMsg: '已取消收藏',
          },
        )
        attentionStatus = '0'
      }
      if (res) {
        this.setData({
          'detail.collected': shift_is_focus,
        })
        this.updateResumeData({ browseResp: { collected: !!shift_is_focus } })
        if (query.sourceId == 14) {
          // 收藏牛人列表的数据要同步删除
          const pages = getCurrentPages()
          if (pages.length) {
            const lastPage = pages[pages.length - 2]
            if (attentionStatus == '0' && lastPage.onClickDeleteListItem) {
              lastPage.onClickDeleteListItem(query?.uuid || '')
            } else if (lastPage && lastPage.getCollectWorkerPageList) {
              lastPage.getCollectWorkerPageList(true, false)
            }
          }
        }
      }
    },
    /** 更新翻页缓存数据- 收藏｜已查看 */
    updateResumeData(options) {
      const { detail, slideAB } = this.data
      if (!slideAB || !detail.resumeSubUuid || !options) {
        return
      }
      const keys = Object.keys(options)
      const updatedData = wx.$.u.deepClone(store.getState().resume.requestData)
      const uuid = detail.resumeSubUuid
      if (uuid && updatedData[uuid]) {
        keys.forEach((key) => {
          // 判断options[key]类型是否为对象
          if (typeof options[key] === 'object') {
            const updatedBrowseResp = {
              ...updatedData[uuid].data[key],
              ...options[key],
            }
            updatedData[uuid].data[key] = updatedBrowseResp
          } else {
            updatedData[uuid].data[key] = options[key]
          }
        })
      }
      dispatch(actions.resumeActions.setRequestData(updatedData))
    },
    onUnload() {
      if (this.data.audioCtx) {
        this.data.audioCtx.destroy()
        delete this.data.audioCtx
      }
    },
    /** 物流分享绘制图片 */
    onImgOK(e) {
      this.setData({ canvasImg: e.detail.path })
    },
    /** 点击分享按钮-埋点 */
    onPointer() {
      wx.$.collectEvent.event('share_click', {
        page_name: '简历详情页',
      })
    },
    // 获取联系受限条件
    async canContact() {
      const { detail, query } = this.data
      if (!detail.resumeSubUuid) return true
      const res = await wx.$.javafetch['POST/resume/v3/contact/canContact']({ uuid: detail.resumeSubUuid, scene: query.source == 'INBOX' ? 9 : 0 })
      if (res.code == 0) {
        return res.data.canContact
      }
      return true
    },
    // 跳转职位发布
    async onGoToPublish(evt) {
      await wx.$.u.waitAsync(this, this.onGoToPublish, [evt], 800)
      const text = wx.$.u.getObjVal(evt, 'currentTarget.dataset.text', '')

      const { selectedTab = {}, jobId } = this.data.query
      const { detail } = this.data

      const occV2 = wx.$.u.getObjVal(detail, 'basicResp.subs.0.occupationInfo.occId')
      // 埋点
      wx.$.collectEvent.event('contact_restriction_button_click', {
        button_name: `${text}，立即沟通牛人`,
      })
      if (text === '打开职位' || text === '修改职位') wx.$.collectEvent.event('job_tip_click', {
        source_id: '3',
        button_name: text,
      })

      switch (text) {
        case '发布职位':
          wx.$.r.push({ path: '/subpackage/recruit/fast_issue/index/index', query: { occV2, flag: 'contact_limit' } })
          break
        case '修改职位':
          wx.$.r.push({ path: '/subpackage/recruit/jisu_issue/index', query: { id: selectedTab.jobId || jobId } })
          break
        case '打开职位':
          wx.$.r.push({ path: '/subpackage/recruit/edit-draft/index', query: { draftId: selectedTab.jobDraftId } })
          break
        default:
          break
      }
    },

    async discountClick() {
      const popup = await dealDialogApi({ dialogIdentify: 'dialDiscount' })
      popup && wx.$.model.discountCall({})
      // wx.$.model.discountCall({})
    },

    // 联系按钮展示
    async contactBtnText() {
      const { selectedTab } = this.data.query
      const { maskAndVisible } = this.data

      const pageCode = getPageCode()
      await messageQueue((state) => !!state.config.btnConfigStatus[`${pageCode}_b`])
      // 联系按钮配置
      const { list_b: btnConfig } = storage.getItemSync(`btn_${pageCode}`)
      const { detail } = this.data

      const { occupationIdList, browseResp, resumeCheckoutResp, hasImChatRight, status } = detail || {}
      let btnObj = {}// 显示按钮数据
      const config = [] // 按钮配置
      if (btnConfig && btnConfig.length && wx.$.u.isArrayVal(occupationIdList)) {
        const obj = btnConfig.find((j) => j.occId == occupationIdList[0])
        const n_obj = btnConfig.find((j) => j.occId == 0)
        if (obj) {
          config.push(obj)
        } else if (n_obj) {
          config.push(n_obj)
        }
      }
      const phoneBtnText = browseResp?.viewed ? '继续沟通' : resumeCheckoutResp?.free ? '免费拨打' : '拨打电话'
      const imBtnText = hasImChatRight ? '继续聊' : '聊一聊'
      if (status == 0) { // 代发信息只展示聊一聊
        btnObj = {
          phone: {
            type: 'phone',
            btnText: phoneBtnText,
            isShow: false,
          },
          chat: {
            type: 'chat',
            btnText: imBtnText,
            isShow: true,
          },
        }
      } else if (!config.length) { // 无配置根据工种类型展示
        const { occClassification } = detail.basicResp?.subs?.[0]?.occupationInfo || {}
        // const isCall = wx.$.u.isArrayVal(occupationIdList) && occupationIdList[0].occClassification != 1
        const isCall = occClassification != 1
        btnObj = {
          phone: {
            type: 'phone',
            btnText: phoneBtnText,
            isShow: isCall,
          },
          chat: {
            type: 'chat',
            btnText: imBtnText,
            isShow: !isCall,
          },
        }
      } else { // 有配置
        const hConfig = config[0]// 取优先级最高的配置
        btnObj = {
          phone: {
            type: 'phone',
            btnText: phoneBtnText,
            isShow: hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0,
          },
          chat: {
            type: 'chat',
            btnText: imBtnText,
            isShow: hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('chat') >= 0) >= 0,
          },
        }
      }

      const notContact = maskAndVisible.restrict

      this.setData({ btnObj, notContact })
      if (notContact) {
        // 埋点
        wx.$.collectEvent.event('contact_restriction_button_exposure', {
          button_name: '发布职位，立即沟通牛人',
        })
      }
    },

    /** 关闭翻页滑动引导 */
    async onSlideTip(e) {
      e.type === 'load' && await wx.$.u.wait(5100)
      this.setData({ showSlideTip: false })
    },
    onCancelDislike() {
      const { detail } = this.data
      const { userInfoResp } = detail || {}
      const { userId } = userInfoResp || {}
      wx.showLoading({ title: '请求中...' })
      wx.$.javafetch['POST/clues/v1/inappropriate/remove']({ toUserId: userId }).then(async (res) => {
        const { code, message } = res || {}
        if (code != 0) {
          wx.hideLoading()
          wx.$.msg(message || '请求失败,请稍后重试')
          return
        }
        const pages = getCurrentPages()
        if (wx.$.u.isArrayVal(pages)) {
          const page = pages.find(item => item.route.indexOf('subpackage/tim/groupConversation/index') >= 0)
          if (page) {
            page.onRefreshGroupInfo && page.onRefreshGroupInfo()
          }
        }
        await this.initResumeDetail()
        wx.hideLoading()
        wx.$.msg('你已取消对该牛人的不合适标记，系统将继续通知TA对你发出的聊天消息')
      }).catch((err) => {
        wx.hideLoading()
        const { error, message } = err || {}
        let msg = '请求异常,请稍后重试'
        if (error && message) {
          msg = message
        }
        wx.$.msg(msg)
      })
    },
  }),
)

// 页面seo方法
async function seo(id) {
  if (!ENV_IS_SWAN) {
    return
  }
  const { data } = await wx.$.javafetch['POST/resume/v1/resumeSeo/tdkInfo'](
    { resumeSubUuid: id },
    {
      showLoading: false,
      hideMsg: true,
      hideError: true,
    },
  )
  /** 获取自定义数据 */
  if (data && data.title) {
    wx.$.setTDK({
      title: data.title,
      description: data.description,
      keywords: data.keywords,
    })
  }
}

const H5_SOURCE_DATA = {
  YUPAO_B_CHAT_CARD: 22, // 在线畅聊卡
  YUPAO_B_VISIT_CARD: 22, // 电话直拨卡
  MASS_RMT: 22, // 群发炸弹C
  UrgentEffectList: 25, // 加急招
  SetTopEffectList: 25, // 置顶
  YUPAO_JOB_RENEWAL_TOP: 25, // 置顶续期
  YUPAO_B_REFRESH_CARD: 25, // 刷新职位卡
  JOB_VIE_CARD: 25, // 竞招职位
  YUPAO_B_PUBLISH_CARD: 25, // 发布普通职位卡
  YUPAO_C_REFRESH_CARD: 16, // 简历刷新
  YUPAO_RESUME_TOP: 16, // 加急简历
  YUPAO_SUB_CARD: 11, // 联系老板
  EffectContactList: 23, // 谁联系过我简历列表
}
/**
 * 工人详情和拨打电话埋点
 * @param detail 找活详情
 * @param oSta
 * @param type 1.找活详情埋点 2.拨打电话埋点  默认为1
 * */
async function workersStatistics(detail, oSta: any = {}) {
  if (!detail) {
    return
  }
  let { source_id } = oSta || {}
  const { sourceKey } = oSta || {}
  if (sourceKey) {
    source_id = H5_SOURCE_DATA[sourceKey]
  }
  const { userInfoResp, basicResp, buriedPointData } = detail || {}
  const { resumeId, resumeUuid, subs } = basicResp || {}
  const { activeStatusText } = userInfoResp || {}
  const { query } = this.data
  let buryingPoint: any = {}
  try {
    buryingPoint = JSON.parse(query.buryingPoint)
  } catch (error) {
    buryingPoint = {}
  }

  const { info } = buryingPoint || {}
  const { source_id: infoSourceId, pagination } = info || {}
  if (!source_id) {
    infoSourceId && (source_id = infoSourceId)
  }
  let occupations_v2 = ''
  let occupations_v2_name = ''
  let occupations_type = '-99999'
  if (wx.$.u.isArrayVal(subs)) {
    const occIds = subs.map(i => i.occupationInfo.occId)
    const occNames = subs.map(i => i.occupationInfo.occName)
    const modes = subs.map(i => `${i.occupationInfo.mode}`)
    occupations_v2 = occIds.join(',') || ''
    occupations_v2_name = occNames.join(',') || ''
    if (modes.includes('2')) {
      occupations_type = '招聘'
    } else if (modes.includes('1')) {
      occupations_type = '订单'
    }
  }
  const sourceName = SOURCE_ID_NAME_DATA[source_id || '26'] || '小程序分享'
  const statistics: any = {
    info_id: `${resumeId || ''}`,
    resume_uuid: `${resumeUuid || ''}`,
    active_status: activeStatusText || '',
    occupations_v2,
    occupations_v2_name,
    occupations_type,
    source: sourceName,
    ...(info || {}),
    ...(buriedPointData || {}),
    ...(oSta || {}),
    source_id: `${source_id || '26'}`,
    pagination: `${pagination || ''}`,
  }
  delete statistics.info
  delete statistics.id
  delete statistics.sourceKey
  delete statistics.uid
  //
  delete statistics.search_result
  delete statistics.post_distance

  wx.$.collectEvent.event('entryWorkerDetails', statistics)
}
