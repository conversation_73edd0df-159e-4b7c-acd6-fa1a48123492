
/** 新ui */
.form-cont {
  display: flex;
  align-items: center;

  .left-cont {
    flex: 1;
  }

  .right-cont {
    color: rgba(0, 0, 0, 0.45);
    margin-left: 8rpx;
  }
}

.content-extra, .content-header {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.content-title {
  font-size: 34rpx;
  font-weight: bold;
}

.section {
  padding: 16rpx 24rpx;
  padding-bottom: 72rpx;
  font-size: 30rpx;

  .form-detail {
    padding: 0;
    background-color: #FFF;
    right: 0;
    bottom: 0;
    left: 0;
    top: 0;
  }

  /** 验证码 */
  .code {
    display: flex;
    justify-content: space-between;
    align-content: center;
  }
}

.improve-recruitment {
  background-color: #fff;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.promoteCityContainer {
  width: 100% !important;
  padding: 0 !important;
}

.picker-tips-ic {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}

.custom-title-class {
  flex-direction: row;
  align-items: center;
}

.custom-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.card-container {
  padding-bottom: 16rpx;
}

.p0-container {
  background-color: #fff;
  border-radius: 16rpx;
  border-bottom: none !important;
  padding: 32rpx 24rpx !important;
}

.p0-title {
  font-size: 34rpx !important;
  font-weight: bold !important;
  color: @text85 !important;
}

.p0-text {
  font-size: 30rpx !important;
  color: @text85 !important;
  font-weight: 400 !important;
}