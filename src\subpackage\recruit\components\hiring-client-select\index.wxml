<view class="container custom-container">
    <view class="title custom-title">{{title}}</view>
    <view class="content custom-content" bindtap="handleSelect">
        <view class="content-text custom-text {{ disabled ? 'disabled' : '' }}" wx:if="{{value.hiringClientName}}">{{value.hiringClientName}}</view>
        <view class="placeholder custom-placeholder" wx:else>{{placeholder}}</view>
        <view class="content-suffix custom-suffix">
            <icon-font type="yp-yp-icon_mbx_vip" size="40rpx" color="rgba(0, 0, 0, 0.25);" class="" />
        </view>
    </view>
</view>