.info-flow {
  position: relative;
  margin: 16rpx 24rpx 0;
  border-radius: 24rpx;
  padding: 24rpx;
}

.complete-content {
  background: linear-gradient(180deg, #e0f2ff 0%, #FFF 96rpx, #FFF 100%);
}

.complete-tip {
  font-size: 34rpx;
  font-style: normal;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  line-height: 48rpx;
  margin-bottom: 8rpx;
}

.complete-text {
  font-size: 26rpx;
  font-style: normal;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 24rpx;
}

.complete-input {
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  line-height: 88rpx;
  background: #f5f7fc;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.attrList-item {
  padding: 0 20rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  color: rgba(0, 0, 0, 0.85);
  height: 64rpx;
  line-height: 64rpx;
  background: #F5F7FC;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.add-icon {
  padding: 0 32rpx;
  margin-right: 0;
}

.input-right {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.tip-gray {
  color: rgba(0, 0, 0, 0.25);
}

.complete-info {
  // margin-bottom: -16rpx;
  margin-right: -16rpx;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  opacity: 1;

  &.hide {
    opacity: 0;
  }
}

.close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
}

.custom-label {
  display: flex;
  // gap: 24rpx;
  flex-wrap: wrap;
  margin: -12rpx;
  margin-top: 12rpx;
}

.custom-label-item {
  flex: 1;
  margin: 12rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 16rpx;
  color: rgba(0, 0, 0, 0.85);
  height: 88rpx;
  line-height: 88rpx;
  background: #F5F7FC;
}

.complete-info-box {
  max-height: 64rpx;
  overflow: hidden;
}
