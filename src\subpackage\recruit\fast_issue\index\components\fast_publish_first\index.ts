import { RootState, storage } from '@/store/index'
import {
  isByAdcodeRegion,
  formatMunicipalityDirectlyArea,
} from '@/utils/helper/location/index'
import { hideLoadTime, tryPromise } from '@/utils/tools/common/index'
import {
  getLocationData,
  getTemplatesByInfoList,
  judgePeculiarByAdcode,
  occV2CopyLogic,
  removeDuplicateClassifyByIds,
} from '../../utils'
import {
  getBasicConfig,
  judgeClassifyType,
  rqCommonButtonFreeStatus,
} from '@/utils/helper/common/index'
import { validator } from '@/utils/tools/index'
import { userLogin } from '../../fastLogin'
import { ClassifyItem, MergedData } from '../../type'
import { changeUserRole } from '@/utils/helper/member/communicate'
import { AllAreasDataItem } from '@/utils/helper/location/type'
import { hrs } from '@/utils/helper/memoized/handleRequestSingleton'
import { getPromoteCityByAddress, getPromoteCityById } from '@/subpackage/recruit/utils/index'

const Bury = wx.$.l.bury()
const cacheDetected = hrs(mobile => wx.$.javafetch['POST/job/v2/manage/job/publish/mobileDetect']({ mobile }))

Component(
  class FastIssue extends wx.$.Component<FastIssue> {
    setInitData() {
      return {
        // 招聘类展示手机号
        // ZPShowTel: false,
        // 是否显示手机号输入框
        showPhone: false,
        // 敏感词警告
        isWarning: '',
        // 为textarea赋值使用
        textareaValue: '',
        // 手机号
        phone: '',
        /** 当前页面已赋值上的地址信息（为了解决全站公用一个地址Model层，其他页面更改了地址信息，导致发布职位页面的地址被清空 */
        haveSetCurrentAddressData: <any>{},
        // 工种的拼接词
        shortAliasStr: '',
        // 城市的拼接词
        shortAliasAddressStr: '',
        // 是否展示输入详情框
        isShowDetailTextArea: false,
        // 号码是否空号限制
        isValidPhone: false,
        // 详情输入框
        content: '',
        // 验证码
        code: '',
        // 快捷的处理的工种标签数据
        haveSelectedClassGroup: [],
        // 快捷的已选择的工种标签
        quickOccValue: [],
        // 是否打开过工种选择器
        isOpenClassifyPop: false,
        // 快捷工种，是否停止拼接
        isStopCombine: false,
        /** 授权手机号code */
        jsCode: '',
        /** 当前是否已在页面登录过了 */
        currentLogin: false,
        /** 当前页面已赋值上的地址信息（为了解决全站公用一个地址Model层，其他页面更改了地址信息，导致发布职位页面的地址被清空 */
        currentAddress: <any>{},
        lastOne: '',
        /** 标题值 */
        title: '',
        /** 实时发布职位的文案 1-发布职位 2-免费发布职位 3-下一步  */
        publishBtnStatusTxt: '发布职位',
        /** 实时发布职位的状态（ 是否免费- 1-不免费 / 2- 免费；3-是否展示 招聘类的下一步) */
        publishBtnStatus: 1,
        /** 是否显示 标题 */
        titleVisible: false,
        /** 验证码的密钥 */
        verifyToken: '',
        /** 用户是否复制过  */
        copyId: 0,
        /** 是否有推荐数据 */
        showQuickTags: false,
        /** 撑开logo的高度 */
        minHeight: 0,
        /** 招聘类型 */
        recruitType: 1,
        /** 职位结构化模板 */
        templates: {},
        /** 原始模板数据 */
        templateSource: [],
        /** 是否展示验证码 */
        showVerifyCode: false,
        /** 推广城市 */
        promoteCities: [],
        /** 是否展示推广城市 */
        promoteCityVisible: false,
      }
    }

    classifies = ''

    properties = {
      classifyShowRecommend: {
        type: Boolean,
        value: false,
      },
    }

    lifetimes = {
      attached() {},
      created() {},
      /** invoked after page-load */
      async ready() {
        // 撑开底部logo高度
        this.createSelectorQuery()
          .select('.logo-footer')
          .boundingClientRect()
          .exec(res => {
            const logoHeight = wx.$.u.getObjVal(res, '0.height', 0)
            this.createSelectorQuery()
              .select('.footerBox')
              .boundingClientRect()
              .exec(boxRes => {
                const height = wx.$.u.getObjVal(boxRes, '0.height', 0)
                const systemInfo = wx.$.u.sInfo()
                const { screenHeight } = systemInfo.systemInfo
                const { statusBarHeight, menuRect, menuPadding } = systemInfo
                const topHeight = (menuRect ? menuRect.height : 0) + menuPadding * 2
                const minHeight = screenHeight
                  - statusBarHeight
                  - height
                  - topHeight
                  - logoHeight
                  - 8
                this.setData({ minHeight })
              })
          })
        const pages: Array<any> = getCurrentPages()
        this.pageQuery = pages.pop().options || {}

        /** 获取发布页上一级的路由存到缓存 */
        const path = pages[pages.length - 2]
          ? pages[pages.length - 2].route || ''
          : ''
        /** flag(modify2publish) 定价系统1.2，地区信息会被提交到model直接使用，不走初始化逻辑 */
        this.isInitRenderAddress = !!this.pageQuery.flag && this.pageQuery.flag !== 'contact_limit'
        /** 存储登陆状态 */
        this.loginStatus = storage.getItemSync('userState').login
        /** 是否是短信注册发布入口 */
        this.isSMSPublish = this.pageQuery.occV2 && !this.pageQuery.flag
        /** 特殊小程序，并且符合条件的用户跳转到 发布流程2 */
        const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
        if (ENV_IS_TT || ENV_IS_WEAPP) {
          wx.login({
            success: response => {
              this.jsCode = response.code
            },
          })
        }
        /** 初始化Data数据 */
        let newData = <Partial<MergedData<FastIssue>>>{
          isOpenClassifyPop: !!this.pageQuery.occV2,
          prevUrl: path,
          publishBtnStatusTxt:
            newIssueJobConfig && newIssueJobConfig.publishButton
              ? '免费发布职位'
              : '发布职位',
          publishBtnStatus:
            newIssueJobConfig && newIssueJobConfig.publishButton ? 2 : 1,
        }

        if (this.pageQuery.flag && this.pageQuery.flag !== 'contact_limit') {
          const contentInfo = this.getContentData(this.pageQuery.content || '')
          newData = { ...newData, ...contentInfo }
          newData.textareaValue = this.pageQuery.content || ''
          newData.isShowDetailTextArea = true
          newData.title = this.pageQuery.title || ''
          newData.isStopCombine = true

          this.setData(newData)

          /** 初始化地区 */
          await this.getQueryDefaultArea()
          /** 初始化城市 */
          await this.getQueryDefaultOcc()
        } else {
          /** 初始化数据 */
          this.setData(newData)
        }

        /** flag(lifetimeReady) ready 函数执行完毕 */
        this.handlePageShow(true)
        this.lifetimeReady = true
      },

      detached() {
        storage.remove('fastRecruitPerfect')
        storage.setItemSync('isChooseFromClassifyPop', false)
        cacheDetected.detach()
      },
    }

    pageLifetimes = {
      show() {
        /** @warn 因为此方法受PageShow影响，可能会在 lifetimes.ready 之前执行，所以新增字段控制 */
        if (this.lifetimeReady) {
          this.handlePageShow()
        }
      },
    }

    handlePageShow(isLoad?: true) {
      /** 数据初始化 */
      this.initData(isLoad)
      this.forceSetFormAddress()
    }

    forceSetFormAddress() {
      wx.$.selectComponent.call(this, '#formArea').then(widget => {
        const formData = widget.getValues()
        const areaId = wx.$.u.getObjVal(formData, 'current_area.id')
        if (
          !areaId
          && Object.keys(this.data.haveSetCurrentAddressData).length
        ) {
          widget.setValues(this.data.haveSetCurrentAddressData)
        }
      })
    }

    /**
     * 从路由上获取地址数据
     */
    async getAddressByRouteParams() {
      const { adCode, areaId, address, location } = this.pageQuery // must not be null
      const areaData = await wx.$.l.getAreaById(areaId)
      const selectCity = areaData.city || areaData.province
      return {
        id: areaId || '',
        adcode: adCode || areaData.current.ad_code || '',
        name: address || '',
        city: selectCity ? selectCity.name : '',
        address,
        selectArea: {
          id: areaId,
          adcode: adCode || '',
          location,
          areaInfo: areaData.current,
          name: address,
          address,
        },
        selectCity,
      }
    }

    /**
     * 从路由上获取工种数据
     */
    async getClassifiesByRouteParams(): Promise<ClassifyItem[]> {
      const { occV2: occV2String = '' } = this.pageQuery
      /** 从路由上获取工种 */
      const occV2Ids = occV2String.split(
        occV2String.indexOf(',') >= 0 ? ',' : '-',
      )
      return removeDuplicateClassifyByIds(occV2Ids)
    }

    /** 根据路由参数，默认选中城市 */
    async getQueryDefaultArea() {
      const defaultArea = await this.getAddressByRouteParams()
      Bury.then(bury => {
        bury.init({
          data: JSON.stringify(defaultArea),
          functionName: 'getQueryDefaultArea',
          name: '从路由上初始化地址数据',
        })
      })
      /** 如果获取不到定位或者地址异常或者 获取到的地址是港澳台，则回显为空 */
      const isPeculiarArea = isByAdcodeRegion(
        defaultArea.adcode || '',
        'peculiar',
      )
      if (
        defaultArea.address != 'undefined'
        && defaultArea.address
        && !isPeculiarArea
      ) {
        wx.$.selectComponent
          .call(this, '#formArea')
          .then(widget => widget.setValues({ current_area: defaultArea }))
        // const addressStr = await formatCityAddressStr(defaultArea)
        this.setData({
          haveSetCurrentAddressData: defaultArea,
          shortAliasAddressStr: `${defaultArea.address}`,
          currentAddress: defaultArea,
        })
      } else {
        wx.$.msg('未获取到地址信息，请手动选择')
      }
    }

    /** 从路由参数上初始化工种 */
    async getQueryDefaultOcc() {
      const { flag, isShare } = this.pageQuery
      const isShowServicePrivacyV5 = storage.getItemSync(
        'isShowServicePrivacyV5',
      )
      /** 后续不执行~ */
      if (isShowServicePrivacyV5) {
        return
      }

      const classifies = await this.getClassifiesByRouteParams()
      Bury.then(bury => {
        bury.init({
          data: JSON.stringify(classifies),
          functionName: 'getQueryDefaultOcc',
          name: '从路由上初始化工种数据',
        })
      })
      if (classifies.length) {
        /** 招聘类工种展示电话号码 */
        // this.judgeZPShowTel(classifies)
        let orderOccV2 = classifies.filter(item => Number(item.mode) === 1)

        if (isShare) {
          orderOccV2 = classifies
        }

        if (!orderOccV2.length) {
          this.onClickClassItem({
            currentTarget: { dataset: { item: classifies[0] } },
          })
          /** 刷新发布按钮状态 */
          this.rqPublishBtnStatus()
          return
        }
        /** 获取可选工种数量配置 */
        const { basicConfig = <any>{} } = <MergedData<FastIssue>> this.data
        const orderOccNum = basicConfig ? basicConfig.orderOccNum : undefined
        /** 限制回显工种数 */
        orderOccV2 = orderOccNum
          ? orderOccV2.slice(0, orderOccNum)
          : orderOccV2

        const shortAliasStr = Array.isArray(orderOccV2)
          ? orderOccV2.map(item => item.name).join('，')
          : ''

        const { shortAliasAddressStr } = this.data

        const content = shortAliasAddressStr
          ? `${shortAliasAddressStr}招${shortAliasStr}`
          : ''
        /** 拼接详情 */
        const quickOccValue = Array.from(orderOccV2)

        this.setData(<Partial<MergedData<FastIssue>>>{
          quickOccValue,
          isOpenClassifyPop: true,
          shortAliasStr,
          isShowDetailTextArea: !!shortAliasStr || isShare,
          /** 走flag(modify2publish)逻辑时，不拼词 */
          ...(flag && flag !== 'contact_limit'
            ? {}
            : {
              textareaValue: content,
              content,
              isShowDetailTextArea: !!shortAliasStr,
            }),
        })
        /** 刷新发布按钮状态 */
        this.rqPublishBtnStatus()
      }
    }

    /** 复制职位回调 */
    async onCopyInfo(e) {
      let currentArea = <any>{}
      /** 历史招工数据 */
      const copied = e.detail || {}
      /** 是否是港澳台地区 */
      let isPeculiarArea = false
      if (
        copied
        && copied.historyIssueAddress
        && copied.historyIssueAddress.areaId
      ) {
        currentArea = await transformHistoryAddress(copied.historyIssueAddress)
        isPeculiarArea = isByAdcodeRegion(
          currentArea?.adcode || '',
          'peculiar',
        )
        if (
          currentArea.address
          && currentArea.address != 'undefined'
          && !isPeculiarArea
        ) {
          wx.$.selectComponent.call(this, '#formArea').then(comp => comp.setValues({
            current_area: currentArea,
          }))
          this.setData({
            haveSetCurrentAddressData: currentArea,
            currentAddress: currentArea,
          })
        } else {
          wx.$.msg('未获取到地址信息，请手动选择')
        }
      }
      /** 复制工种 */
      const formatOccArr = await occV2CopyLogic(copied.occV2)
      const newOccIdList = await wx.$.l.transformClsIdHidsByOccV2(
        formatOccArr || [],
      )
      const promoteCity = await getPromoteCityById(copied.promoteCityId)
      /**  */
      const occIds = newOccIdList.map((item: any) => item.id || '')
      const classifies = await wx.$.l.getClassifyByIds(
        occIds,
        this.data.recruitType,
      )
      const isCanMontage = await judgeClassifyType(
        classifies,
        this.data.quickOccValue,
      )
      const title = copied.title
        ? String.prototype.slice.call(copied.title, 0, 45)
        : ''
      const setObj: any = {
        textareaValue: copied.detail,
        content: copied.detail,
        isShowDetailTextArea: true,
        quickOccValue: newOccIdList,
        isOpenClassifyPop: true,
        copyId: copied.id || 0,
        title: !isCanMontage ? title : '',
        isStopCombine: true,
        promoteCities: promoteCity ? [promoteCity] : [],
      }
      setObj.recruitType = copied.recruitType || 1
      if (
        currentArea.address != 'undefined'
        && currentArea.address
        && !isPeculiarArea
      ) {
        setObj.shortAliasAddressStr = currentArea.address
      } else {
        wx.$.collectEvent.event('publishSuperAddressError', {
          showAddress: JSON.stringify(currentArea),
          functionName: 'onCopyInfo',
        })
      }
      Bury.then(bury => {
        bury.operation({
          data: JSON.stringify(setObj),
          functionName: 'onCopyInfo',
          name: '复制招工',
        })
      })

      // 复制详情
      this.setData(setObj)
    }

    /** 敏感词 */
    onKeyChange(e) {
      this.setData({
        isWarning: e.detail,
      })
    }

    async initData(isLoad?: true) {
      try {
        // 隐私协议未同意时，不执loading （处理生产loading状态隐私协议弹窗点不了）
        wx.$.loading('加载中...')
        hideLoadTime()
        const { content,
          basicConfig,
          showPhone,
          code,
          bindTel,
          loginStatus,
          phone } = <MergedData<FastIssue>> this.data
        /** 登录页返回-登录状态变更 */
        if (!this.loginStatus && loginStatus) {
          changeUserRole(1)
        }
        /** 页面初始化 || 切换登录状态 */
        if (isLoad || (!this.loginStatus && loginStatus)) {
          let newPhone
          if (content) {
            const matched = validator.matchContentPhone(content)
            newPhone = phone || matched
          }
          /**
           * 1. 继承手机号展示状态
           * 2. 用户输入手机号且手机号与绑定手机号不相同
           * */
          const newShowPhone = newPhone && newPhone !== bindTel

          const newData = {
            textareaValue: content || '',
            content: content || '',
            showPhone: newShowPhone || showPhone,
            code: newShowPhone ? code : '',
            showVerifyCode: newPhone && newPhone !== bindTel,
          }
          if (!showPhone) {
            newData.phone = phone || bindTel || ''
          }
          this.setData(newData)
          /** 检测登录号码是否空号 */
          if (loginStatus) {
            this.mobilePrivateDetect(newData.phone, 'isShowSpace')
          }
          /** 获取发布的基础配置(敏感词,订单类/招聘类工种配置等) */
          if (!basicConfig || !Object.keys(basicConfig).length) {
            getBasicConfig({ isAll: true })
          }
          /** 判断用户发布是否免费和是否配置完善项 */
          this.rqPublishBtnStatus()
          /** 获取--新流程发布职位基础--配置项 */
          this.commonHandleNewJobConfig()
        }
      } catch (error) {
        console.warn('[INIT ERROR]', error)
      }
      wx.hideLoading()
    }

    /** 选择地区 */
    async changeCityLocation({ detail: { value } }) {
      const { shortAliasStr, isStopCombine, promoteCityVisible } = <MergedData<FastIssue>> this.data
      if (!value.adcode || !value) return
      const addressStr = await formatCityAddressStr(value)
      Bury.then(bury => {
        bury.operation({
          data: JSON.stringify(value),
          functionName: 'changeCityLocation',
          name: '选择地区',
        })
      })
      // 先设置当前页面的选中的值（避免onShow回显的时候没附上值)
      const newData = <any>{
        currentAddress: value,
        shortAliasAddressStr: `${addressStr}`,
      }
      const promoteCity = await getPromoteCityByAddress(value)
      newData.promoteCities = promoteCityVisible ? [promoteCity] : []
      const trades = (await wx.$.selectComponent
        .call(this, '#formArea')
        .then(widget => widget.getValues()?.trades)) || []
      const isCanMontage = trades && trades.length ? trades.some(item => item.mode == 1) : false
      // 订单类- 若开启拼词，可拼词； 招聘类- 不可拼词，直接清空详情。
      if (addressStr && shortAliasStr && !isStopCombine) {
        if (isCanMontage) {
          newData.textareaValue = `${addressStr}招${shortAliasStr}`
          newData.content = `${addressStr}招${shortAliasStr}`
        } else {
          newData.textareaValue = ''
          newData.content = ''
        }
      }
      this.setData(newData)
      // 选择城市后，实时判断
      this.rqPublishBtnStatus()
    }

    async onAgreeOk() {
      this.isInitRenderAddress = !!this.pageQuery.flag && this.pageQuery.flag !== 'contact_limit'
      this.initAddress()
    }

    async checkGPSAddr() {
      const locationArea = await getLocationData()
      const validLoc = locationArea && (locationArea.city || locationArea.name)
      if (validLoc) {
        return locationArea
      }

      return undefined
    }

    async checkHisAddr() {
      const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
      const hisAddr = newIssueJobConfig
        ? newIssueJobConfig.historyIssueAddress
        : {}
      if (Object.keys(hisAddr).length > 0) {
        const isLocMap = hisAddr.location && !!hisAddr.location.longitude
        const locFormat = isLocMap
          ? `${hisAddr.location.longitude},${hisAddr.location.latitude}`
          : <string>hisAddr.location
        const areaObj = await wx.$.l.getAreaById(hisAddr.areaId)
        const newSelectCity = <AllAreasDataItem>(
          (areaObj.city || areaObj.province)
        )
        const addr = <any>{
          ...hisAddr,
          id: hisAddr.areaId,
          adcode: hisAddr.adCode,
          location: locFormat,
          name: hisAddr.address || '',
        }

        if (newSelectCity.name || newSelectCity.ad_name) {
          addr.city = newSelectCity.name || newSelectCity.ad_name
        }
        const newSelectArea = {
          ...areaObj.current,
          location: locFormat,
          adcode: hisAddr.adCode,
          name:
            hisAddr.address || (<AllAreasDataItem>areaObj.current).name || '',
          address: hisAddr.detailedAddress || '',
        }

        addr.selectCity = newSelectCity
        addr.selectArea = newSelectArea

        return addr
      }

      return undefined
    }

    /** 地址初始化 */
    async initAddress() {
      const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
      let showAddress: any
      const isGetGps = newIssueJobConfig.isGps == 1 && !newIssueJobConfig.historyIssueAddress
      if (newIssueJobConfig.isGps == 2 || isGetGps) {
        showAddress = (await this.checkGPSAddr()) || {}
      } else {
        showAddress = (await this.checkHisAddr()) || {}
      }
      wx.$.collectEvent.event('FastIssueInitData', {
        data: JSON.stringify(showAddress),
        isGPS: newIssueJobConfig.isGps == 2 || isGetGps,
        functionName: 'initAddress',
        name: '根据【GPS|历史发布】初始化地址数据',
        historyIssueAddress: JSON.stringify(
          newIssueJobConfig.historyIssueAddress,
        ),
        serial: 2,
      })

      const { promoteCityVisible } = this.data
      const promoteCities = []
      if (promoteCityVisible) {
        const promoteCity = await getPromoteCityByAddress(showAddress)
        promoteCity && promoteCities.push(promoteCity)
        this.setData({ promoteCities })
      }

      // wx.$.collectEvent.event('publishInitAddress', {
      //   isGetGps,
      //   showAddress: JSON.stringify(showAddress),
      //   isGps: newIssueJobConfig.isGps,
      //   historyIssueAddress: JSON.stringify(newIssueJobConfig.historyIssueAddress),
      //   serial: 1,
      // })

      // 如果获取不到定位或者地址异常或者 获取到的地址是港澳台，则回显为空。
      const isPeculiarArea = showAddress.adcode
        && judgePeculiarByAdcode(showAddress.adcode as string)
      // 是否能获取 areaId
      const locationAreaId = wx.$.u.getObjVal(showAddress, 'selectArea.areaInfo.id')
        || wx.$.u.getObjVal(showAddress, 'selectCity.id', '')
      //  未知地址
      if (
        (showAddress.address === 'undefined'
          && showAddress.name === 'undefined')
        || (!showAddress.address && !showAddress.name)
        || isPeculiarArea
        || !locationAreaId
      ) {
        this.isInitRenderAddress = true

        this.setData({
          shortAliasAddressStr: '',

        })
        this.getQueryDefaultOcc()
        // wx.$.collectEvent.event('publishSuperAddressError', {
        //   showAddress: JSON.stringify(showAddress),
        //   functionName: 'initAddress',
        //   type: 'reset_init',
        // })
        return
      }

      wx.$.selectComponent.call(this, '#formArea').then(widget => widget.setValues({
        current_area: showAddress,
      }))
      const addressStr = await formatCityAddressStr(showAddress)
      this.isInitRenderAddress = true
      if (!addressStr) {
        try {
          wx.$.collectEvent.event('publishSuperAddressError', {
            showAddress: JSON.stringify(showAddress),
            functionName: 'initAddress',
          })
        } catch (error) {
          /* empty */
        }
      }
      this.setData({
        shortAliasAddressStr: `${addressStr}`,
        haveSetCurrentAddressData: showAddress,
        currentAddress: showAddress,
      })

      this.getQueryDefaultOcc()
    }

    /** 选择工种 */
    async onChooseClassify(e) {
      const { value } = e.detail

      this.trades = value
      this.refreshTemp()
      /** 快捷工种点击上报 */
      Bury.then(bury => {
        bury.operation({
          data: JSON.stringify(value),
          functionName: 'onChooseClassify',
          name: '选择工种',
        })
      })
      const { shortAliasAddressStr, isStopCombine } = <MergedData<FastIssue>>(
        this.data
      )
      const isChooseCombine = storage.getItemSync('isChooseFromClassifyPop')
      const occArr = Array.isArray(value)
        ? value.map((item: any) => {
          return item?.id || ''
        })
        : []

      this.setData({ currentClassifyIds: occArr })

      const classify = await wx.$.l.getClassifyByIds(
        occArr,
        this.data.recruitType,
      )
      this.classifies = value
      // this.judgeZPShowTel(classify)
      // 订单类- 若开启拼词，可拼词； 招聘类- 不可拼词，直接清空详情。
      const isCanMontage = await judgeClassifyType(
        classify,
        this.data.quickOccValue,
      )
      if (isChooseCombine && !isStopCombine) {
        if (value?.length > 0) {
          const shortAliasList = value.map(item => item.name)
          if (isCanMontage) {
            let content = ''
            if (shortAliasAddressStr) {
              content = `${shortAliasAddressStr}招${shortAliasList.join('，')}`
            }

            this.setData({
              shortAliasStr: shortAliasList.join('，'),
              textareaValue: content,
              content,
            })
          } else {
            this.setData({
              shortAliasStr: shortAliasList.join('，'),
              textareaValue: '',
              content: '',
            })
          }
        }
      }
      // 选择工种后，实时判断
      this.rqPublishBtnStatus()
    }

    /** 表单的onChage事件 */
    async onChange({ detail: { value } }) {
      // 因为城市选择器，在空地址的情况下，默认有 北京市的数据。
      if (value?.trades?.length > 0) {
        !this.data.isShowDetailTextArea
          && Bury.then(bury => {
            bury.operation({
              data: '展示详情输入框',
              functionName: 'onChange',
              name: '展示详情输入框',
            })
          })
        this.setData({
          isShowDetailTextArea: true,
        })
      }
    }

    /**
     * 抽离对文本的处理
     * @param content
     */
    getContentData(value: string) {
      const phone = validator.matchContentPhone(value)
      const { showPhone, bindTel, loginStatus, code } = <MergedData<FastIssue>>(
        this.data
      )
      if (phone) {
        const newShowPhone = phone !== bindTel
        return {
          phone,
          showPhone: newShowPhone || showPhone,
          showVerifyCode: newShowPhone,
          content: value,
          textareaValue: value,
          isStopCombine: true,
          code: newShowPhone ? code : '',
        }
      }
      if (!loginStatus && !showPhone) {
        return { content: value, textareaValue: value, isStopCombine: true }
      }
      return { content: value, textareaValue: value, isStopCombine: true }
    }

    onInput({ detail, target }) {
      const { value } = detail
      const { name } = target.dataset
      if (name === 'phone') {
        console.log(value)
      }

      if (name === 'content') {
        const newData = <any> this.getContentData(value)
        if (
          this.data.phone
          && newData.phone
          && newData.phone !== this.data.phone
        ) {
          if (this.data.verifyToken) {
            newData.verifyToken = ''
          }
          if (this.data.code) {
            newData.code = ''
          }
        }
        this.setData(newData)
        if (newData.phone) {
          this.mobilePrivateDetect(newData.phone, 'isShowSpace')
        }
      } else if (
        name === 'phone'
        && this.data.phone
        && value.length === 11
        && value !== this.data.phone
      ) {
        this.setData({
          [name]: value,
          verifyToken: '',
          code: '',
        })
      } else {
        this.setData({
          [name]: value,
        })
      }
    }

    /** 手机号码输入框的失焦监听 */
    async onPhoneBlurEvent(e) {
      const { value } = e.detail
      await this.mobilePrivateDetect(value)
      const { bindTel, isValidPhone, code } = <MergedData<FastIssue>> this.data
      this.setData({
        // showPhone: isValidPhone || (value && value != bindTel),
        code: isValidPhone || (value && value != bindTel) ? code : '',
        showVerifyCode: isValidPhone || (value && value != bindTel),
      })
    }

    /** 特殊情况地址的处理 */
    async specialAddressData(current_area, s_area, s_city) {
      return new Promise<any>(async resolve => {
        let new_selectArea = s_area
        let new_selectCity = s_city
        if (s_area && Object.keys(s_area).length == 0 && current_area) {
          const areaObj = await wx.$.l.getAreaById(current_area.area_id)
          new_selectCity = areaObj.city || areaObj.province
          new_selectArea = {
            ...areaObj.current,
            location: current_area.location,
            adcode: current_area.adcode,
            name: current_area.name,
            district: '',
            address: '',
          }
        }
        resolve({ new_selectArea, new_selectCity })
      })
    }

    /** 空号检测 */
    async mobilePrivateDetect(phoneNumber?: any, type?: any) {
      const { phone } = this.data
      const mobile_phone = phoneNumber || phone
      if (mobile_phone && validator.isPhone(mobile_phone)) {
        const response = await tryPromise(cacheDetected(mobile_phone))
        // 是否异常：true=是；false=否；
        if (response.code == 0) {
          const obj: any = <Partial<MergedData<FastIssue>>>{
            isValidPhone: response?.data?.hasException,
          }
          if (type == 'isShowSpace' && response?.data?.hasException) {
            obj.showPhone = true
            obj.showVerifyCode = true
          }
          this.setData(obj)
        }
      }
    }

    /** 职位详情的焦点获取事件 */
    onTextAreaFocus() {
      this.setData({ isStopCombine: true })
    }

    /**
     * 处理服务端配置
     * @returns
     */
    async commonHandleNewJobConfig() {
      const { haveSelectedClassGroup, newIssueJobConfig: configData } = <
        MergedData<FastIssue>
      > this.data
      // 位置信息已经初始化，无需在此执行
      if (this.isInitRenderAddress) {
        return
      }
      // 短信注册发布-不走推荐、历史工种
      if (this.isSMSPublish) {
        this.initAddress()
        return
      }
      let newOccList = []
      // 如果已经选择过快捷标签
      if (haveSelectedClassGroup.length > 0) {
        newOccList = wx.$.u.deepClone(haveSelectedClassGroup)
      } else if (configData.newUser) {
        // 新用户- 推荐工种-- 快捷选择
        newOccList = await getRecommendClassifies()
      } else {
        // 老用户- 历史工种-- 快捷选择
        newOccList = await getHistoryClassifies()
      }
      const showQuickTags = Object.values(newOccList).some(
        item => !!item.occupations.length,
      )

      this.setData({
        haveSelectedClassGroup: newOccList,
        showQuickTags,
      })
      await this.initAddress()
    }

    /** 选择工种的快捷标签-单击
   *  ①单次点击为选中并展示选中的样式，双次点击为取消选中
      ②订单类可以多选（最多选择x个），如果选择二级工种button数量＞x（新增订单类个数配置），toast提示：最多选择x个工种（toast2秒后消失）
      ③其他行业工种只能单选。已选中其他行业某个工种，又点击第二个时，自动替换为最新的选择项
      ④先选中建筑工程，又点击其他行业（或先选中其他行业又选建筑工程）工种时，清空之前的选择项，并选中当前选择的。
  */
    async onClickClassItem(e) {
      const { item: selected } = e.currentTarget.dataset
      // this.judgeZPShowTel([selected])

      const { haveSelectedClassGroup = [],
        quickOccValue = [],
        basicConfig,
        shortAliasAddressStr,
        isStopCombine,
        isShowDetailTextArea,
        lastOne } = <MergedData<FastIssue>> this.data

      // 已选择的数据
      let quickOccValueList = [...quickOccValue]
      /** 工种类型冲突 */
      const modeConflict = quickOccValueList
        && quickOccValueList.length
        && quickOccValueList.some(({ mode }) => mode != selected.mode)
      if (modeConflict) {
        wx.$.msg(
          Number(selected.mode) === 1 ? '以下工种可以多选' : '以下工种只能单选',
        )
        /** 如果工种类型发生了切换，直接清空所选内容，改用所选值 */
        quickOccValueList = [{ ...selected, isSelected: true }]
        /** 此工种未被选中 */
      } else if (!selected.isSelected) {
        /** 选中类型为招聘 */
        if (Number(selected.mode) === 2) {
          quickOccValueList = [{ ...selected, isSelected: true }]
          /** 选中为订单类且超出个数限制 */
        } else if (
          quickOccValueList.length > 0
          && basicConfig?.orderOccNum > 0
          && quickOccValueList.length >= basicConfig.orderOccNum
        ) {
          wx.$.msg(`最多选择${basicConfig.orderOccNum}个工种`, 2000)
        } else {
          quickOccValueList.push({ ...selected, isSelected: true })
        }
        /** 此工种已选中 */
      } else {
        quickOccValueList.splice(
          quickOccValueList.findIndex(item => item.id === selected.id),
          1,
        )
      }

      const occIds = quickOccValueList.map(item => item.id)
      /** 获取去重后的工种，且该工种在工种树上 */
      const classifies = await removeDuplicateClassifyByIds(occIds)

      if (
        quickOccValueList.length > 0
        && classifies.length < quickOccValueList.length
      ) {
        /** 如果工种树上没有该工种，则不更新 */
        quickOccValueList = classifies
        wx.$.msg('工种暂未开放')
        return
      }
      /** 更新推荐工种列表数据 */
      const newSection = haveSelectedClassGroup.map(section => {
        if (!section || !Array.isArray(section.occupations)) return section
        return {
          ...section,
          occupations: section.occupations.map(item => ({
            ...item,
            isSelected: quickOccValueList.some(
              quickOcc => quickOcc.id === item.id && quickOcc.name === item.name,
            ),
          })),
        }
      })
      /** 获取工种拼词，在清空工种选择时，回显最后一个值 */
      const quickStr = classifies.map(item => item.name).join('，') || lastOne
      const isCanMontage = await judgeClassifyType(
        quickOccValueList,
        quickOccValue,
      )

      const newData = <any>{
        haveSelectedClassGroup: newSection,
        quickOccValue: quickOccValueList,
        isShowDetailTextArea: isShowDetailTextArea || !!quickStr,
      }

      /** 保存最后去除的工种的名称 */
      if (classifies.length === 1) {
        newData.lastOne = classifies[0].name
      }
      if (!isStopCombine) {
        newData.shortAliasStr = quickStr
        if (isCanMontage) {
          newData.textareaValue = `${shortAliasAddressStr}招${quickStr}`
          newData.content = `${shortAliasAddressStr}招${quickStr}`
        } else {
          newData.textareaValue = ''
          newData.content = ''
        }
      }

      if (this.pageQuery && this.pageQuery.flag && this.pageQuery.flag !== 'contact_limit') {
        delete newData.content
        delete newData.textareaValue
        delete newData.isShowDetailTextArea
      }
      this.setData(newData)
    }

    /** 打开工种选择器的回调事件 */
    cbOpen(e) {
      const { type } = e.currentTarget.dataset
      if (type == 'open') {
        // 打开工种选择器
        wx.$.selectComponent
          .call(this, '#trades')
          .then(comp => comp.openClassifyPop())
      }
      this.setData({ isOpenClassifyPop: true })
    }

    /** 带上手机号和验证码去登陆页 */
    toLogin() {
      const query = {
        auth_type: 2,
        phone: this.data.phone,
        code: this.data.code,
      } as any
      wx.$.selectComponent
        .call(this, '#verificationCode')
        .then(widget => {
          const { time } = widget.data
          query.initTime = time
        })
        .catch(() => {
          /* noop */
        })
      wx.$.r.push({
        path: '/subpackage/userauth/auth/index',
        query,
      })
    }

    /* 点击获取电话埋点 */
    buryPoint() {
      wx.$.collectEvent.event('miniPageClick', {
        click_button: '打开组件',
        page_name: '手机快速验证组件',
      })
    }

    /** 展示底部 按钮的 文案及 是否存在下一步-去完善页、
     * 发布职位流程，只有招聘类才请求 是否有完善项的接口
     */
    async rqPublishBtnStatus() {
      await wx.$.selectComponent.call(this, '#formArea').then(async comp => {
        const currValues = comp.getValues()
        const trades = wx.$.u.getObjVal(currValues, 'trades', [])
        const cityId = wx.$.u.getObjVal(currValues, 'current_area.area_id')
          || wx.$.u.getObjVal(currValues, 'current_area.id')
          || wx.$.u.getObjVal(currValues, 'current_area.areaId')
          || ''
        if (cityId && trades.length > 0) {
          // 先判断订单还是招聘类
          const isOrder = await judgeClassifyType(
            trades,
            this.data.quickOccValue,
          )
          const isFree = await this.rqCurrentChosenIsFree(cityId, trades)
          if (!isOrder) {
            const isNext = await this.rqIsCanHaveNextProcess(trades)
            const { templateSource } = this.data
            /** 判断当前工种完善项是否包含jobPerfect分类的完善项 */
            const completable = templateSource.some(
              item => wx.$.u
                .getObjVal(item, 'templateInfo.controlInfoList', [])
                .filter(it => it.jobDisplayPage == 'jobPerfect').length,
            )
            if (isNext && completable) {
              this.setData({
                publishBtnStatusTxt: '下一步',
                publishBtnStatus: 3,
                publishPurchaseStatus: isFree ? 2 : 1,
              })
              return
            }
          }
          this.setData({
            publishBtnStatusTxt: isFree ? '免费发布职位' : '发布职位',
            publishBtnStatus: isFree ? 2 : 1,
            publishPurchaseStatus: isFree ? 2 : 1,
          })
        }
      })
    }

    /** 请求定价--当前城市&工种是否免费 */
    async rqCurrentChosenIsFree(cityId, occArr = []) {
      if (cityId && occArr?.length > 0) {
        const occIds = occArr.map(item => item.id)
        const { recruitButtonFree } = await rqCommonButtonFreeStatus(
          3,
          cityId,
          occIds,
        )
        return recruitButtonFree
      }
      return false
    }

    /** 请求是否去完善页 */
    async rqIsCanHaveNextProcess(occArr = []) {
      const { loginStatus } = <MergedData<FastIssue>> this.data
      if (occArr?.length > 0 && loginStatus) {
        const occIds = occArr.map(item => item.id)
        const res: any = await wx.$.javafetch[
          'POST/job/v3/manage/job/complete/setUpCompleteCheck'
        ]({
          occIds,
          checkScene: 'PUBLISH',
          recruitType: this.data.recruitType,
        })
        if (res && res.code == 0) {
          return res.data?.needSetUpComplete
        }
      }
      return false
    }

    onGetPhoneNumber(e: any) {
      console.error('[debugger]: getPhoneNumber', e)
      wx.$.loading()
      this.loginFn(e).finally(wx.hideLoading)
    }

    async loginFn(e: any) {
      /** 需要上报的错误 */
      const buriedErrorInNeed = [
        'getPhoneNumber:ok',
        'getPhoneNumber:fail user deny',
      ]
      if (buriedErrorInNeed.includes(e.detail.errMsg)) {
        wx.$.collectEvent.event('errorPosition', {
          name: 'EP-needLoginfn',
          ePClickButton:
            e.detail.errMsg === 'getPhoneNumber:ok' ? '点击允许' : '点击拒绝',
          ePTel: this.data.phone,
        })
      }

      return userLogin(
        e,
        async (_, userState) => {
          const tel = wx.$.u.getObjVal(userState, 'tel', '')
          const { phone, code } = this.data
          if (!tel) {
            return Promise.reject('[AFTER_LOGIN]: 未获取到绑定手机号')
          }
          this.setData({
            phone: phone || tel,
            // showPhone: phone && (phone !== tel),
            // code: (phone && (phone !== tel)) ? code : '',
            showVerifyCode: phone && phone !== tel,
          })
          this.loginStatus = true
          changeUserRole(1)
          await Promise.all([
            getBasicConfig({ isAll: true }),
            this.rqPublishBtnStatus(),
            this.mobilePrivateDetect(phone || tel, 'isShowSpace'),
            this.refreshTemp(),
          ])
          return this.checkCompliance()
        },
        this.data.jsCode,
      ).finally(() => {
        this.setData({
          jsCode: '',
        })
      })
    }

    /** 调用页面的合规词校验方法 */
    checkCompliance() {
      this.triggerEvent('checkCompliance', this.data)
    }

    /** 获取验证码- 回调事件 */
    onCallBackVerifyToken(e) {
      if (e.detail) {
        this.setData({ verifyToken: e.detail })
      }
    }

    async onSubmit() {
      await wx.$.u.waitAsync(this, this.onSubmit, [], 300)
      /** 点击下一步--埋点 */
      const { publishBtnStatusTxt } = this.data
      if (publishBtnStatusTxt == '下一步') {
        wx.$.collectEvent.event('post_job_next_step_click', {
          button_name: '下一步',
        })
      }
      /** 开放标签唤起登录时，可以点击到协议页导致校验按钮失效导致发布接口异常，这里做一层兜底 */
      await this.rqPublishBtnStatus()
      this.triggerEvent('submit', { ...this.data })
    }

    async onNavBack() {
      await wx.$.u.waitAsync(this, this.onNavBack, [], 300)
      this.triggerEvent('navback', {})
    }

    onChooseRecruitType(e) {
      const { type } = e.currentTarget.dataset
      this.setData({
        recruitType: Number(type || 1),
      })
      this.refreshTemp()
    }

    async refreshTemp() {
      if (!this.trades || !this.trades.length) return
      const { recruitType, showPhone } = this.data
      const occInfoList = this.trades.map(it => ({ occId: it.id }))
      const { templates, source } = await getTemplatesByInfoList(
        occInfoList,
        recruitType,
      )

      const isLogin = !!storage.getItemSync('userState').login
      /** 未登录时 即使配置需要展示手机号也不能展示手机号 */
      const telTempUsable = isLogin
        ? wx.$.u.getObjVal(templates, 'tel.status', false)
        : false
      const titleVisible = wx.$.u.getObjVal(
        templates,
        'jobTitle.status',
        false,
      )

      const promoteCityVisible = !!wx.$.u.getObjVal(
        templates,
        'promoteCity.status',
        false,
      )
      const { currentAddress = {} } = this.data

      const { promoteCities = [] } = this.data
      const promoteCity = await getPromoteCityByAddress(currentAddress)
      if (promoteCity && !promoteCities.length) {
        promoteCities.push(promoteCity)
      }

      this.setData({
        templates,
        showPhone: telTempUsable || showPhone,
        templateSource: source,
        titleVisible,
        promoteCityVisible,
        promoteCities: promoteCityVisible ? promoteCities : [],
      })
    }

    observers = {
      /** 未输入手机号且绑定手机号存在时默认填入绑定手机号，兼容 */
      async bindTel() {
        if (this.data.bindTel && !this.data.phone) {
          this.setData({
            phone: this.data.bindTel,
          })
          await this.mobilePrivateDetect(this.data.bindTel, 'isShowSpace')
        }
        /** 两个手机号一致，且校验通过 */
        if (
          this.data.bindTel
          && this.data.phone
          && this.data.bindTel == this.data.phone
          && !this.data.isValidPhone
        ) {
          this.setData({
            showVerifyCode: false,
          })
        }
      },
    }

    /** 推广城市选择变化 */
    onPromoteCityChange(e) {
      const { value } = e.detail
      this.setData({
        promoteCities: value || [],
      })
    }

    useStore(state: RootState) {
      return {
        /** 当前用户是否登录 */
        loginStatus: state.storage.userState.login,
        /** 用户绑定手机号 */
        bindTel: state.storage.userState.tel,
        /** 发布招工管理后台配置 */
        newIssueJobConfig: state.recruitFastIssue.newIssueJobConfig,
        /** 城市数据  */
        areaTree: state.storage.areaTree,
        /** 基础配置2 */
        basicConfig: state.config.basicConfig,
      }
    }

    /** 登录状态 */
    loginStatus = false

    /** 路由参数 */
    pageQuery = <Record<string, any>>{}

    /** 是否渲染地址 */
    isInitRenderAddress = false

    isSMSPublish = false

    /** 保证show方法在ready方法执行完成后再执行 */
    lifetimeReady = false

    /** 选择的工种 */
    trades = []
  },
)

/** 将历史记录的address 转换成 地址选择器可用的地址 */
const transformHistoryAddress = async function (address: any) {
  const { areaId, location, adCode: adcode = '', address: name = '' } = address
  const areaData = await wx.$.l.getAreaById(areaId)
  const selectCity = areaData.city || areaData.province
  const d_location = location
  return {
    ...address,
    id: areaId || '',
    adcode,
    name,
    city:
      areaData.city || areaData.province
        ? (areaData.city || areaData.province).name
        : '',
    selectArea: {
      id: areaId,
      adcode,
      name,
      location: `${d_location?.longitude || ''},${d_location?.latitude || ''}`,
      areaInfo: areaData.current,
    },
    selectCity,
  }
}

/** 获取推荐工种 */
async function getRecommendClassifies() {
  const { userId, login } = storage.getItemSync('userState')
  if (!login) {
    return []
  }
  const recRecommendClassifies = storage.getItemSync('recRecommendClassifies')
  const recRecommendclassifyRecommendTime = storage.getItemSync(
    'recRecommendclassifyRecommendTime',
  )
  if (
    recRecommendclassifyRecommendTime
    && wx.$.u.isArrayVal(recRecommendClassifies)
  ) {
    return [
      {
        occupations: recRecommendClassifies.map(occ => ({
          ...occ,
          isSelected: false,
        })),
      },
    ]
  }
  return wx.$.javafetch['POST/labelService/v1/recommended/occNew']({
    userId,
    roleType: 2,
    sceneType: 1,
  }).then(async ({ data }) => {
    const { list } = data || {}
    if (Array.isArray(list)) {
      const ids = list.map(item => item.occId)
      const occupations = await wx.$.l.getClassifyByIds(ids)
      storage.setItemSync(
        'recRecommendclassifyRecommendTime',
        new Date().getTime(),
      )
      storage.setItemSync('recRecommendClassifies', occupations)
      return [
        {
          occupations: occupations.map(occ => ({ ...occ, isSelected: false })),
        },
      ]
    }

    return []
  })
}

/** 获取历史推荐公众 */
async function getHistoryClassifies() {
  return wx.$.javafetch['POST/job/v2/manage/job/publish/historyOcc']()
    .then(({ data }) => {
      return data && Array.isArray(data.list)
        ? [
          {
            occupations:
                data.list.map(item => ({ ...item, isSelected: false })) || [],
          },
        ]
        : []
    })
    .catch(() => {
      return []
    })
}

/** 获取选中城市的拼接字符串 */
async function formatCityAddressStr(value) {
  let addressStr = ''
  const { selectCity, selectArea, city } = value || {}
  const { city_name, ad_name, name, pid } = selectCity || {}
  if (city_name || ad_name || name) {
    const s_str = formatMunicipalityDirectlyArea(pid)
    if (s_str) {
      // 直辖市：重庆
      addressStr += s_str
    } else {
      addressStr += city_name || ad_name || name
    }
  }
  const { areaInfo, ad_name: saad_name, district } = selectArea || {}
  const { ad_name: aiad_name } = areaInfo || {}
  if (aiad_name || saad_name) {
    const areaStr = aiad_name || saad_name || ''
    addressStr += areaStr == addressStr ? '' : areaStr
  } else if (!addressStr && district) {
    addressStr = district
  } else if (!addressStr && city) {
    addressStr = city
  }
  return addressStr
}
