/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 追聊消息修改弹框
 */

import { store } from '@/store/index'
import { handleAction } from '../../midUtils'
import { applyExchange } from '../../utils'

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
    query: { type: Object, value: {} },
  }

  observers = {
  }

  data = {
    // 是否显示换电话
    isExChangeTelShow: false,
    // 是否显示换微信
    isExChangeWechatShow: false,
    // 是否显示添加微信号弹框
    isUpdateWechatShow: false,
    // 传入的发起交换的类型
    crExchangeType: '',
  }

  async onExChangeTelClick() {
    await wx.$.u.waitAsync(this, this.onExChangeTelClick, [], 2000)
    this.onExChangeTel()
  }

  onExChangeTel(isCall = false) {
    const { conversation } = store.getState().timmsg
    const { rightsStatusInfo } = conversation || {}
    const { exchangeTel } = rightsStatusInfo || {}
    const { value, status, forbiddenMsg, hasImRight } = exchangeTel || {}
    if (forbiddenMsg) {
      wx.$.msg(forbiddenMsg)
      return
    }
    if (!isCall && hasImRight == 0 && !value) {
      handleAction.call(this, conversation, {
        type: 'msg',
        chatsend: () => {
          this.onExChangeTel(true)
        },
      })
      return
    }
    this.triggerEvent('close')
    if (status == 1) {
      this.setData({ isExChangeTelShow: true })
    }
    if (status == 3) {
      applyExchange()
    }
  }

  onExChangeTelClose() {
    this.setData({ isExChangeTelShow: false, crExchangeType: '', crCallbakc: null })
  }

  async onExChangeWechatClick() {
    await wx.$.u.waitAsync(this, this.onExChangeWechatClick, [], 2000)
    this.onExChangeWechat()
  }

  onExChangeWechat(isCall = false) {
    const { conversation } = store.getState().timmsg
    const { rightsStatusInfo } = conversation || {}
    const { exchangeWechat } = rightsStatusInfo || {}
    const { value, status, forbiddenMsg, hasImRight } = exchangeWechat || {}
    if (forbiddenMsg) {
      wx.$.msg(forbiddenMsg)
      return
    }
    if (!isCall && hasImRight == 0 && !value) {
      handleAction.call(this, conversation, {
        type: 'msg',
        chatsend: () => {
          this.onExChangeWechat(true)
        },
      })
      return
    }
    this.triggerEvent('close')
    if (status == 1) {
      this.setData({ isExChangeWechatShow: true })
    }
    if (status == 3) {
      applyExchange('EXCHANGE_WECHAT')
    }
  }

  onExChangeWechatClose() {
    this.setData({ isExChangeWechatShow: false })
  }

  /** 抽屉组件已收起 */
  onClose() {
    this.triggerEvent('close')
  }

  onUpdateWechatShow() {
    this.setData({ isUpdateWechatShow: true })
  }

  onUpdateWechatClose() {
    this.setData({ isUpdateWechatShow: false })
  }

  onCrCallback(e) {
    this.triggerEvent('crcallback', e.detail)
  }

  contactBtnText() {
    this.triggerEvent('contactBtnText')
  }
})
