page {
    background-color: #F5F6FA;
  }

  .body {
    background-color: #F5F6FA;
    padding: 0 24rpx 0 24rpx;
    /* padding: 0 24rpx 320rpx 24rpx;*/
    /* min-height: 100vh;*/
  }

  .recruitment {
    background-color: #FFF;
    padding-top: 32rpx;
    border-radius: 16rpx;
    margin: 16rpx 0;
    overflow: hidden;
  }

  .mrb {
    padding-bottom: 24rpx;
  }

  .title {
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
    padding-left: 24rpx
  }

  .textarea {
    min-height: 300rpx;
    background: #fff;
  }

  .turn {
    background-color: #fff;
    width: 100%;
    min-height: 96rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8rpx;
    margin-bottom: 16rpx;

    .center-con {
      width: 100%;
      height: auto;
      padding: 16px 12px;

      .title-wrap {
        font-size: 34rpx;
        color: rgba(0, 0, 0, 0.85);
        line-height: 44rpx;
        font-weight: bold;
        margin-bottom: 24rpx;
      }

      .input-phone {
        height: 42rpx;
        width: 100%;

        .input-box {
          width: 100%;
          height: 44rpx;
          font-size: 28rpx;
          color: #111;
          line-height: 44rpx;
        }
      }
    }

    .center-b {
      border-bottom: 1rpx solid #EFF1F6;
    }
  }

  .phone {
    margin-top: 16rpx;
  }

  .code-wrap {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-bottom: 1rpx solid #EFF1F6;

    .codeBox {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .input-phone {
        width: 430rpx;
      }

      .code {
        font-size: 30rpx;
        font-weight: bold;
      }
    }
  }


  /* 敏感词提示 */
  .warning-tips {
    background-color: #FFEBEC;
    color: #f74742;
    font-size: 26rpx;
    line-height: 30rpx;
    padding: 16rpx;
    margin-top: 24rpx;
    border-radius: 8rpx;
  }

  .red-word {
    width: 654rpx;
    margin-left: 24rpx;
    line-height: 36rpx;
  }


  .recruit-choose-con {
    width: 100%;
    height: auto;
    background-color: #F5F6FA;
    margin-top: 16rpx;

    .input-tips-wrap {
      width: 100%;
      height: auto;
      border-radius: 8rpx;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #FFF;
      padding: 32rpx 24rpx 16rpx 24rpx;

      .title-con {
        width: 100%;
        height: 48rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
        font-weight: bold;

        .left {
          height: 48rpx;
          display: flex;
          align-items: center;
          color: #000;
          opacity: 0.85;
          font-size: 34rpx;
        }

        .right {
          display: flex;
          font-size: 30rpx;
          align-items: center;
          justify-content: space-between;
          height: 48rpx;
          color: #0092FF;
        }
      }

      .group {
        width: 100%;
        font-size: 28rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #262626;

        .tack-con {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          .class-item {
            padding: 0rpx 24rpx;
            line-height: 64rpx;
            background-color: #F5F6FA;
            border-radius: 8rpx;
            margin-right: 16rpx;
            margin-bottom: 16rpx;
            height: 64rpx;
          }

          .class-seleted {
            color: #0092FF;
            background-color: #E0F3FF;
            font-weight: bold;
          }
        }

        .noSelectedStyle {
          max-height: 232rpx;
        }
      }
    }
  }

  .my-class {
    margin-bottom: 16rpx;
  }

  .textarea-style {
    background: #fff !important;
    padding: 0 24rpx 32rpx 24rpx !important;
  }

  .textarea-class {
    background: #fff !important;
  }

.loginProtocolPopup {
  background-color: #fff;
  min-height: 20vh;
  padding: 48rpx 32rpx 0;
  border-radius: 16rpx 16rpx 0 0;

  width: 100%;
  overflow: hidden;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.rule-tips-btn {
  color: #1677ff; /* 蓝色链接样式 */
}

.dialog-tips {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  line-height: 40rpx;
  text-align: left;
  vertical-align: middle;
  color: @text65;
  font-size: 30rpx;
  font-weight: 400;
  text-align: left;
  margin-top: 24rpx;
}

.footer {
    background: #fff;

    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;

      height: 96rpx;
      font-size: 34rpx;
      font-weight: 500;
    }

    .btn-confirm,
    .register-btn {
      width: 100% !important;
      background: @primary-color;
      margin: 0;
      color: rgba(255, 255, 255, 0.95);
    }
  }

  .emptyView {
    height: 48rpx;
  }

  .tipTitle {
    position: relative;
    color: @text85;
    font-size: 38rpx;
    font-weight: bold;
    text-align: left;
  }

  .loginTipCloseIcon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .textarea-header {
    padding-right: 24rpx;
  }

  .custom-promote-city {
    width: 100% !important;
    padding: 0 24rpx 32rpx !important;
  }

  .promote-title {
    display: flex;
    flex-direction: row;
    padding-left: 24rpx;
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
    color: #262626;
  }

  .city-margin {
    margin-top: 16rpx !important;
  }

  .principal-container {
    padding: 32rpx 24rpx;
    background: #fff;
    border-radius: 16rpx;
  }
  .p0-container {
    padding: 0 !important;
    border: none !important;
    border-radius: 16rpx;
  }

  .p0-title {
    font-size: 34rpx !important;
    font-weight: bold !important;
    color: #262626 !important;
    line-height: 48rpx !important;
  }

  .p0-text {
    font-size: 30rpx !important;
    color: rgba(0, 0, 0, 0.25);
  }
