<image src="https://cdn.yupaowang.com/yupao_common/87debc5c.png" class="picker-tips-ic" mode="aspectFill" bind:tap="showTips"/>
<drawer visible="{{visible}}" isMaskClose bind:close="closeTips">
    <view class="drawer-container">
        <view class="drawer-title">推广城市<image src="https://cdn.yupaowang.com/yupao_common/1fa70009.png" mode="aspect-fill" class="close-ic" bindtap="closeTips"></image></view>
        <view class="drawer-sub-title">职位默认推广到工作地址所在城市，可选择推广到外地 </view>
        <view class="q-title">Q1：什么是“推广城市”？</view>
        <view class="a-desc">系统会根据你填写的职位名称，显示推广城市功能。</view>
        <view class="a-content">
            <view class="a-text-main">如果你有以下需求，推荐你使用此功能：</view>
            <view class="a-text-title">场景一：跨城招聘</view>
            <view class="a-text-main m-fix">当你发布的职位工作地址在 A 城市（如成都），但希望吸引 B 城市（如重庆）的求职者前来工作时，请将“推广城市”设为 B 城市（重庆）。</view>
            <view class="a-text-title">场景二：远程招聘</view>
            <view class="a-text-main m-fix">当你发布的职位支持远程办公（如主播可在家工作），且您希望重点吸引 B 城市（如重庆）的求职者应聘时，请将“推广城市”设为 B 城市（重庆）。</view>
            <view class="a-text-desc">如果您没有类似需求，推广城市请保持选择与职位工作地址所在城市一致</view>
        </view>
        <view class="q-title q2-fix">Q2：选择“推广城市”后，有什么效果？</view>
        <view class="a-content">
            <view class="a-text-main">职位将被推广到所选城市，当职位工作地址所在城市与求职者期望城市不一致时，求职者看到此职位将展示“外地”标签。</view>
            <view class="a-text-desc">职位发布成功后，推广城市不可修改。</view>
        </view>
        <m-stripes></m-stripes>
    </view>
</drawer>