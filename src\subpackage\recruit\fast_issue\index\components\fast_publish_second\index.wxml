<!--发布招工 流程2 -->
<view class="body">
    <custom-header title="发布职位" customBack bind:back="onNavBack"  />
    <view style="min-height: {{minHeight}}px;">
      <view class="recruit-choose-con">
        <view class="principal-container" >
          <hiring-client-select 
            custom-container="p0-container"
            custom-title="p0-title"
            custom-text="p0-text"
            wx:if="{{newIssueJobConfig.needHiringClient}}"
            ></hiring-client-select>
        </view>
        <!-- 表单数据 -->
        <m-form id="formArea" change>
          <view class="recruitment {{isWarning ? 'mrb' : ''}}">
            <view class="textarea">
              <sensitive-textarea
                value="{{textareaValue}}"
                defaultValue="{{content}}"
                thesaurusList="{{basicConfig.thesaurusList}}"
                adjustPosition="{{true}}"
                placeholder="请勿填写电话、QQ、微信等联系方式、性别歧视、地域歧视、海外招工、违反法律法规的相关内容"
                data-name="content"
                bind:input="onInput"
                
                bind:focus="onTextAreaFocus"
                bind:keyChange="onKeyChange"
                bind:blur="blurEvent"
                myClass="textarea-style"
                myTextareaClass="textarea-class"
                headerClass="textarea-header"
                >
                <view slot="title" class="title">招工详情</view>
              </sensitive-textarea>
            </view>
            <!-- 敏感词提示 -->
            <view class="warning-tips red-word" wx:if="{{isWarning}}">
              <text class="warning-text">您发布的内容里面存在敏感词，建议修改，否则敏感词将不会被展示</text>
            </view>
          </view>
          <m-form-classify-choose
            formId="formArea"
            isAb="{{false}}"
            placeholder="选择您平时需要招的工种"
            sourcePageName="发布招工"
            name="trades"
            id="trades"
            change
            quickOccValue="{{quickOccValue}}"
            isShowRecommend="{{classifyShowRecommend}}"
            bind:change="onChooseClassify"
            bind:cbOpen="cbOpen"
            isPulishRecruitEnhance="{{true}}"
            prevUrl="{{prevUrl}}"
            bind:sendValueLabel="sendFilterValueLabel"
            isShowOutSideChoose="{{startCaluteTimeShow && haveSelectedClassGroup.length == 0}}"
            moreBottom="{{true}}"
            cacheExposureList="{{haveSelectedClassGroup}}"
            bind:sendcacheExposureList="sendcacheExposureList"
            styleType="{{1}}"
            sceneType="{{1}}"
            occupationHintMsg="{{newIssueJobConfig.occupationHintMsg || ''}}"
            isZpSingle="{{true}}"
            sortOz="{{true}}"
          />
          <!-- 工种推荐词-模块 -->
          <block wx:if="{{haveSelectedClassGroup.length > 0}}">
            <view class="input-tips-wrap">
              <view class="title-con">
                <view class="left">
                  {{isShowSelectedHeadline ? '已选择工种' : '为您推荐的工种'}}
                </view>
                <view class="right" data-type="open" bind:tap="cbOpen" data-name="打开职位选择器">
                  <text>选择更多工种</text>
                  <icon-font type="yp-yp-icon_mbx_vip" size="40rpx" color="#0092FF" />
                </view>
              </view>
              <view class="group" wx:key="index">
                <view class="tack-con {{!isShowSelectedHeadline ? 'noSelectedStyle' : ''}}">
                  <view class="class-item {{soloItem.isSelected ? 'class-seleted': ''}}" wx:for="{{haveSelectedClassGroup}}" wx:for-item="soloItem" data-item="{{soloItem}}" bind:tap="onClickClassItem" >{{soloItem.name}}</view>
                </view>
              </view>
            </view>
          </block>
          <m-form-city-choose
            isCity
            change
            disabledCities="33,34,35"
            source="publishRecruit"
            name="current_area"
            bind:change="changeCityLocation"
            title="{{newIssueJobConfig.cityHintMsg}}"
            userAcq="{{userAcq}}"
            formId="formArea"
            custom-class="city-margin"
            moreBottom="{{true}}"
            styleType="{{1}}"
            myClass="my-class"
            bind:getGPS="onGetGPS"
          />
          <view class="recruitment" wx:if="{{promoteCityVisible&&currentAddress.selectCity.id}}">
            <view class="promote-title">{{templates.promoteCity.title || '推广城市'}} <promote-tips/></view>
            <city-promote titleVisible="{{false}}" container-class="custom-promote-city" value="{{promoteCities}}" placeholder="{{templates.promoteCity.subtitle}}" title="{{templates.promoteCity.title}}"  wx:if="{{promoteCityVisible&&currentAddress.selectCity.id}}" publishCity="{{currentAddress}}" bind:change="onPromoteCityChange"></city-promote>
          </view>
        </m-form>
      </view>
      
      <!-- 输入手机号 -->
      <view class="turn phone" wx:if="{{showPhone|| !loginStatus && sPostEnable}}">
        <view class="center-con center-b">
          <view class="title-wrap">手机号</view>
          <view class="input-phone">
            <input
              value="{{phone}}"
              placeholder="请输入联系电话"
              placeholder-style="color:rgba(0, 0, 0, 0.25);"
              maxlength="11"
              type="number"
              class="input-box"
              data-name="phone"
              bind:input="onInput"
              bind:blur="onPhoneBlurEvent"></input>
          </view>
        </view>
      </view>
      <!-- 输入验证码 -->
      <view class="turn code-wrap" wx:if="{{showVerifyCode|| !loginStatus && sPostEnable}}">
        <view class="center-con">
          <view class="title-wrap">验证码</view>
          <view class="codeBox">
            <view class="input-phone">
              <input
                placeholder="请输入验证码"
                placeholder-style="color:rgba(0, 0, 0, 0.25);"
                type="number"
                maxlength="{{4}}"
                value="{{code}}"
                data-name="code"
                class="input-box"
                bind:input="onInput"
              />
            </view>
            <view class="code">
              <verification-code biz="login" initTime="{{0}}" wx:if="{{!loginStatus && sPostEnable}}" id="verificationCode" tel="{{phone}}" telCodeOrigin="2" bind:verifyToken="onCallBackVerifyToken"/>
              <verification-code id="verificationCode" wx:else tel="{{phone}}" telCodeOrigin="2" bind:verifyToken="onCallBackVerifyToken"/>
            </view>
          </view>
          <!-- 验证码提示语 -->
          <view class="warning-tips" wx:if="{{!code}}">
            <text class="warning-text" wx:if="{{!isValidPhone}}">注意：不填写验证码，职位信息将发布失败！！！</text>
            <text class="warning-text" wx:if="{{isValidPhone}}">温馨提示：为了确保您的号码还在使用，请验证手机号</text>
          </view>
        </view>
      </view>
    </view>
    <view style="padding-bottom: {{bottomHeight || 200}}px;">
      <view class="logo-footer" style="opatity: {{minHeight ? 1 : 0}};">
        <logo-footer custom-style="height: auto; padding-bottom: 24rpx;" />
      </view>
    </view>
    <!-- 底部发布规则 -->
      <view class="footer-rule-tips">
        <submit-rule-tips
        id="rule-tips"
        isCheckAgreement="{{isCheckAgreement}}"
        bind:checkAgreement="handleCheckAgreement"
        sPostEnable="{{sPostEnable}}"
        loginStatus="{{loginStatus}}"
        publishBtnStatusTxt="{{publishBtnStatusTxt}}"
        bind:buryPoint="buryPoint"
        bind:getPhoneNumber="onGetPhoneNumber"
        bind:submit="onSubmit"
        showAgreement="{{newIssueJobConfig.publishRuleWhetherShow}}"
        myClass="tips-class"
        userAcq="{{pageOptions.userAcq}}" />
      </view>
      <!-- 温馨提示弹窗(隐私协议) -->
<drawer catch:touchmove="true"  visible="{{dialogVisible}}" isMaskClose="{{false}}" zIndex="9999">
  <view class="loginProtocolPopup">
    <view class="tipTitle">
      <text>温馨提示</text>
      <view catch:tap="onClose">
        <icon-font type="yp-icon_pop_close" size="48rpx" color="#666" customClass="loginTipCloseIcon" />
      </view>
    </view>
<view class="dialog-tips">
  <text>发布职位信息即表示遵守</text>
  <text class="rule-tips-btn" bind:tap="jumpRule">《鱼泡直聘职位信息发布规则》</text>
  <block wx:if="{{pageOptions.userAcq == 2 || pageOptions.userAcq == 4}}">
    <text class="rule-tips-btn" bind:tap="onGoToPrivacyPolicyPage" data-type="user">、《服务协议》、</text>
    <text class="rule-tips-btn" bind:tap="onGoToPrivacyPolicyPage" data-type="privacy">《隐私政策》</text>
  </block>
  ，如果违反规则可能导致你的账号异常
</view>
    <view class="footer">
      <button class="fast-login-btn login-btn btn-confirm" bind:tap="onCheckSubmit" loading="{{loading}}">
        {{ publishBtnStatusTxt == '下一步' ?  '同意并继续' : '同意并发布'}}
      </button>
    </view>
    <view class="emptyView" />
  </view>
</drawer>
  </view>
