import { actions, dispatch, store } from '@/store/index'

Component(class extends wx.$.Component {
  properties = {
    conversation: {
      type: Object,
      value: {},
    },
  }

  observers = {
    value(v) {
      if (v) {
        wx.$.collectEvent.event('quick_greet_exposure')
        this.setData({ showValue: v.trim().replace(/\s+/g, '') })
      } else {
        this.setData({ showValue: '' })
      }
    },
  }

  lifetimes = {
    ready() {
      this.initData()
    },
  }

  data = {
    // 追聊牛人数据
    list: [],
    // 选中的招呼语
    value: '',
    // 显示的追聊消息
    showValue: '',
    // 选中的招呼语下标
    sltedIdx: -1,
  }

  initData() {
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/helloWordList']().then((res) => {
      const { data, code } = res || {}
      if (code == 0) {
        const { list } = data || {}
        let value = ''
        let sltedIdx = -1
        let nList = []
        if (wx.$.u.isArrayVal(list)) {
          nList = list.map((item) => (item && item.content) || '')
          const item = list[0] || {}
          const { content } = item || {}
          value = content || ''
          sltedIdx = 0
        }
        this.setData({ list: nList, value, sltedIdx })
      } else {
        this.setData({ list: [], value: '', sltedIdx: -1 })
      }
    }).catch(() => {
      this.setData({ list: [], value: '', sltedIdx: -1 })
    })
  }

  async onClose() {
    await wx.$.u.waitAsync(this, this.onClose, [], 2000)
    const { conversation } = store.getState().timmsg
    const { conversationId } = conversation || {}
    await dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, isShowSayHelloAgain: false } }))
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/close']({ conversationId })
    this.triggerEvent('close')
  }

  onChange() {
    wx.$.collectEvent.event('quick_greet_click', { click_button: '换一句' })
    const { sltedIdx, list } = this.data
    const nSltedIdx = (sltedIdx + 1) % list.length
    const value = list[nSltedIdx]
    this.setData({ sltedIdx: nSltedIdx, value })
  }

  async onEdit() {
    await wx.$.u.waitAsync(this, this.onEdit, [], 2000)
    wx.$.collectEvent.event('quick_greet_click', { click_button: '编辑' })
    const { value } = this.data
    this.triggerEvent('edit', { value })
  }

  async onSend() {
    await wx.$.u.waitAsync(this, this.onSend, [], 2000)
    const { conversation, value } = this.data as DataTypes<typeof this>
    const { conversationId } = conversation || {} as any
    dispatch(actions.timmsgActions.setState({ conversation: { ...conversation, isShowSayHelloAgain: false } }))
    wx.$.collectEvent.event('quick_greet_click', { click_button: '发送' })

    const msg = '发送失败，请稍后重试'
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/batchCommit']({ conversationIds: [conversationId], helloWord: value }).then((res) => {
      wx.hideLoading()
      const { code, data } = res || {}
      const { successNum } = data || {}
      if (code == 0) {
        successNum == 0 && wx.$.msg(msg)
        return
      }
      wx.$.msg(msg)
    }).catch(() => {
      wx.hideLoading()
      wx.$.msg(msg)
    })
  }
})
