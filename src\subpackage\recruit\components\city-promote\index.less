.picker-container {
    width: 686rpx;
    padding: 40rpx 0;
}

.picker-title-line {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.picker-title-text {
    color: @text65;
    font-size: 30rpx;
    font-weight: 400;
}

.picker-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 16rpx;
}

.picker-content-placeholder {
    color: rgba(0,0,0,.25);
    font-size: 34rpx;
    font-weight: 400;
}

.picker-content-text {
    font-size: 34rpx;
    font-weight: 400;
    color: @text85;
    .textrow(1);
}

.disabled {
    color: rgba(0,0,0,.25);
    font-size: 34rpx;
    font-weight: 400;
}
