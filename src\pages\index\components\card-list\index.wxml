<view class="body" style="min-height: calc(100vh - {{calcHeight}}); position: relative;">
  <!-- 引导订阅间距抵冲 -->
  <view style="margin-top: {{!firstLoaded ? '16rpx' : '0'}}" />
  <!-- 引导订阅 -->
  <block wx:if="{{login}}">
    <guide-subscribe id="subscribe" hasList="{{list && list.length != 0}}" bind:onSubscribe="onSubscribe" />
  </block>
  <ab-slot ab-id="JobList">
    <block wx:if="{{!firstLoaded}}">
      <skeleton-v5 wx:if="{{cardMode == 2 || (cardMode == 0 && isV5Card) || (cardMode == 1 && joblist_newDDcard === 'oldZP')}}" />
      <skeleton-v4 wx:else isNewDD="{{isNewDD}}" />
    </block>
  </ab-slot>
  <block wx:if="{{!noData}}">
    <block wx:for="{{list}}" wx:key="guid">
      <!-- 如果记录的历史ID和当前ID一样，就给它头上加上提示 -->
      <view class="refresh-message" wx:if="{{ is_histroy_id == item.jobId && index > 0 }}">- 以上是本次刷新信息 -</view>
      <!-- 信息流 (引导订阅卡片展示时隐藏完善简历) -->
      <view class="slide-down" wx:if="{{!(isSubscribeShow && recruitSort.value === 'newest') && infoFlow.resumeSubUuid && (index + 1) == infoFlow.rank}}">
        <list-info-flow infoFlow="{{infoFlow}}" bind:openPop="openPopup" top="{{listScrollTop}}" />
      </view>
      <view class="recruit-item {{item.isInsert? 'force-insert' :''}}" data-index="{{index}}" data-item="{{item}}">
        <ab-slot ab-id="JobList">
          <!-- cardMode 为 2 时：使用 v5 卡片 -->
          <!-- cardMode 为 0 时：根据 isV5Card 的值来决定使用 v4 还是 v5 -->
          <!-- cardMode 为 1 且 joblist_newDDcard 为 oldZP 时：使用旧版（isNewZPCard=false）v5 卡片 -->
          <recruit-card-v5 wx:if="{{cardMode == 2 || (cardMode == 0 && isV5Card) || (cardMode == 1 && joblist_newDDcard === 'oldZP')}}" pageCode="recruit_list" item="{{item}}" occV2="{{occV2}}" isFreeFirst isShowTop data-index="{{index}}" fromOrigin="recruitListIndex" isBeforeClick bind:beforeClick="onClickDetail" bind:clickTopBtn="onClickTopBtn" bind:callphonebtn="onCallPhoneBtn" bind:onGoToChat="onGoToChat" listSource="{{listSource}}" isNeedJump="{{1}}" joblisttype="{{recruitSort.value}}" pageShowAuth="{{pageShowAuth}}" isFromMainRecruitList="{{isFromMainRecruitList}}" isFromOccVList="{{isFromOccVList}}" isVerifyFamous="{{isVerifyFamous}}" isNewZPCard="{{isNewZPCard && cardMode == 2}}"/>
          <recruit-card-v4 wx:else pageCode="recruit_list" item="{{item}}" occV2="{{occV2}}" isFreeFirst isShowTop data-index="{{index}}" fromOrigin="recruitListIndex" isBeforeClick bind:beforeClick="onClickDetail" bind:clickTopBtn="onClickTopBtn" bind:callphonebtn="onCallPhoneBtn" bind:onGoToChat="onGoToChat" listSource="{{listSource}}" isNeedJump="{{1}}" joblisttype="{{recruitSort.value}}" pageShowAuth="{{pageShowAuth}}" isFromMainRecruitList="{{isFromMainRecruitList}}" isFromOccVList="{{isFromOccVList}}" isVerifyFamous="{{isVerifyFamous}}" isNewDD="{{joblist_newDDcard === 'newDD'}}" />
        </ab-slot>
      </view>
      <block wx:if="{{selectClassifyTabId.isRecommend}}">
        <!-- 根据后台配置-第N页的第2个索引位置展示“其他人还在搜” -->
        <block wx:if="{{index == 14 && ENV_SUB !== 'zyqg'}}">
          <other-recomend-key-word class="interaction-card" keyList="{{keyList}}"/>
        </block>
        <block wx:if="{{index == 11}}">
          <guidance-labeling />
        </block>
      </block>

      <!-- 广告每隔七条数据一条 -->
      <block wx:if="{{index && (index + 1) % 7 === 0 && advertUnitId}}">
        <advert unitId="{{advertUnitId}}" />
      </block>
    </block>
    <block wx:if="{{!loading && !hasMore}}">
      <view class="no-more">- 没有更多内容了 -</view>
    </block>
  </block>
  <!-- loading -->
  <loading-refresh wx:if="{{loading}}" />
  <!-- 无数据 -->
  <view class="empty {{filterConditionCount ? 'struct' : ''}}" wx:if="{{noData && !loading}}">
    <empty-box
      height="auto"
      img-class="no-data-img"
      title="{{filterConditionCount ? '' : '暂无相关职位'}}"
      imgType='recruitSearch'
    />
    <struct-filter-tag wx:if="{{filterConditionCount}}" class="struct-filter-tag" filterCondition="{{filterCondition}}"  filterConditionTem="{{filterConditionTem}}" tabId="{{tabId}}" bind:update="updateFilterCondition"/>
  </view>
</view>
<!-- 评价弹框 -->
<score-evaluation-nw
  wx:if="{{evaluateContentControl.show}}"
  zIndex="{{10001}}"
  isMidGetTel="{{isMidGetTel}}"
  isCooperation="{{true}}"
  action="{{isMidGetTel.action}}"
  special_type="{{info.special_type}}"
  noToast="{{true}}"
  type="job"
  targetId="{{info.id}}"
  expressionKey="{{evaluateContentControl.expression}}"
  bind:submitsuccess="onEvalSubmitSuccess"
  bind:hide="onEvalClose"
  bind:complaint="onComplaint"
/>
<!-- isComplaint="{{info.aboutInfoSpecifics && info.aboutInfoSpecifics.isComplaint}}" -->
