/// drawer

.picker-tips-ic {
    width: 32rpx;
    height: 32rpx;
    margin-left: 8rpx;
    transform: translateY(5rpx);
}
.close-ic {
    width: 42rpx;
    height: 42rpx;
}

.drawer-container {
    padding: 32rpx;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    max-height: calc(100vh - 176rpx);
    overflow-y: scroll;
}

.drawer-title {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    color: @text85;
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
}

.drawer-sub-title {
    margin-top: 16rpx;
    line-height: 42rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: @text45;
}

.q-title {
    margin-top: 24rpx;
    font-size: 34rpx;
    font-weight: bold;
    color: @text85;
    line-height: 48rpx;
}

.a-desc {
    margin-top: 16rpx;
    line-height: 42rpx;
    font-weight: 400;
    color: @text65;
    font-size: 30rpx;
}
.a-content {
    width: 100%;
    padding: 32rpx 24rpx;
    background-color: rgba(245,246,250,1);
    border-radius: 24rpx;
    margin-top: 16rpx;
}
.a-text-main {
    color: @text85;
    font-size: 30rpx;
    font-weight: 400;
    line-height: 42rpx;
}

.a-text-title {
    margin-top: 24rpx;
    color: @text85;
    font-weight: bold;
    font-size: 34rpx;
    line-height: 48rpx;
}
.a-text-desc {
    margin-top: 24rpx;
    font-weight: 400;
    font-size: 30rpx;
    line-height: 42rpx;
    color: @text45;
}

.m-fix {
    margin-top: 8rpx;
}

.q2-fix {
    margin-top: 32rpx;
}