import { isByIdRegion, isByAdcodeRegion, DIRECT_CITY_IDS } from '@/utils/helper/location/index'
import type { PagePoint, TTreeFullVal, TTreeVal, TTreeState, TAddrTreeData, TSelectAddress } from './index.d'
import {
  getAddrConfig,
  hotID,
  getAreaTreeFull,
  getSelectSearches,
  handlerData,
  addrConfDef,
  setSelect,
  handlerSelect,
  getAreaConfig,
  hmtID,
  getHotIds,
} from './tools'

/** 页面地址配置 */
let addrConf = { ...addrConfDef }

/** 埋点source_id 映射关系 */
const sourceMap = {
  1: '招工列表',
  2: '发布招工',
  3: '找活列表',
  4: '编辑招工',
  5: '编辑找活名片',
  6: '招工搜索中间页',
  7: '招工搜索结果页',
  8: '找活搜索中间页',
  9: '找活搜索结果页',
  10: '发布找活名片',
  11: '新牛人引导',
}

async function getPointData(areaIds) {
  const ids = areaIds.map(item => String(item))
  const areaInfos = await wx.$.l.getAreaByIds(ids)
  const hotIds = await getHotIds()
  const provinces = new Set<string>()
  const cites = new Set<string>()
  const areas = new Set<string>()

  const isHot = hotIds.some(item => ids.includes(item))

  areaInfos.forEach(({ province, city, district, current, special }) => {
    if (!current) {
      return
    }

    if (current.id == 1) {
      provinces.add(current.name)
      return
    }
    if (special === 'region') {
      if (province) {
        cites.add(province.name)
      }
      if (city) {
        areas.add(city.name)
      }
    } else {
      if (province) {
        provinces.add(province.name)
      }
      if (district) {
        areas.add(district.name)
      }
      if (city) {
        cites.add(city.name)
      }
    }
  })
  const provinceArr = Array.from(provinces)
  isHot ? provinceArr.unshift('热门') : ''

  return {
    provinces: provinceArr,
    cites: Array.from(cites),
    areas: Array.from(areas),
  }
}

/** 页面埋点 */
export const pagePoint = async function (even, option = {}, areaIds = []) {
  const options: any = option || {}
  const source_id = wx.$.u.getObjVal(addrConf, 'point.source_id') || ''
  const source = sourceMap[source_id] || ''
  if (!source) {
    return
  }
  if (even === 'city_filter_click') {
    const button_name = wx.$.u.getObjVal(addrConf, 'point.button_name') || ''
    options.button_name = button_name
  }

  if (wx.$.u.isArrayVal(areaIds)) {
    const { provinces, cites, areas } = await getPointData(areaIds)

    options.select_province = provinces.join(',')
    options.select_city = cites.join(',')
    options.select_area = areas.join(',')

    options.area_id = areaIds.join(',')
  }

  wx.$.collectEvent.event(even, {
    ...options,
    source_id,
    source,
  })
} as PagePoint

/** 获取地址页的配置数据 */
export function getAddrConf() {
  const conf = getAddrConfig()
  addrConf = conf
  return getAddrConfig()
}

/** 初始化地址数据
 * @param areas 已选中的地址数组
 */
export async function initAddress(areas: (string | number)[] = []): Promise<TTreeState> {
  /** 5576, 510104 */
  const areaTreeFull = await getAreaTreeFull()
  let oneArea: TTreeFullVal = handlerData(wx.$.u.deepClone(areaTreeFull.province), 1)
  let twoArea: TTreeFullVal = []
  let threeArea: TTreeFullVal = []
  const { selectAddr, areaInfo, areaIsRegion, isHot, deleteAddr } = await getSelectSearches(areas)

  if (!areaInfo.current || areaInfo.current.id == 1) {
    // 当没有选中地址或者选中的是全国时
    oneArea[0].checked = true
    const oneAreaId = oneArea[0].id
    twoArea = wx.$.u.deepClone(areaTreeFull.city[oneAreaId]) || []
    if (areaInfo.current.id == 1) {
      // 选中的为全国时
      twoArea[0].checked = true
    }
    return { oneArea, twoArea, threeArea, selectAddr, deleteAddr }
  }

  /** 已选中其他地址时 */
  if (areaInfo.province) {
    /** 设置显示的二级地址 */
    if (isHot) {
      twoArea = setSelect(areaTreeFull.city[hotID], areaInfo.city.id)
    } else {
      const provinceId = areaInfo.province.pid === hmtID
        ? hmtID
        : areaInfo.province.id
      twoArea = setSelect(areaTreeFull.city[provinceId], areaInfo.city.id)
    }
  }

  if (addrConf.level == 3 && (areaIsRegion || areaInfo.city)) {
    if (isHot && areaIsRegion) {
      /** 选中热门城市的并且是直辖市时候 */
      threeArea = setSelect(areaTreeFull.city[areaInfo.province.id], areaInfo.current.id)
    } else {
      /** 设置显示的三级地址 */
      threeArea = setSelect(areaTreeFull.district[areaInfo.city.id], areaInfo.district.id)
    }
  }

  const dataResult = selectHandler({
    oneArea,
    twoArea,
    threeArea,
    selectAddr,
  }, isHot)

  oneArea = dataResult.oneArea
  twoArea = dataResult.twoArea
  threeArea = dataResult.threeArea

  return { oneArea, twoArea, threeArea, selectAddr, deleteAddr }
}

/** 处理地址列表选中逻辑-包括每个级别的数量
 * @param addrState 地址列表数据
 * @param area 当前点击的地址信息
 * @param isHot 是否选中热门城市（仅限进入页面初始化的时候为true）
 */
export function selectHandler(addrState: TTreeState, isHot = false): TAddrTreeData {
  const { selectAddr } = addrState
  let { oneArea } = addrState
  let twoArea = handlerData(addrState.twoArea, 2)
  let threeArea = handlerData(addrState.threeArea, 3)

  const selectIds = selectAddr.map(item => `${item.id}`)

  if (wx.$.u.isArrayVal(oneArea) && wx.$.u.isArrayVal(twoArea)) {
    oneArea.forEach(item => {
      item.checked = false
    })
    if (isHot) {
      oneArea[0].checked = true
    } else {
      const threePid = twoArea[0].isFull ? twoArea[0].id : twoArea[0].pid
      /** 省地址的处理 */
      oneArea.some(item => {
        if (threePid == item.id) {
          item.checked = true
        }
        return threePid == item.id
      })
    }
  }

  if (wx.$.u.isArrayVal(threeArea)) {
    const threePid = threeArea[0].isFull ? threeArea[0].id : threeArea[0].pid
    twoArea.forEach(item => {
      item.checked = false
      if (threePid == item.id) {
        item.checked = true
      }
    })
    /** 区域地址的处理 */
    threeArea.forEach(item => {
      if (selectIds.includes(`${item.id}`)) {
        item.checked = true
      } else {
        item.checked = false
      }
    })
  } else if (wx.$.u.isArrayVal(twoArea)) {
    /** 没有三级地址列表有城市地址的处理 */
    twoArea.forEach(item => {
      item.checked = false
      /** 已选中的地址 */
      if (selectIds.includes(`${item.id}`)) {
        item.checked = true
      }
    })
  }

  if (addrConf.selectType === 'resumePositionTab') {
    if (isByAdcodeRegion(selectAddr[0].ad_code)) {
      oneArea = oneArea.filter(item => item.checked)
    } else if (wx.$.u.isArrayVal(threeArea)) {
      oneArea = twoArea.filter(item => item.checked)
      twoArea = threeArea
      threeArea = []
    }
  }
  return { oneArea, twoArea, threeArea }
}

/**
 * 处理选中事件
 * @param areaInfo ITreeArea: { province: '', city: '', district: '', current: '' }
 * @param selecting 已选中的地址数组
 * @param level 当前页面UI的地址级别
 * @param 是否点击的是热门地址
 * */
export async function selectAddress(area: TTreeVal, selecting: TTreeVal[], isHot = false): Promise<TSelectAddress> {
  const objSelect = handlerSelect(area, selecting, isHot)

  let { isEnd } = objSelect
  const { oneArea,
    twoArea,
    threeArea,
    provinceId,
    cityId,
    districtId } = objSelect

  const isSelect = selecting.some(item => item.id == area.id)

  const isDirectCity = selecting.some(item => DIRECT_CITY_IDS.includes(`${item.id}`))

  if ((!isSelect && addrConf.selectType === 'district' && !isEnd && area.level != 3) || (isSelect && isDirectCity && addrConf.forceCityOnly)) {
    /** 【紧急】小程序-简历列表-v4.3.3-优化城市选择器切换“市”时的交互方式 */
    const { city, special } = await wx.$.l.getAreaById(area.id)
    if (special == 'region' || (city && city.id)) {
      isEnd = true
      area.isFull = true
    }
  }

  // forceCityOnly: 当选择到市级时就结束，不再展示区级
  if (addrConf.forceCityOnly && area.level == 2) {
    isEnd = true
    area.isFull = true
  }

  /** 设置选中情况 */
  oneArea.forEach((item) => {
    item.checked = false
    if (isHot) {
      item.checked = item.id == hotID
    } else {
      item.checked = item.id == provinceId
    }
  })

  if (wx.$.u.isArrayVal(twoArea)) {
    twoArea.forEach(item => {
      item.checked = item.id == cityId
      if (area.isFull) { // 如果是isFull，则是当前地址的全地址
        item.checked = item.id == area.id
      }
    })
  }
  if (wx.$.u.isArrayVal(threeArea)) {
    threeArea.forEach(item => {
      item.checked = item.id == districtId
      if (area.isFull) { // 如果是isFull，则是当前地址的全地址
        item.checked = item.id == area.id
      }
    })
  }

  const twoAreaNew = handlerData(twoArea, 2)
  const threeAreaNew = handlerData(threeArea, 3)
  const isConfirm = isEnd && !addrConf.isMultiple
  /* if (isEnd && addrConf.selectType === 'district' && area.isFull) {
    // 这里addrConf.selectType为district，是为了处理简历筛选页的逻辑
    isConfirm = true
  } */

  return {
    oneArea,
    twoArea: twoAreaNew,
    threeArea: threeAreaNew,
    isEnd,
    isConfirm,
  }
}

/** 处理选中数组 */
export function selectArrHandler(data: TTreeVal, dataList: TTreeVal[]) {
  // 如果选择的是全国
  if (data.id == 1) {
    const isDel = dataList.some(item => item.id == 1)
    return isDel ? [] : [{ ...data }]
  }

  const list = dataList.filter((item => {
    if (addrConf.selectType == 'district') {
      // 仅限单个城市的区域多选的情况
      if (item.id == data.id) {
        return true
      }
      if (data.isFull || data.pid == hmtID) {
        // 选中全地址和港澳台的情况
        return false
      }
      return data.pid == item.pid
    }
    return true
  }))

  /** 当前选中的地址在数组中的位置 */
  const index = list.findIndex(item => item.id == data.id)
  if (index != -1) {
    list.splice(index, 1)
    return list
  }

  list.push(data)

  return list.filter((item, index) => {
    if (item.id == 1) { // 移除全国
      return false
    }
    if (index == list.length - 1) {
      return true
    }
    if (data.id == item.pid) {
      return false
    }
    if (data.id == item.gid) {
      return false
    }
    if (data.pid == item.id) {
      return false
    }
    /* if (item.id == data.gid) {
      return false
    } */
    return data.gid != item.id
  })
}

/** 清空所有已选的id */
export function selectClear<T extends TAddrTreeData>(addrState: T): T {
  const { oneArea, twoArea, threeArea } = wx.$.u.deepClone(addrState)

  oneArea.forEach(item => {
    item.checked = false
    if (wx.$.u.isArrayVal(twoArea)) {
      const threePid = twoArea[0].isFull ? twoArea[0].id : twoArea[0].pid
      item.checked = item.id == threePid
    }
  })

  twoArea.forEach(item => {
    item.checked = false
    if (addrConf.level == 3 && wx.$.u.isArrayVal(threeArea)) {
      const threePid = threeArea[0].isFull ? threeArea[0].id : threeArea[0].pid
      item.checked = item.id == threePid
    }
  })

  threeArea.forEach(item => {
    item.checked = false
  })

  return { oneArea, twoArea, threeArea } as T
}

/** 获取需要搜索的城市数据 */
export async function getSearchAreas() {
  const { hotCities } = await getAreaConfig()
  const treeData = await wx.$.l.getAreaTreeData()
  const cityDatas = []

  treeData.forEach(({ children, ...item }) => {
    if (isByIdRegion(item.id)) {
      cityDatas.push(item)
    } else if (wx.$.u.isArrayVal(children)) {
      children.forEach(({ children: C, ...itemC }) => {
        if (itemC && itemC.id) {
          cityDatas.push({ ...itemC, pName: item.name })
        }
      })
    }
  })

  if (wx.$.u.isArrayVal(hotCities)) {
    // 将hotIds中的数据进行匹配 匹配成功的hostId时删除cityDatas中匹配到的数据
    const hotDatas = []
    hotCities.forEach(item => {
      const cityInd = cityDatas.findIndex(city => city.id == item)
      if (cityInd != -1) {
        const cityData = (cityDatas.splice(cityInd, 1))[0]
        hotDatas.push(cityData)
      }
    })

    cityDatas.unshift(...hotDatas)
  }

  return cityDatas
}
