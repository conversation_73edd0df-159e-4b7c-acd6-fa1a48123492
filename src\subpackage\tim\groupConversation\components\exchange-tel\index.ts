/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 换电话
 */

import { store } from '@/store/index'
import { applyExchange } from '../../utils'

function maskPhoneNumber(tel) {
  if (!tel) {
    return ''
  }
  if (tel.length === 11) {
    return `${tel.substring(0, 3)}****${tel.substring(7)}`
  }
  return tel
}

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
    crExchangeType: { type: String, value: '' },
  }

  observers = {
    visible(v) {
      if (v) {
        const { crExchangeType } = this.data
        const { userState } = store.getState().storage
        const { tel = '' } = userState || {} as any
        this.setData({ tel: maskPhoneNumber(tel) })
        wx.$.collectEvent.event('phone_change_confirmation_popup_exposure', { trigger_source: crExchangeType == 'SEND_TEL_INTERCEPT_EXCHANGE_TEL' ? '2' : '1' })
      }
    },
  }

  data = {
    // 用户手机号
    tel: '',
  }

  onEditInfo() {
    const { crExchangeType } = this.data as DataTypes<typeof this>
    wx.$.collectEvent.event('phone_change_confirmation_popup_click', { trigger_source: crExchangeType == 'SEND_TEL_INTERCEPT_EXCHANGE_TEL' ? '2' : '1', click_button: '修改手机号' })
    this.triggerEvent('close')
    wx.$.r.push({ path: '/subpackage/member/info/index' })
  }

  async onClose() {
    await wx.$.u.waitAsync(this, this.onClose, [], 1000)
    const { crExchangeType } = this.data as DataTypes<typeof this>
    wx.$.collectEvent.event('phone_change_confirmation_popup_click', { trigger_source: crExchangeType == 'SEND_TEL_INTERCEPT_EXCHANGE_TEL' ? '2' : '1', click_button: '取消' })
    if (crExchangeType) {
      this.triggerEvent('crcallback', { type: 'close', crExchangeType })
    }
    this.triggerEvent('close')
  }

  async onConfirm() {
    await wx.$.u.waitAsync(this, this.onConfirm, [], 1000)
    const { conversation } = store.getState().timmsg
    const { crExchangeType } = this.data as any
    const { conversationId } = conversation || {}
    if (!conversationId) {
      wx.$.msg('会话异常,请稍后重试').then(() => {
        wx.$.r.back()
      })
      return
    }
    wx.$.collectEvent.event('phone_change_confirmation_popup_click', { trigger_source: crExchangeType == 'SEND_TEL_INTERCEPT_EXCHANGE_TEL' ? '2' : '1', click_button: '确认' })
    applyExchange(crExchangeType || 'EXCHANGE_TEL', {}, {
      success: () => {
        if (crExchangeType) {
          this.triggerEvent('crcallback', { type: 'confirm', crExchangeType })
        }
        this.triggerEvent('close')
      },
    })
  }
})
