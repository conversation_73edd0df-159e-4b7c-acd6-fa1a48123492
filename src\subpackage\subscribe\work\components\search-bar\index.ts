/*
 * @Date: 2022-02-14 21:03:13
 * @Description:
 */
import { MapStateToData, connect } from '@/store/index'
import { tryPromise, throttle, getMenuButtonBoundingClientRect, rpxToPx, escapeRegExp } from '@/utils/tools/common/index'
import { addSubscribeWork } from '../../../utils/index'
import { showNoticeModal } from '../../utils'

const mapStateToData: MapStateToData = (state) => {
  const { subscribeConfig } = state.recruitSubscribe
  return {
    chooseType: subscribeConfig.chooseType,
    city: subscribeConfig.city,
    maxLabel: subscribeConfig.maxLabel,
  }
}

Component(connect(mapStateToData)({
  options: {
    // 在组件定义时的选项中启用多slot支持
    multipleSlots: true,
  },
  // 组件的属性列表
  properties: {
    isShowVip: {
      type: Boolean,
      value: false,
    },
  },

  // 组件的初始数据
  data: {
    inputValue: '',
    isClear: false,
    // 下拉列表显示隐藏
    visible: false,
    // 显示下拉列表的头部距离
    headerTop: 0,
    tips: [
      {
        img: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp_mini_sub_wc.png',
        title: '实时微信提醒',
        desc: '有新的职位信息第一时间微信提醒您',
      },
      {
        img: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp_mini_sb_ks.png',
        title: '快速求职',
        desc: '方便随时查找职位，快速获得工作机会',
      },
      {
        img: 'https://cdn.yupaowang.com/yp_mini/images/xjj/yp_mini_sub_jz.png',
        title: '精准匹配招聘信息',
        desc: '可同时添加5个职位，精准推送匹配的职位信息',
      },
    ],
  },
  observers: {
    chooseType() {
      this.getScrollViewLocation()
    },
    isShowVip() {
      this.getScrollViewLocation()
    },
  },
  // 组件的生命周期
  lifetimes: {
    // 在组件实例进入页面节点树时执行
    ready() {
      this.getScrollViewLocation()
    },
  },
  // 组件的方法列表
  methods: {
    // 获取滚动列表的定位
    getScrollViewLocation() {
      const headerHeight = getMenuButtonBoundingClientRect().bottom
      const padding = rpxToPx(48)
      setTimeout(() => {
        this.createSelectorQuery()
          .select('.search-bar')
          .boundingClientRect((res) => {
            const { top } = res || {}
            this.setData({
              headerTop: top - headerHeight - padding,
            })
          })
          .exec()
      }, 100)
    },
    onChangeInput: throttle(function (e) {
      const inputValue = e.detail.value
      if (inputValue && inputValue.trim() !== '') {
        // 搜索匹配的标签
        this.getLabelList(inputValue.replaceAll('\\', '')).then((labelList) => {
          wx.$.collectEvent.event('workTypeSelectionSearch', {
            content_texts: inputValue,
            search_result: labelList.length ? '有结果' : '无结果',
            source: '订阅好活',
            gs_keywords_show: labelList.map(item => item.label),
          })
          this.triggerEvent('onSearchLabel', wx.$.u.isArrayVal(labelList))
          this.setData({
            inputValue,
            labelList,
            visible: labelList.length > 0,
          })
        })
      } else {
        this.triggerEvent('onSearchLabel', false)
        this.setData({ inputValue, visible: false })
      }
    }),
    /** 是否已添加工种判断 */
    duplicate(label) {
      const idx = this.data.chooseType.findIndex((c) => c.name === label)
      if (idx >= 0) {
        wx.$.msg('您已添加该工种')
      }
      return idx >= 0
    },

    /** 开始搜索 */
    onSearch(e) {
      const { label: keywords, index } = e.detail
      if (!keywords || keywords.trim() == '') {
        wx.$.msg('搜索内容不能为空')
        return
      }
      if (!showNoticeModal()) {
        return
      }
      const { labelList, inputValue } = this.data
      // 埋点
      wx.$.collectEvent.event('workTypeSelectionSearchLabelsClick', {
        request_id: `rq${new Date().getTime()}`,
        keywords_list: labelList.map(lb => (lb.label)),
        keywords: inputValue || '',
        keywords_position: `${index + 1}`,
        source_id: 1,
      })
      this.addKeywords(keywords, true)
      this.triggerEvent('onSearchLabel', false)
      this.setData({
        visible: false,
      })
    },
    /** 新增工种 */
    async addWork(label) {
      // 订阅词添加
      const isSuc = await addSubscribeWork({ addLabel: label })
      if (isSuc) {
        this.setData({
          inputValue: '',
        })
        this.triggerEvent('success')
      }
      if (!isSuc && this.data.city.length === 0) {
        // 判断到未添加城市弹出城市选择
        this.triggerEvent('onOpenArea')
      }
    },
    // 清楚文字
    onClear() {
      this.triggerEvent('onSearchLabel', false)
      this.setData({
        inputValue: '',
        visible: false,
      })
    },
    /** 获取搜索标签列表 */
    getLabelList(keywords = '') {
      // 正则
      const regKeywords = new RegExp(escapeRegExp(keywords), 'gi')
      //  替换的字符
      const repKeywords = "<span style='color:#0092ff'>$&</span>"
      return tryPromise(
        wx.$.javafetch['POST/reach/v1/config/subJob/getAssociationalLabelList']({
          label: keywords,
        }, { hideMsg: true }).then(({ data }: any) => {
          if (data.labelList) {
            return data.labelList.map((label) => ({ label, aliasName: `<div>${label.replace(regKeywords, repKeywords)}</div>` }))
          }
          return []
        }),
      )
    },
    // 增加新的工种
    addKeywords(inputValue, isMsg?) {
      wx.$.collectEvent.event('miniPageClick', { click_button: '点击订阅工种', page_name: '/subpackage/subscribe/work/index' })
      const { chooseType, maxLabel } = this.data
      // 正则表达式
      // eslint-disable-next-line prefer-regex-literals
      const reg = new RegExp('^[A-Za-z0-9\u4e00-\u9fa5]+$')
      const disable = inputValue.length < 2 || chooseType.length >= maxLabel
      if (!disable) {
        if (inputValue.length > 6) {
          wx.$.msg('最多输入6位', 1500)
        } else if (!reg.test(inputValue)) {
          wx.$.msg('只能输入中文数字和英文', 1500)
        } else if (!this.duplicate(inputValue)) {
          this.setData({
            isClear: true,
          })
          this.addWork(inputValue)
        }
      } else if (isMsg && chooseType.length >= maxLabel) {
        wx.$.msg(`最多添加${maxLabel}个职位`)
      }
    },
    /** 添加按钮判断 */
    onAddClick() {
      if (!showNoticeModal()) {
        return
      }
      const { inputValue } = this.data
      this.addKeywords(inputValue, true)
      this.triggerEvent('onSearchLabel', false)
      this.setData({
        visible: false,
      })
    },
    /** 防止点击 */
    onDisableMove() {},
    /** 键盘 */
    onKeyboard(e) {
      const height = wx.$.u.getObjVal(e, 'detail.height', 0)
      this.triggerEvent('onInputFocus', height > 0)
      if (height > 0) {
        wx.pageScrollTo({
          scrollTop: this.data.headerTop,
        })
      }
    },
    onBlur() {
      this.triggerEvent('onInputFocus', false)
    },
  },
}))
