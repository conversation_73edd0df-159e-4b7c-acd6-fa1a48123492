.skeleton {
  padding: 32rpx;
  background: #fff;
  
  .skeleton-content {
    display: flex;
    align-items: flex-start;
    
    .skeleton-avatar {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      margin-right: 24rpx;
      flex-shrink: 0;
    }
    
    .skeleton-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16rpx;
      
      .skeleton-title {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
      }
      
      .skeleton-row {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

