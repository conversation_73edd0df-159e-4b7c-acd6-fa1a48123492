/*
 * @Date: 2022-02-09 11:00:45
 * @Description: 群组会话窗口
 */
import { MapStateToData, connectPage, dispatch, actions, messageQueue, storage, store } from '@/store/index'
import { isIos } from '@/utils/tools/validator/index'
import * as timUtils from './timUtils'
import * as utils from './utils'
import * as midUtils from './midUtils'
import * as operUtils from './operUtils'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import resource from '@/components/behaviors/resource'
import { toLogin } from '@/utils/helper/common/toLogin'
import { guid } from '@/utils/tools/common/index'

const { top: topHeight, height } = wx.$.u.sInfo().menuRect
const mapStateToData: MapStateToData = (state) => {
  const { comwords, timmsg, user, message, storage } = state
  const { imGlobalSwitch } = message || {}
  const { toolbarIcon = [], maxMessageWordCountLimit } = imGlobalSwitch || {}

  return {
    role: storage.userChooseRole,
    // 常用语
    greetingList: comwords.greetingList,
    // im消息列表
    messageList: timmsg.messageList,
    // 用于续拉，分页续拉时需传入该字段
    nextReqMessageID: timmsg.nextReqMessageID,
    // 消息列表是否已全部加载完成
    moreMes: timmsg.moreMes,
    // 列表滚动位置ID
    scrollLocId: timmsg.scrollLocId,
    // 列表刷新状态
    refresher: timmsg.refresher,
    // 下拉加载数据前保存列表第一个的ID
    topId: timmsg.topId,
    // 工具栏数据
    toolbarIcon,
    // 用户信息
    userInfo: user.userInfo,
    // 单聊会话基本信息
    conversation: timmsg.conversation,
    // 两分钟内会话数据
    twoMinutesConvData: timmsg.twoMinutesConvData,
    // 对方设置我的备注
    remarkToMe: timmsg.remarkToMe,
    // 是否有超过30天的数据
    isHasOldMsg: false,
    // 透传消息的弹框标识
    popupWindowIdentify: timmsg.popupWindowIdentify,
    /** 是否触发-牛人查看风险职位弹窗 */
    isShowRisk: timmsg.isShowRisk,
    /** 是否触发-IM沟通风险提示弹窗 */
    isShowImChatRisk: timmsg.isShowImChatRisk,
    /** 风险配置弹窗-内容 */
    riskContent: timmsg.riskContent,
    /** 是否已经弹出过IM沟通风险提示弹窗 （进一次会话详情，只弹一次） */
    havePopImChatRisk: timmsg.havePopImChatRisk,
    // --IM会话页面： 控制 牛人风险弹窗 和 IM沟通风险提示弹窗的 总开关
    isShowRiskAndImChatRisk: timmsg.isShowRiskAndImChatRisk,
    maxMessageWordCountLimit,
  }
}
Page(
  connectPage(mapStateToData)({
    ...(!ENV_IS_SWAN ? { behaviors: [resource] } : {}),
    data: {
      ...(ENV_IS_SWAN ? resource.data : {}),
      /** 路由参数 */
      query: {
        /** 腾讯IM单聊id */
        conversationID: '',
        /** 后台IM单聊id */
        conversationId: '',
        /** IM单聊对方账号 */
        toUserImId: '',
        /** 是否调用过消息回调接口(/im/initChatCallback) 0:未调用 1:调用过 */
        chatCallback: '',
        /** 是否是首次创建会话 */
        isFirstTimeCreate: '',
        /** 后端接口的未读数量 */
        toUnread: 0,
        /** 会话发起人是否是自己 */
        isFromOwn: true,
        /** 来源: imlist:消息列表 */
        ptype: '',
      },
      headHeight: topHeight + height + 4,
      capsuleHeight: height,
      keyboardHeight: 0,
      // 动态高度
      autoHeight: 98,
      // 会话消息-找活详情
      resumeInfoMsg: {},
      // 会话开始时间
      startTime: '',
      // 底部聊天功能高度，用于body撑起高度
      chatBtmHeight: 90.5,
      // 顶部聊天功能高度
      chatTopHeight: 112,
      // 是否显示常用语
      cwShow: false,
      // 是否显示表情包
      emojiShow: false,
      // 底部工具栏是否显示
      toolbarShow: false,
      // 输入框文本内容
      chatContent: '',
      // 判断语音正在播放那个,存消息记录的ID
      audioId: '',
      // 是否显示长按弹框
      isShowTk: false,
      // 输入框的foucs
      inputFocus: false,
      // 第一个卡片是否是自己的
      cardIsSelf: false,
      // 判断是否已经下拉加载数据
      isPull: false,
      // 估计拍是否显示
      isShowSkeleton: true,
      /** 找活详情评价内容控制器 */
      evaluateContentControl: {
        content: [],
        show: false,
      },
      /** 判断评价计数是否有加1 */
      isAddEvalNum: false,
      /** 评价弹窗是否加入中间号获取真实号码 */
      isMidGetTel: {
        isShow: false,
        tel: '',
        action: 2,
      },
      /** 音频文件 */
      remoteAudioUrl: '',
      isShowCard: true,
      // 充值弹框是否弹出
      showRechargePopup: false,
      // 充值弹框描述
      rechargeContent: [],
      // 用于充值成功后判断执行的那些逻辑
      rechargeType: '',
      isHide: false,
      // 是否显示顶部时间内容
      isShowTime: false,
      // 是否显示添加微信号弹框
      isUpdateWechatShow: false,
      //  要添加微信号的消息ID
      exchangeWxMsgId: '',
      pageCode: '',
      // 更换职位数据
      jobList: [],
      // 选中的更换职位ID
      spJobId: 0,
      // 是否显示更换职位弹框
      spVisible: false,
      // 更换职位提示内容
      jobToast: '',
      toLogin: false,
      /** 进页面后拉取的会话数据 */
      initPullNotReadImData: [],
      page: 1,
      // 修改追聊消息弹框是否显示
      followChatEditShow: false,
      // 追聊消息内容
      followChatEditContent: '',
      // 换手机号或换微信弹框是否显示
      exchangeTelOrWechatPopShow: false,
      // B端不合适弹框是否显示
      dislikeBPopShow: false,
      // C端不感兴趣弹框是否显示
      dislikeCPopShow: false,
      dislikeList: [],
      // 不更新最后一条数据
      noUpdateLastMsg: false,
    },
    async onLoad(options) {
      const { login } = store.getState().storage.userState
      const userChooseRole = storage.getItemSync('userChooseRole')
      if (!login) {
        setTimeout(() => {
          this.setData({ toLogin: true })
        }, 100)
        toLogin(true).then(() => {
          this.onLoad(options)
        })
        return
      }
      this.setData({ pageCode: getPageCode() })
      dispatch(actions.timmsgActions.setState({ popupWindowIdentify: '' }))
      dispatch(actions.timmsgActions.setState({ nextReqMessageID: '', scrollLocId: '', messageList: [], moreMes: true }))
      this.onAuMdioonitor()
      await operUtils.initData.call(this, options, userChooseRole == 1)
      // 牛人进入页面后-处理2页的未读消息-获取IM沟通风控弹窗是否弹出
      if (userChooseRole == 2) {
        operUtils.handleTwoPageUnreadMsg.call(this)
      }
    },
    onHide() {
      this.setData({ isHide: true })
    },
    async onShow() {
      const { conversation, isHide, toLogin } = this.data
      if (toLogin) {
        this.setData({ toLogin: false, isHide: false })
        const { login } = store.getState().storage.userState
        !login && wx.$.r.back()
        return
      }
      if (!conversation.conversationID && isHide) {
        wx.$.r.back()
        return
      }
      this.setData({ isHide: false })
      setTimeout(() => {
        this.onTopBtnChange()
      }, 50)
    },
    async onUnload() {
      dispatch(actions.timmsgActions.setState({
        popupWindowIdentify: '',
        riskContent: {
          popupTitle: '',
          imageUrl: '',
          tipsContent: '',
        },
        isShowImChatRisk: false,
        isShowRisk: false,
        havePopImChatRisk: false,
        isShowRiskAndImChatRisk: false,
      }))
      /** ------- start 存储两分钟内数据，用于撤回时，对方没有收到撤回消息回调---------- */
      const twoMinutesConvData = { ...this.data.twoMinutesConvData }
      const nData = []
      const nowTime = new Date().getTime()
      this.data.messageList.forEach((item) => {
        const msgTime = item.time
        const diff = (nowTime - msgTime) / 1000
        if (diff < 120 && item.chatType == 'groupChat') {
          nData.push(item)
        }
      })
      const { query, noUpdateLastMsg } = this.data
      const { toUserImId } = query || {}
      twoMinutesConvData[`C2C${toUserImId}`] = nData
      dispatch(actions.timmsgActions.setState({ twoMinutesConvData }))
      /** ------- end ---------- */
      if (this.data.audioCtx) {
        this.data.audioCtx.destroy()
      }
      if (this.data.messageList.length > 0) {
        const lastMsg = await wx.$.l.getConversationLastMsg(this.data.messageList)
        if (lastMsg) {
          !noUpdateLastMsg && dispatch(actions.messageActions.outConvChange(lastMsg))
          await wx.$.l.sendConverReaded(lastMsg.conversationID)
        } else if (!noUpdateLastMsg) {
          const { conversation } = this.data
          const { conversationID } = conversation || {}
          // 单纯清空未读数
          dispatch(actions.messageActions.outConvClearNum(conversationID))
        }
        setTimeout(() => {
          dispatch(actions.messageActions.fetchMsgTabNum())
        }, 200)
      }
      dispatch(actions.timmsgActions.clearConversation())
    },
    onListReport() {
      operUtils.listReport.call(this, 2)
    },
    onRefreshConversation() {
      const { query } = this.data
      operUtils.getGroupInfo.call(this, query, true)
    },
    onScrollViewClick() {
      const { cwShow, emojiShow, toolbarShow } = this.data
      let sData: any = {}
      if (toolbarShow) {
        sData = { toolbarShow: false }
      }
      if (cwShow) {
        sData = { cwShow: false }
      }
      if (emojiShow) {
        sData = { emojiShow: false }
      }
      this.setData({ ...sData, isShowTk: false })
      setTimeout(() => {
        utils.getChatBottomHeightOnly.call(this)
        utils.getChatTopHeightOnly.call(this)
      }, 50)
    },
    // 获取底部聊天功能高度
    getChatBottomHeightOnly() {
      this.setData({ keyboardHeight: 0 })
      utils.getChatBottomHeightOnly.call(this)
      utils.getChatTopHeightOnly.call(this)
    },
    onTopBtnChange() {
      utils.getChatBottomHeightOnly.call(this)
      utils.getChatTopHeightOnly.call(this)
    },
    onCiviShow() {
      this.setData({ inputFocus: false })
    },
    async onReEdit(e) {
      const { text } = e.detail
      if (text) {
        const btmBtn = await wx.$.selectComponent.call(this, '#btm-btn')
        btmBtn.setData({ chat: text })
        this.setData({ inputFocus: true })
      }
    },
    /** 充值成功返回回调 */
    onRechargeSuccess() {
      const { query, conversation, rechargeType } = this.data
      if (rechargeType == 'reDeduction') {
        this.setData({ rechargeType: '' })
        midUtils.reDeduction.call(this, conversation)
      } else {
        operUtils.getGroupInfo.call(this, query)
      }
    },
    /** 充值弹窗关闭事件 */
    onToggleRechargePopup() {
      this.setData({
        showRechargePopup: false,
        rechargeContent: [],
      })
    },
    onPullDownRefresh() {
      this.setData({ isPull: true })
      dispatch(actions.timmsgActions.setState({ refresher: true, scrollLocId: '' }))
      const { conversation } = this.data
      const { conversationID } = conversation || {}
      setTimeout(() => {
        operUtils.getImMessageList.call(this, conversationID)
      }, 100)
    },
    // 顶部导航拨打电话
    topCallPhone(e) {
      const { type } = e.currentTarget.dataset
      this.onShorcutBtn({ detail: { type } })
    },

    // 快捷按钮
    async onShorcutBtn(e) {
      const { query } = this.data
      const { conversation } = (await operUtils.getGroupInfo.call(this, query, true)) || { conversation: null }
      const { type } = e.detail || {}
      const sData: any = {
        emojiShow: false,
        isShowTk: false,
        toolbarShow: false,
      }
      switch (type) {
        case 'kjhf':
          if (!this.data.cwShow) {
            await utils.getGreetingList()
          }
          sData.cwShow = !this.data.cwShow
          if (!sData.cwShow) {
            sData.inputFocus = true
          }
          break
        case 'call':
          if (!midUtils.jugeIntConRemind.call(this, conversation, { type: 'call' })) {
            return
          }
          midUtils.callMidPhone.call(this)
          break
        default:
          break
      }
      this.setData({ ...sData })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },

    /** 组件回调拨打中间号 */
    async onCallMidPhone(val) {
      const { conversation } = this.data
      const { telRightsInfo, relatedJobId, fromType } = conversation || {}
      const { infoId } = telRightsInfo || {}
      wx.$.l.resumeMidTelV3.call(this, { uuid: infoId, isPrivacy: val.detail == 1 ? 0 : 1, isPopup: 0, jobId: relatedJobId, sceneV2: '21', isChatSearch: fromType == 10 })
    },

    // 操作显示表情包
    async onEmojiShow(e) {
      const sData: any = { cwShow: false, toolbarShow: false, emojiShow: e.detail.emShow, isShowTk: false }
      if (!sData.emojiShow) {
        sData.inputFocus = true
      }
      this.setData({ ...sData })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },
    // 删除常用语
    async onDelGreeting(e) {
      const { grtid } = e.detail
      await wx.$.javafetch['POST/reach/v1/im/greeting/deleteGreeting']({ id: grtid })
      const list = this.data.greetingList.filter((item) => {
        return item.id !== grtid
      })
      dispatch(actions.comwordsActions.setState({ greetingList: list }))
      wx.$.msg('删除成功')
    },
    // 点击常用语
    async onComWordsClick(e) {
      const isRec = midUtils.jugeIntConRemind.call(this, null, {
        type: 'msg',
        chatsend: () => {
          this.cwsAfter(e)
        },
      })
      if (!isRec) {
        return
      }
      this.cwsAfter(e)
    },
    // 常用语点击后续操作
    async cwsAfter(e) {
      const { value } = e.detail
      const btmBtn = await wx.$.selectComponent.call(this, '#btm-btn')
      btmBtn.setData({ chat: value })
      this.setData({ cwShow: false, inputFocus: true })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },

    // 点击聊天类型(语音和文本)
    async onChatTypeClick(e) {
      const isRec = midUtils.jugeIntConRemind.call(this, null, {
        type: 'msg',
        chatsend: async () => {
          const { type } = e.detail || {}
          if (type == 'msg') {
            this.onMsgSend(e)
          } else if (type == 'audio') {
            const btmBtn = await wx.$.selectComponent.call(this, '#btm-btn')
            btmBtn.onChangeAudio()
          }
        },
      })
      if (!isRec) {
        wx.hideKeyboard()
        return
      }
      this.setData({ cwShow: false, emojiShow: false, toolbarShow: false, isShowTk: false })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },
    // 点击用户名片卡片消息跳转
    onResCardMsgClick(e) {
      const { id, isSelf } = e.detail
      const buryingPoint = {
        info: {
          request_id: guid(),
          source_id: '15',
        },
      }
      utils.toResumeDetail(id, isSelf, { buryingPoint: JSON.stringify(buryingPoint) })
    },
    // 点击头像，跳转用户信息页面
    onAvaterClick() {
      operUtils.handleAvaterClick.call(this)
    },
    // 聊天内容发送
    async onChatSend(e) {
      const isRec = midUtils.jugeIntConRemind.call(this, null, {
        type: 'msg',
        chatsend: async () => {
          const { type } = e.detail || {}
          if (type == 'audio') {
            this.onAudioSend(e)
          } else {
            this.onMsgSend(e)
          }
        },
      })
      if (!isRec) {
        return
      }
      const { type } = e.detail || {}
      if (type == 'audio') {
        this.onAudioSend(e)
      } else {
        this.onMsgSend(e)
      }
    },
    // 消息状态点击事件
    onStatusClcik(e) {
      const { type, msgInfo } = e.detail || {}
      // 重新发送消息
      if (type == 'resend') {
        const { noCreate, msgId } = timUtils.delMsg(msgInfo.oId)
        const message = wx.$.tim.findMessage(msgInfo.oId)
        noCreate && utils.setMsgStatus(msgId, 'nostatus')
        // noCreate: 用于判断是否需要重新在会话框中显示消息  msgId:用于环信发送消息时是否需要重新创建消息ID
        wx.$.l.resendMessage(message, {
          pre: (msg) => {
            if (!noCreate) {
              const nMsg = { ...msg }
              nMsg.status = 'nostatus'
              operUtils.handleReceivedOrSendMsg.call(this, nMsg)
            }
          },
          success: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'success')
          },
          fail: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'fail')
          },
        })
      }
    },
    // 语音消息发送
    onAudioSend(e) {
      const { value, type, noCreate } = e.detail || {}
      if (!value || type != 'audio') {
        return
      }
      const { conversation } = this.data
      const { toUserImId } = conversation || {}
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      wx.$.l.sendAudioMessage(
        {
          to: toUserImId,
          value,
        },
        {
          pre: (msg) => {
            const nMsg = { ...msg }
            if (!noCreate) {
              nMsg.status = 'nostatus'
            }
            operUtils.handleReceivedOrSendMsg.call(that, nMsg)
          },
          success: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'success')
          },
          fail: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'fail')
          },
        },
      )
    },
    // 文本消息发送
    async onMsgSend(e) {
      const { value, noCreate } = e.detail
      if (!value) {
        return
      }
      const { imGlobalSwitch } = store.getState().message
      const { sendPhoneNumberInterceptSwitch, phoneNumberRegularExpression } = imGlobalSwitch || {}
      const { conversation } = this.data
      const { toUserImId, rightsStatusInfo } = conversation || {}
      const { exchangeTel } = rightsStatusInfo || {}
      const { value: telValue, status, hasImRight } = exchangeTel || {}
      if (sendPhoneNumberInterceptSwitch && status) {
        if (hasImRight == 0 && !telValue) {
          midUtils.handleAction.call(this, conversation, {
            type: 'msg',
            chatsend: () => {
              this.onMsgSend(e)
            },
          })
          return
        }
        const regex = new RegExp(phoneNumberRegularExpression, 'g')
        const isOK = regex.test(value)
        if (isOK) {
          // 保存输入框的内容
          this.crMsgValue = value
          const etowPop = await wx.$.selectComponent.call(this, '#exchangeTelOrWechatPop')
          etowPop.setData({ isExChangeTelShow: true, crExchangeType: 'SEND_TEL_INTERCEPT_EXCHANGE_TEL' })
          return
        }
      }

      const btmBtn = await wx.$.selectComponent.call(this, '#btm-btn')
      btmBtn.setData({ chat: '' })
      this.gcSendTextMessage({ value, noCreate, toUserImId })
    },

    gcSendTextMessage(params) {
      const { value, noCreate, toUserImId } = params || {}
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      wx.$.l.sendTextMessage(
        {
          to: toUserImId,
          value,
        },
        {
          pre: (msg) => {
            const nMsg = { ...msg }
            if (!noCreate) {
              nMsg.status = 'nostatus'
            }
            operUtils.handleReceivedOrSendMsg.call(that, nMsg)
          },
          success: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'success')
          },
          fail: (msgres) => {
            utils.setMsgStatus(msgres.ID, 'fail')
          },
        },
      )
    },

    async onCrCallback(e) {
      const { type, crExchangeType } = e.detail
      if (crExchangeType == 'SEND_TEL_INTERCEPT_EXCHANGE_TEL') {
        const btmBtn = await wx.$.selectComponent.call(this, '#btm-btn')
        if (type == 'close' && this.crMsgValue) {
          const { conversation } = this.data
          const { toUserImId } = conversation || {}
          this.gcSendTextMessage({ value: this.crMsgValue, toUserImId })
          btmBtn.setData({ chat: '' })
          this.crMsgValue = ''
        } else if (type == 'confirm') {
          btmBtn.setData({ chat: this.crMsgValue })
          this.crMsgValue = ''
        }
      }
    },
    // 修改语音播放ID
    onAudioId(e) {
      const { isread, isself, id } = e.target.dataset
      const sData: any = { audioId: e.detail.audioId }
      if (!isself && !isread) {
        const messageList = [...(this.data.messageList || [])]
        this.data.messageList.forEach((item, idx) => {
          const { isRead, flow, needReadReceipt, isRevoked } = item || {}
          if (item.id == id) {
            if (!isRead && flow == 'in' && needReadReceipt && !isRevoked) {
              wx.$.l.msgReadReceipt([item])
            }
            messageList[idx] = { ...item, isRead: true }
          }
        })
        dispatch(actions.timmsgActions.setState({ messageList, scrollLocId: '' }))
      }
      this.setData({ ...sData })
    },
    // 系统消息按钮点击事件
    async onSysMsgClick(e) {
      const { ctype } = e.detail
      if (ctype == 'gtry') {
        const { toolbarIcon } = this.data
        if (!wx.$.u.isArrayVal(toolbarIcon)) {
          await dispatch(actions.messageActions.getGlobalSwitch())
        }
        this.setData({ cwShow: false, emojiShow: false, toolbarShow: true })
        setTimeout(() => {
          operUtils.getChatBottomHeight.call(this)
        }, 50)
      }
    },
    // 更多工具栏点击事件
    async onMoreToolBarChange() {
      const { toolbarShow, toolbarIcon } = this.data
      // 获取工具栏数据
      if (!wx.$.u.isArrayVal(toolbarIcon)) {
        await dispatch(actions.messageActions.getGlobalSwitch())
      }
      const sData: any = { cwShow: false, emojiShow: false }
      sData.toolbarShow = !toolbarShow
      if (!sData.toolbarShow) {
        sData.inputFocus = true
      }
      this.setData({ ...sData })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },
    // 工具栏功能触发
    onToolChange(e) {
      const { type, value } = e.detail || {}
      const { tempFilePaths } = value || {}
      if (type == 'photo' && wx.$.u.isArrayVal(tempFilePaths)) {
        operUtils.toolSendImg.call(this, e)
      } else if (type == 'change_position') {
        wx.$.collectEvent.event('change_position_button_click', { source: '3' })
        operUtils.toolChangePosition.call(this)
      } else if (type == 'location') {
        operUtils.toolSendLocation.call(this, e)
      }
    },
    onSpClose() {
      this.setData({ spVisible: false })
    },
    onSpConfirm(e) {
      operUtils.changeSpJob.call(this, e)
    },
    // 点击自动回复消息的拨打手机号
    async onMsgBtnClick() {
      const isRec = midUtils.jugeIntConRemind.call(this, null, { type: 'call' })
      if (!isRec) {
        return
      }

      midUtils.callMsgMidPhone.call(this)
    },
    audioPlay(e) {
      if (this.data.audioCtx) {
        const { remoteAudioUrl } = e.detail
        this.setData({ remoteAudioUrl })
        this.data.audioCtx.src = remoteAudioUrl
        this.data.audioCtx.play()
      }
    },
    audioPause() {
      if (this.data.audioCtx) {
        this.data.audioCtx.stop()
      }
    },
    onAuMdioonitor() {
      if (!this.data.audioCtx) {
        this.data.audioCtx = wx.createInnerAudioContext()
        if (wx.setInnerAudioOption) {
          wx.setInnerAudioOption({ obeyMuteSwitch: false })
        } else {
          this.data.audioCtx.obeyMuteSwitch = false
        }
        this.data.audioCtx.onError(() => {
          this.data.audioCtx.src = 'http://1'
          const { remoteAudioUrl } = this.data
          if (remoteAudioUrl && isIos()) {
            setTimeout(() => {
              this.data.audioCtx.src = remoteAudioUrl
              this.data.audioCtx.play()
              this.setData({ remoteAudioUrl: '' })
            }, 50)
          } else {
            wx.$.msg('语音文件已损坏或不存在')
            this.setData({ isPlay: false })
          }
        })
        this.data.audioCtx.onEnded(() => {
          this.setData({ isPlay: false })
        })
      }
    },
    bindkeyboardheightchange(e) {
      const sData: any = { keyboardHeight: e?.detail?.height || 0 }
      if (sData.keyboardHeight > 0) {
        sData.cwShow = false
        sData.emojiShow = false
        sData.toolbarShow = false
      }
      this.setData({ ...sData })
      setTimeout(() => {
        operUtils.getChatBottomHeight.call(this)
      }, 50)
    },
    onUpdateWechatClose() {
      this.setData({ isUpdateWechatShow: false, exchangeWxMsgId: '', exchangeType: '' })
    },
    onAddWechat(e) {
      const { exchangeWxMsgId, exchangeType } = e.detail
      this.setData({ isUpdateWechatShow: true, exchangeWxMsgId, exchangeType })
    },
    async onBossReqFile(e) {
      const { exchangeMsgId, exchangeType } = e.detail
      const topbtn = await wx.$.selectComponent.call(this, '#topbtn')
      topbtn.onWorkerSendResumeFile(exchangeMsgId, exchangeType)
    },
    // 联系按钮展示
    async contactBtnText() {
      const pageCode = getPageCode()
      const userChooseRole = storage.getItemSync('userChooseRole')
      const role = userChooseRole == 1 ? 'b' : 'c'
      await messageQueue((state) => !!state.config.btnConfigStatus[`${pageCode}_${role}`])
      // 联系按钮配置
      const { list_b, list_c } = storage.getItemSync(`btn_${pageCode}`)
      const btnConfig = userChooseRole == 1 ? list_b : list_c
      const { conversation } = this.data
      const { infoDetail, telRightsInfo } = conversation
      const { relatedInfoOccIds = '' } = infoDetail || {}
      let showTelBtn = false// 显示按钮数据
      const config = [] // 按钮配置
      const occV2 = (relatedInfoOccIds || '').split(',')
      occV2.forEach((i) => {
        const obj = wx.$.u.isArrayVal(btnConfig) ? btnConfig.find((j) => j.occId == i) : null
        if (obj) {
          config.push(obj)
        }
      })

      config.sort((a, b) => {
        // 如果优先级（priNum）相同，则按照规则id排序
        // 如果优先级（priNum）不同，则按照优先级（priNum）排序
        return a.priNum === b.priNum ? b.id - a.id : b.priNum - a.priNum
      })

      if (wx.$.u.isArrayVal(config) && telRightsInfo && telRightsInfo.isShowTelButton) {
        const [hConfig] = config
        showTelBtn = hConfig.buttonInfoList.findIndex(i => i.buttonCode.indexOf('phone') >= 0) >= 0
      }
      this.setData({ showTelBtn })
    },
    // 关闭 - 风险提示弹窗
    onCloseRecruitRisk() {
      dispatch(actions.timmsgActions.setState({
        isShowRiskAndImChatRisk: false,
      }))
    },
    onShowFollowChatEdit(e) {
      const { value } = e.detail
      this.setData({ followChatEditShow: true, followChatEditContent: value })
    },
    onCloseFollowChatEdit() {
      this.setData({ followChatEditShow: false, followChatEditContent: '' })
    },
    onShowChangeTelOrWechatPop() {
      this.setData({ exchangeTelOrWechatPopShow: true })
    },
    onCloseExchangeTelOrWechatPop() {
      this.setData({ exchangeTelOrWechatPopShow: false })
    },
    onDislikePop() {
      const { role } = this.data
      wx.showLoading({ title: '加载中...' })
      wx.$.javafetch['POST/clues/v1/inappropriate/configList']().then(res => {
        wx.hideLoading()
        const { data } = res || {}
        const { list } = data || {}
        if (role == 1) {
          this.setData({ dislikeBPopShow: true, dislikeList: list })
          return
        }
        this.setData({ dislikeCPopShow: true, dislikeList: list })
      }).then(() => {
        wx.hideLoading()
      })
    },
    onCloseDislikeBPop() {
      this.setData({ dislikeBPopShow: false })
    },
    onCloseDislikeCPop() {
      this.setData({ dislikeCPopShow: false })
    },
    async onRefreshGroupInfo() {
      this.setData({ noUpdateLastMsg: true })
      setTimeout(() => {
        const pages = getCurrentPages()
        const idx = pages.findIndex(page => page.route.indexOf('pages/msg-page/index') >= 0)
        const backNum = pages.length - idx - 1
        if (idx >= 0 && backNum > 0) {
          wx.$.r.back(backNum)
        } else {
          wx.$.r.reLaunch({ path: '/pages/msg-page/index' })
        }
      }, 50)
    },

  }),
)
