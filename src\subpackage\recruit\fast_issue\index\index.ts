import { SHARE_CHANNEL } from '@/config/share'
import miniConfig from '@/miniConfig/index'
import { RootState, actions, dispatch, storage, store } from '@/store/index'
import { completeShare, createTrackId, getShareInfo, getShareInfoByType } from '@/utils/helper/share/index'
import {
  setShareInfo,
  toResumeIndexOfSaveClassify, transformClassifyOccv2,
  validateForm,
  formatAddressWithFormData,
  occV2ToClsId,
  judgeEveryLevel2,
} from './utils'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { tryPromise } from '@/utils/tools/common/index'
import type { IGetReportDataParams, IPublishBuryPointEventData, LiteralText, MergedData } from './type'
import { resume_page } from './constants'
import { fetchResumeExist } from '@/utils/helper/resume/index'
import { changeUserRole } from '@/utils/helper/member/communicate'
import { toLogin } from '@/utils/helper/common/toLogin'

const Bury = wx.$.l.bury()

Page(class FastIssue extends wx.$.Page {
  data = {
    miniToken: miniConfig.token,
    /** 发布渠道，初始0获取配置后根据接口返回走流程1,2 */
    serial: 0,
    /** 展示新的挽留弹窗 */
    popNewRetention: false,
    /** 检查内部员工 */
    isEmployee: false,
    /** 是否展示分享弹窗 */
    showSharePublishModal: false,
    /** 分享信息 */
    sharePublishData: <any>{},
    /** 工种选择器是否展示推荐 */
    classifyShowRecommend: false,
    /** 兼容选择器异常 */
    selectedAddr: '',

    /** 页面参数 */
    pageOptions: <any>{},
    /** 是否展示企业名称弹窗 */
    showBiznameDialog: false,
    /** 弹窗配置 */
    dialogData: {},

  }

  /** 业务组件，发布流程1 发布流程2 */
  serialComponent = null

  /** 短信注册发布 */
  isSMSPublish = false

  /** 是否来自私域链接，私域链接需要在发布埋点中新增字段source */
  isSiyu = false

  /** 获取业务组件 */
  async getSerialComponent() {
    if (this.serialComponent === null) {
      this.serialComponent = await wx.$.selectComponent.call(this, '#fast-publish')
    }
    return this.serialComponent
  }

  handleBizDialogHidden() {
    this.setData({
      showBiznameDialog: false,
    })
  }

  getABTest() {
    wx.$.u.getAbUi('pub_reco', 'pub_show').then(res => {
      res && this.setData({
        classifyShowRecommend: true,
      })
    })
  }

  /**
   * @warn 调用时，缓存组件值，返回到当前页面时通过setForm参数将组件值回填
   *  */
  selectCommon(selectedAddr: any, setForm: boolean) {
    if (setForm) {
      const { selectedAddr } = this.data
      if (selectedAddr) {
        this.getSerialForm().then(form => {
          form.setValues({
            current_area: selectedAddr,
          })
        })
      }
    }
    this.setData({
      selectedAddr,
    })
  }

  // 检查内部员工
  async getShowShareBuoy() {
    const res = await wx.$.javafetch['POST/account/v1/setting/check/internal/employee']()
    if (res.code == 0) {
      this.setData({
        isEmployee: res.data.isEmployee,
      })
    }
  }

  /**
   * 获取流程表单
   * @returns
   */
  async getSerialForm() {
    const serialComponent = await this.getSerialComponent()
    return wx.$.selectComponent.call(serialComponent, '#formArea')
  }

  /**
  * 从子组件中获取表单值
  * */
  async getSerialFormData() {
    return this.getSerialForm().then(async (widget) => widget.getValues())
  }

  async onLoad(options = {} as any) {
    this.isSiyu = options.sourceType == 'siyu'
    /** 发布职位页埋点 */
    wx.$.collectEvent.event('post_job_page_expose')
    /** 页面初始化时首先清楚竞招标识，防止onShow 直接执行发布逻辑 */
    storage.removeSync('jobVieCheckType')
    /** 清除存待开放标记，防止onShow方法上错误的触发待开放逻辑 */
    storage.removeSync('toPurchaseJobVieData')
    this.setData({
      pageOptions: options,
    })
    this.getABTest()
    const userState = storage.getItemSync('userState')
    /** 短信注册发布 */
    this.isSMSPublish = options.occV2 && !options.flag

    /** 发布招工2 */
    const secondSerial = miniConfig.token === 'gdh' || miniConfig.token === 'findjzgr'
    /** 强制流程2 */
    const forceSerialTwo = options.sourceType === '2'

    /** 如果已经登录，获取一次招工配置 */
    await tryPromise(dispatch(actions.recruitFastIssueActions.fetchGetNewJobConfig()))
    const { newIssueJobConfig } = <MergedData<FastIssue>> this.data
    /**
     * 鱼泡招工或鱼泡快招， 强制流程2 或者（不属于flag(modify2publish)定价系统访问时。未登录，或配置了流程2时。）走流程2发布
     */
    const serial = (secondSerial && (forceSerialTwo || (!options.flag && (!userState.login || (newIssueJobConfig && newIssueJobConfig.hasPublishFlowTwo))))) ? 2 : 1
    this.setData({
      serial,
    })
    // 发布流程一需要弹
    if (serial == 1 || (serial == 2 && (!(options.userAcq == '2' || options.userAcq == '4')))) {
      wx.$.selectComponent.call(this, '#privacy-protocol').then((c) => c.render(false))
    } else {
      await dispatch(actions.storageActions.setItem({ key: 'isShowServicePrivacyV5', value: false }))
      this.onAgreeOk()
    }
    if (this.options.isFromRolePop) {
      setTimeout(() => wx.$.msg('您已成功切换到老板端'), 500)
    }
  }

  async unload() {
    this.setData({
      showRetention: false,
    })
  }

  async onShow() {
    console.warn('fast-issue page show with serial', this.data.serial)
    this.selectCommon('', true)
    const userState = storage.getItemSync('userState')
    if (ENV_SUB === 'gdh' && userState.login) {
      this.getShowShareBuoy()
    }

    /** 竞招会员页返回后需要直接发布 */
    const jobVieCheckType = storage.getItemSync('jobVieCheckType')
    storage.removeSync('jobVieCheckType')
    if (jobVieCheckType) {
      /** 购买了竞招会员，需清理缓存待开放数据，防止异常 */
      storage.removeSync('toPurchaseJobVieData')
      this.getSerialComponent().then(async (widget) => {
        const formData = await wx.$.selectComponent.call(widget, '#formArea').then((form) => form.getValues())
        return [widget.data, formData]
      }).then(([data, formData]) => this.checkCompliance(data, formData, { publishType: jobVieCheckType === 'COMMON' ? 1 : undefined, ignoreDialog: ['wjzqytc'] }))
    } else {
      /** 如果是从竞招购买页返回，但是未购买竞招会员存草稿箱 */
      const data = storage.getItemSync('toPurchaseJobVieData')
      storage.removeSync('toPurchaseJobVieData')
      if (data && Object.keys(data).length) {
        await wx.$.l.saveDraft(data, '信息已存入草稿箱，可在管理职位-待开放中继续发布')
        wx.$.r.reLaunch({
          path: '/subpackage/recruit/published/index',
          query: { activeTab: 'waiting' },
        })
      }
    }
  }

  /** 流程1流程2 登录成功后需要获取展示按钮信息 */
  onGetEmployeeType() {
    this.getShowShareBuoy()
  }

  /** 鱼泡招工发布页分享浮标点击 */
  clickBuoy() {
    this.setData({
      showSharePublishModal: true,
    })
  }

  /** `隐私协议` 将基于页面的回调事件透传到组件上 */
  async onAgreeOk() {
    this.getSerialComponent().then((widget) => {
      try {
        widget.onAgreeOk()
      } catch (error) {
        console.error(error)
      }
    })
  }

  async getSharePublishModalInfo() {
    const userState = storage.getItemSync('userState')
    /** 分享信息 */
    const shareInfo = {
      sharePage: 'subpackage/recruit/details/index',
      sharePath: 'rec_detail_btm_friend',
      track_seed: createTrackId(),
      path: '/subpackage/recruit/fast_issue/fast_pulish_second/index',
      source: miniConfig.shareSource,
      refid: userState.userId,
    }

    const res = await completeShare(4, shareInfo)
    const { sharePublishData } = this.data
    return {
      desc: '',
      imageUrl: sharePublishData.path,
      // eslint-disable-next-line max-len
      path: `/subpackage/recruit/fast_issue/fast_pulish_second/index?isShare=true&occV2=${sharePublishData.occV2}&refid=${shareInfo.refid}&source=${shareInfo.source}&track_seed=${res.data?.trackSeed}`,
      title: sharePublishData.text,
    }
  }

  /** 设置当前页面的分享内容 */
  async onShareAppMessage(options) {
    const userLocationCity = storage.getItemSync('userLocationCity')
    const { letter, resumeCityObj } = userLocationCity || {}
    const { letter: recLetter } = resumeCityObj || {}

    if (options.from === 'button' && this.data.showSharePublishModal) {
      return this.getSharePublishModalInfo()
    }

    if (ENV_SUB === 'gdh') {
      if (this.data.pageOptions && this.data.pageOptions.userAcq) {
        options.userAcq = this.data.pageOptions.userAcq
      }
      // if (this.data.serial === 2) { // 流程2分享
      return this.getSerial2ShareInfo(options)
    }
    const shareInfo = getShareInfoByType(SHARE_CHANNEL.SHARE_WECHAT_FRIEND)

    return getShareInfo({
      path: `${shareInfo.path}&area=${recLetter || letter}`,
      title: '最近找活用户信息全在这里',
      imageUrl: 'https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_wechat_share.png',
      from: options.from,
    })
  }

  /** 流程2分享 */
  async getSerial2ShareInfo(options) {
    /** 随机数 */
    const min = 1500
    const max = 3000
    const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min
    const serialComponent = await this.getSerialComponent()
    const form = await wx.$.selectComponent.call(serialComponent, '#trades')
    let cl: any = ''
    if (form && form.data && form.data.valueLabel) {
      const array = form.data.valueLabel.split('、') || []
      cl = array[0] || ''
    }
    const title = `附近${randomNumber}位牛人正在找${cl}工作`
    const ext: any = {}
    if (this.data.pageOptions && this.data.pageOptions.id) {
      ext.key = 'id'
      ext.val = this.data.pageOptions.id
    }

    return setShareInfo({ ...options, ...ext }, title)
  }

  /** 关闭SharePublishModal */
  hideSharePublishModal() {
    this.setData({
      showSharePublishModal: false,
      sharePublishData: {},
    })
  }

  /**
   * 已登录状态下，组件触发发布逻辑
   * @param {{ detail: FastIssue["data"] }} event
   */
  async submit(event: any) {
    await wx.$.u.waitAsync(this, this.submit, [event], 2000)
    /** 获取表单数据 */
    const formData = await this.getSerialFormData()
    let areaId
    try {
      let { selectArea = {}, selectCity = {} } = formData.current_area
      const { newSelectArea, newSelectCity } = await tryPromise(formatAddressWithFormData(formData.current_area), { newSelectArea: selectArea, newSelectCity: selectCity })
      selectArea = newSelectArea
      selectCity = newSelectCity
      areaId = selectArea.areaInfo ? selectArea.areaInfo.id : (selectArea.id || selectCity.id)
    } catch (error) {
      /** noop */
    }

    const needHiringClient = wx.$.u.getObjVal(this.data, 'newIssueJobConfig.needHiringClient')
    /** 表单校验 */
    await validateForm({ ...event.detail, areaId, ...formData, serial: this.data.serial, needHiringClient })
      .then(() => {
        wx.showLoading({ title: '发布中', mask: true })
        /** 合规校验 next step */
        this.checkCompliance(event.detail, formData)
      })
      .catch(async (msg) => {
        Bury.then((bury) => {
          bury.operation({
            data: msg,
            functionName: 'submit',
            name: '发布职位',
            serial: this.data.serial,
          })
        })
        wx.$.msg(msg)
      })
  }

  /**
   * @private
   * 合规校验
   * @param data 触发提交逻辑的组件的data
   * @param formData 触发提交逻辑的组件所对应的表单数据
   */
  async checkCompliance(data: Record<string, any>, formData: Record<string, any>, extra: Record<string, any> = {}) {
    console.warn('[INTERNAL_CHECK_COMPLIANCE] received objects:', data, formData)
    // const { phone, bindTel, code, closeInfoId } = data
    const { trades } = formData
    const { templates: _, basicConfig: _a, templateSource, ...mData } = data
    /** 判断是否需要走完善信息 */
    const completable = templateSource?.some(item => wx.$.u.getObjVal(item, 'templateInfo.controlInfoList', []).filter(it => it.jobDisplayPage == 'jobPerfect').length)

    /** 获取工种 ID 的数组 */
    const occV2Ids = Array.isArray(trades) ? trades.map(item => item.id) : []

    const params = await transformPublishData(data, formData)

    // 如果最终未获取到地址信息，引导用户重新选择地址~
    if (!params.areaId) {
      try {
        wx.$.collectEvent.event('checkAreaError', {
          jobConfig: JSON.stringify(this.data.newIssueJobConfig),
          mFormData: JSON.stringify(formData),
          mData: JSON.stringify(mData),
          mParam: JSON.stringify(params),
          serial: this.data.serial,
        })
      } catch (error) {
        console.error(error)
      }
      wx.$.msg('地址信息获取异常，请重新选择')
      return
    }

    try {
      wx.$.collectEvent.event('handlePublishInfo', {
        jobConfig: JSON.stringify(this.data.newIssueJobConfig),
        mFormData: JSON.stringify(formData),
        mParam: JSON.stringify(params),
        mData: JSON.stringify(mData),
        serial: this.data.serial,
      })
    } catch (error) {
      console.error(error)
    }

    const reportData = await this.getReportData({ typeText: '成功', params, data })
    const isWindows = wx.getSystemInfoSync().platform == 'windows'

    /** 所选工种为招聘类，且发布按钮类型为3(下一步) 且 有完善项模板 */
    if (data.publishBtnStatus === 3 && completable) {
      /** 包含完善逻辑的预检接口 */
      const navigateParams = await wx.$.l.deleteNullProp({
        ...params,
        isPublish: true,
        occIds: wx.$.u.uniqueArr(occV2Ids),
        copyId: data.copyId,
        isSMSPublish: this.isSMSPublish,
        publishBtnStatus: data.publishPurchaseStatus,
        // completeSetting,
        origin: 'fast_issue',
        serial: this.data.serial,
        templateSource: data.templateSource,
        userAcq: this.data.serial == 2 ? this.data.pageOptions.userAcq : undefined,
      })
      wx.hideLoading()
      if (isWindows) {
        wx.$.l.jumpToCompleteWin(this, navigateParams, reportData)
        return
      }
      wx.$.l.jumpToComplete(navigateParams, reportData)
    } else { /** 否则直接走常规预检接口 */
      const reportData = await this.getReportData({ typeText: '成功', params, data })
      const prevCheckParams = { ...params, ...extra }
      await wx.$.l.prevCheckWithDraft(
        <any>prevCheckParams,
        {
          serial_number: '流程1',
          reportData,
          ignoreDialog: extra.ignoreDialog,
        },
        async (temporary, response) => {
          /** 流程2 */
          if (this.data.serial === 2) {
            dispatch(actions.storageActions.removeItem('fastIssueContent'))
          }
          this.reportPublishEvent({ typeText: '成功', params, response, data })

          const jobId = wx.$.u.getObjVal(response, 'data.data.id', '')

          const toCompleteOcc = <LiteralText[]>wx.$.u.getObjVal(response, 'data.data.toCompleteOcc', [])
          /** 订单类工种具有完善项的模板 */
          const perfTemplateSource = templateSource.filter(item => toCompleteOcc && toCompleteOcc.some(id => id == item.occId))
          /** (订单类) 追加完善项 */
          if (perfTemplateSource.length) {
            const navigateParams = await wx.$.l.deleteNullProp({
              ...params,
              isPublish: false,
              occIds: wx.$.u.uniqueArr(perfTemplateSource.map(item => item.occId)),
              jobId,
              copyId: data.copyId,
              isSMSPublish: this.isSMSPublish,
              userAcq: this.data.serial == 2 ? this.data.pageOptions.userAcq : undefined,
              publishBtnStatus: data.publishPurchaseStatus,
              origin: 'fast_issue',
              type: 'update',
            })
            const isWindows = wx.getSystemInfoSync().platform == 'windows'
            if (isWindows) {
              wx.$.l.jumpToCompleteWin(this, navigateParams, reportData)
              return
            }
            wx.$.l.jumpToComplete(navigateParams, reportData, 'replace')
            return
          }

          if (!temporary && this.isSMSPublish) {
            const { data } = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/publish/checkFirst']({}))
            if (data && data.status) {
              const classifyId = await wx.$.l.transformOccV2ToHidClsId(params.occV2)
              wx.$.r.replace({
                path: '/subpackage/recruit/fast_issue/follow_wechat/index',
                query: {
                  classifyId,
                  areaId: params.areaId,
                  jobId,
                },
              })
              return
            }
          }
          /** 临时发布点击去认证，不需要跳转到置顶页 */
          if (temporary) return
          wx.$.l.jumpToRecruitTopSet(jobId)
        },
      ).catch(async (error) => {
        /** 如果抛出错误是个对象，且存在dialogIdentify字符串包含在特殊弹窗标识列表中 */
        if (error && ['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(error.dialogIdentify)) {
          const published = error.dialogIdentify == 'Guidetofillafterpublishing'
          if (published && error.jobId) {
            const newReportData = { ...(reportData || {}), info_id: error.jobId }
            wx.$.collectEvent.event('releaseRecruitment', newReportData)
          }
          if (published && Number(this.data.serial) === 2) { /** 如果来自流程2 */
            dispatch(actions.storageActions.removeItem('fastIssueContent'))
          }

          if (published && this.isSMSPublish) {
            const { data } = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/publish/checkFirst']({}))
            if (data && data.status) {
              const classifyId = await wx.$.l.transformOccV2ToHidClsId(prevCheckParams.occV2)
              wx.$.r.replace({
                path: '/subpackage/recruit/fast_issue/follow_wechat/index',
                query: {
                  classifyId,
                  areaId: prevCheckParams.areaId,
                  jobId: error.jobId,
                },
              })
            }
          }

          // TODO 获取弹窗配置，唤起弹窗
          const popup = await dealDialogRepByApi(error.dialogIdentify)
          if (popup) {
            /** 弹出弹窗 */
            this.setData({
              showBiznameDialog: true,
              dialogData: {
                ...popup,
                ...error,
              },
            })
          }
        }
      })
    }
    /** 通过合规词校验直接走发布预检，不走后续弹窗逻辑 */
  }

  /**
   * 上报发布事件
   * @param {IGetReportDataParams} props
   */
  async reportPublishEvent(props: IGetReportDataParams) {
    // 获取上报数据
    const reportData = await this.getReportData(props)
    wx.$.collectEvent.event('releaseRecruitment', reportData)
  }

  /**
   * 发布招工埋点上报数据
   * @see {@link JavaFetch["POST/job/v2/manage/job/publish/job"]} 发布招工预检接口
   * @param {IGetReportDataParams} _ 获取上报数据所需参数
   * @property {string} typeText 上报类型 enum("成功","失败", "待开放")
   * @property {Partial<SingleParamOf<JavaFetch["POST/job/v2/manage/job/publish/preCheck"]>>} params 预检接口的参数
   * @property {Partial<YResponseTypes['POST/job/v2/manage/job/publish/job']>} response 预检接口的返回
   * @property {Record<string, any>} data 发布招工组件的data
   */
  async getReportData({ typeText, params = {}, response = {}, data = {} }: IGetReportDataParams): Promise<IPublishBuryPointEventData> {
    /** 提取数据中的班级组选择和新发布任务配置 */
    const { haveSelectedClassGroup, newIssueJobConfig } = data
    let tradesStr = ''
    /** 快速选中标签初始化 */
    const quickTags: string[] = []
    /** 获取选中标签 */
    Array.isArray(haveSelectedClassGroup) && haveSelectedClassGroup.forEach((item) => {
      item && Array.isArray(item.occupations) && item.occupations.forEach((solo) => {
        solo && quickTags.push(solo.name)
      })
    })

    let level_2_work = ''
    let level_3_work = ''
    const { current_area = {}, trades } = await this.getSerialFormData()
    const { newSelectArea, newSelectCity } = await formatAddressWithFormData(current_area)

    const tradesName = Array.isArray(trades) ? trades.map(item => item.name).join('、') : ''
    if (Array.isArray(trades) && judgeEveryLevel2(trades)) {
      level_2_work = tradesName
    } else {
      level_3_work = tradesName
    }

    tradesStr = await occV2ToClsId(trades)

    const cityName = newSelectCity.city_name ? newSelectCity.city_name : newSelectCity.name
    const quickTagsLabel = quickTags.length > 0 ? quickTags.join('/') : ''

    const areaInfoName = wx.$.u.getObjVal(newSelectArea, 'areaInfo.name')
    const areaName = wx.$.u.getObjVal(newSelectArea, 'name')

    const event: IPublishBuryPointEventData = {
      serial_number: this.data.serial === 1 ? '流程1' : '流程2', // 流程1
      published_work: tradesStr,
      release_city: `${cityName}_${areaInfoName || areaName}`,
      published_results: typeText,
      level_2_work,
      level_3_work_click: level_3_work,
      level_3_work_exposure: newIssueJobConfig?.newUser ? '新发布用户' : quickTagsLabel,
      spelling: '1',
      display_verification: params && params.code ? '1' : '0',
      source: this.isSiyu ? '私域' : '',
    }

    if (ENV_SUB === 'findjzgr') {
      event.recommend_id = storage.getItemSync('sourceCode') || ''
    }
    if (typeText == '成功') {
      const info_id = wx.$.u.getObjVal(response, 'data.data.id', '')
      event.info_id = info_id
    }
    if (data.isWarning) {
      event.sensitive_words = data.isWarning
    }

    return event
  }

  /**
   * 关闭挽留弹窗
   */
  async retentionClose(event: any) {
    const { type, reason } = event.detail
    console.warn(event.detail)
    this.setData({
      popNewRetention: false,
    })

    if (reason) {
      wx.$.collectEvent.event('rnPageClickButton', {
        click_button: reason,
      })
    }

    if (type === 'confirm') {
      this.reportPublishEvent({ typeText: '失败' })
      if (this.isSMSPublish) { // 短信发布
        const { trades } = await this.getSerialFormData()
        const occV2s = await transformClassifyOccv2(trades)
        await toResumeIndexOfSaveClassify('', occV2s)
        await changeUserRole(1, false)
        wx.$.r.reLaunch({
          path: resume_page,
        })
      } else {
        wx.$.r.back()
      }
    }
  }

  /** 来自于角色选择页面的返回逻辑 */
  async backWithRoleCheck() {
    /** 未配置弹窗, 直接返回到上一个页面 */
    const popup = await dealDialogRepByApi('dianjifanhuixuanzejuese')
    if (!popup) {
      this.reportPublishEvent({ typeText: '失败' })
      return wx.$.r.back()
    }

    return wx.$.showModal({
      ...popup,
      success: async ({ routePath, btnIndex }) => {
        if (btnIndex == -1) return
        this.reportPublishEvent({ typeText: '失败' })
        if (routePath === 'change') {
          const { isLogin } = <MergedData<typeof this>> this.data
          await changeUserRole(2)
          if (!isLogin) {
            toLogin().then(() => {
              wx.$.javafetch['POST/account/v1/role/changeRole']({ role: 2 })
              return fetchResumeExist()
            }).then(async (existData) => {
              if (existData && existData.exist) {
                wx.$.nav.reLaunch(
                  '/subpackage/resume/publish/index',
                )
              } else {
                this.onBackResume()
              }
            })
            return
          }
          fetchResumeExist().then(existData => {
            if (existData && existData.exist) {
              /** 跳转到我的简历详情 */
              wx.$.r.reLaunch({ path: '/subpackage/resume/publish/index' })
            } else {
              /** 跳转到发布简历 */
              this.onBackResume()
            }
          })
        } else {
          this.backToResume()
        }
      },
    })
  }

  /** 切换成牛人角色的时没有发布简历的逻辑 */
  async onBackResume() {
    wx.$.r.reLaunch({ path: '/subpackage/resume/resume_publish/index' })
  }

  /** 返回到找活列表 */
  async backToResume() {
    /**
     * 跳转到找活列表
     * 如果找活列表已选中工种城市，则不携带参数
    */
    const { resumeOption } = store.getState().storage.common
    if (resumeOption.city && resumeOption.classify) {
      await changeUserRole(1, false)
      wx.$.r.reLaunch({
        path: resume_page,
      })
    } else {
      /** 如果是从角色弹窗来的,就带工种和地址到找活列表 */
      const { trades, current_area } = await this.getSerialFormData()
      const defaultTopArea = current_area ? (current_area.area_id || current_area.id || '') : ''
      const isSelectedArea = !!current_area?.adcode
      const isSelectedClassify = Array.isArray(trades) && trades.length > 0
      const occV2s = await transformClassifyOccv2(trades)
      let areaData: any
      let occData = []
      /** 只有在发布招工存在城市信息，且找牛人列表是全国时带入城市数据 */
      if (isSelectedArea) {
        const userLocationCity = storage.getItemSync('userLocationCity')
        const { id, resumeCityObj } = userLocationCity || {}
        const { id: recId } = resumeCityObj || {}
        if ((recId || id) && Number(recId || id) === 1) {
          areaData = defaultTopArea
        }
      }
      /** 如果选择了工种信息，则携带工种数据到找活列表 */
      if (isSelectedClassify) {
        occData = occV2s
      }

      /** 更新找活列表参数 */
      await toResumeIndexOfSaveClassify(areaData, occData)
      await changeUserRole(1, false)
      wx.$.r.reLaunch({
        path: resume_page,
      })
    }
  }

  /** 发布页，返回逻辑 */
  async handleNavBack() {
    await wx.$.u.waitAsync(this, this.handleNavBack, [], 500)
    /**
     * 简历列表、简历详情联系受限跳转到发布页时，点击返回直接返回到上一个页面
     */
    if (this.data.pageOptions?.flag == 'contact_limit') {
      this.reportPublishEvent({ typeText: '失败' })
      wx.$.r.back()
      return
    }
    /** 来源角色选择弹窗 */
    if (this.options.isFromRolePop) {
      this.backWithRoleCheck()
      // wx.$.r.back()
      return
    }
    const publishRecRetentionDateLocal = storage.getItemSync('publishRecRetentionDateLocal')
    const nowDate = new Date().toLocaleDateString()
    /** 每日首次弹出新的挽留弹窗 */
    const shouldPopNewRetention = nowDate !== publishRecRetentionDateLocal
    /** 获取弹窗配置 */
    const retention = await dealDialogRepByApi('FBZWWL_2')

    const config = store.getState().recruitFastIssue.newIssueJobConfig
    const { publishQuestion = [] } = config || {}
    /** 如果需要弹窗新的挽留弹窗，更新弹出日期（每天只弹出一次） */
    if (shouldPopNewRetention && retention && Array.isArray(publishQuestion) && publishQuestion.length > 0) {
      dispatch(actions.storageActions.setItem({ key: 'publishRecRetentionDateLocal', value: nowDate }))
      this.setData(<MergedData<FastIssue>>{
        popNewRetention: true,
      })
      return
    }

    const pages = getCurrentPages()
    const prev = pages[pages.length - 2]
    const path = prev ? prev.route : ''
    const popup = await dealDialogRepByApi('releaseRetentionPop')
    if (!popup) {
      wx.$.r.back()
      return
    }
    wx.$.showModal({
      ...popup,
      success: async ({ itemClass }) => {
        if (itemClass !== 'cancel') return
        this.reportPublishEvent({
          typeText: '失败',
        })
        /** 如果是从角色弹窗来的,就带工种和地址到找活列表 */
        if (!path) {
          this.backToResume()
        } else {
          wx.$.r.back()
        }
      },
    })
  }

  useStore(state: RootState) {
    return {
      newIssueJobConfig: state.recruitFastIssue.newIssueJobConfig,
      isLogin: state.storage.userState.login,
    }
  }
})
/**
 * 处理发布数据
 *  */
const transformPublishData = async function (data: Record<string, any>, formData: Record<string, any>) {
  const { phone, bindTel, code, verifyToken, content, title, closeInfoId, showVerifyCode, promoteCities } = data
  const { trades, current_area } = formData

  const params = <any>{
    /** 详细信息长度不能长于1500个字 */
    detail: content.slice(0, 1500),
  }
  /**
   * 判断是否需要提交验证码
   * 1.用户输入手机号与绑定手机号不同
   * 2.用户手机号需要进行空号检测
   * 3.其他展示了验证码输入框的情况
   */
  if (showVerifyCode) {
    params.code = code
    params.verifyToken = verifyToken
  }
  /** 用户输入过手机号则使用输入手机号，否者使用绑定手机号 */
  params.mobile = phone || bindTel
  if (data.titleVisible && title) {
    params.title = title
  }
  let { selectArea = {}, selectCity = {} } = current_area
  // eslint-disable-next-line max-len
  const { newSelectArea, newSelectCity } = await tryPromise(formatAddressWithFormData(current_area), { newSelectArea: selectArea, newSelectCity: selectCity })
  const address = await wx.$.l.formatThoroughAddress(selectArea)
  selectArea = newSelectArea
  selectCity = newSelectCity
  /** 经纬度数据可能是对象也可能是字符串，这里要统一格式 */
  if (selectArea.location) {
    if (typeof selectArea.location !== 'object') {
      const [longitude, latitude] = selectArea.location.split(',')
      params.location = await wx.$.l.handleLocationVal({ longitude, latitude })
    } else {
      params.location = await wx.$.l.handleLocationVal(selectArea.location)
    }
  }

  if (Array.isArray(promoteCities) && promoteCities[0] && promoteCities[0].id) {
    params.promoteCityId = promoteCities[0].id
  }

  params.occV2 = <any>(await wx.$.l.getBtmClassifyChild(trades))
  params.areaId = selectArea.areaInfo ? selectArea.areaInfo.id : (selectArea.id || selectCity.id)
  params.address = address
  params.recruitType = data.recruitType || 1

  if (closeInfoId) {
    params.closeInfoId = closeInfoId
  }

  return params
}
