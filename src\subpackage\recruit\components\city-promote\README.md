# 城市推广选择组件

## 功能特性

- 集成 openAddress 地址选择功能
- 禁用香港、澳门、台湾地区选择
- 推广城市只可选择市一级
- 直辖市情况下选择"省"一级（北京市、上海市、天津市、重庆市）
- 支持自定义标题和占位符

## 使用方法

### 在页面 JSON 中引入组件

```json
{
  "usingComponents": {
    "city-promote": "/subpackage/recruit/components/city-promote/index"
  }
}
```

### 在页面模板中使用

```xml
<city-promote
  value="{{promoteCities}}"
  publishCity="{{currentAddress}}"
  title="推广城市"
  placeholder="请选择推广城市"
  bind:change="onCityChange"
/>
```

### 在页面逻辑中处理

```typescript
Page({
  data: {
    promoteCities: [],
    currentAddress: null, // 发布城市
  },

  onCityChange(event) {
    const { value } = event.detail;
    console.log('选择的推广城市:', value);
    this.setData({
      promoteCities: value,
    });
  },
});
```

## 属性说明

| 属性        | 类型   | 默认值           | 说明               |
| ----------- | ------ | ---------------- | ------------------ |
| value       | Array  | null             | 当前选中的城市数组 |
| publishCity | Object | null             | 发布城市数据       |
| title       | String | '推广城市'       | 组件标题           |
| placeholder | String | '请选择推广城市' | 占位符文本         |

## 事件说明

| 事件名 | 说明               | 参数                               |
| ------ | ------------------ | ---------------------------------- |
| change | 城市选择改变时触发 | event.detail.value: 选中的城市数据 |

## 城市数据格式

```typescript
{
  id: number | string,    // 城市ID
  name: string,          // 城市名称
  pid?: number | string, // 父级ID
  // 其他地址相关字段...
}
```

## 限制说明

1. **禁用地区**: 香港(ID: 33)、澳门(ID: 34)、台湾(ID: 35)
2. **选择级别**: 限制为市一级选择，单选模式
3. **强制市级**: 使用 `forceCityOnly: true` 确保直辖市不显示区级选项
4. **外地推广确认**: 当推广城市与发布城市不一致时，会弹出确认弹窗
5. **弹窗逻辑**:
   - 推广城市包含发布城市：直接保存，不弹窗
   - 推广城市不包含发布城市：弹出"确定推广到外地？"确认弹窗
   - 点击"取消"：关闭弹窗，停留在选择页面
   - 点击"确定"：保存选择，返回发布页面
