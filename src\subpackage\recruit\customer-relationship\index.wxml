<view class="customer-relationship-page">
  <!-- 导航栏 -->
  <custom-header title=" " border custom-style="background-color:#fff;" />
  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 页面标题 -->
    <view class="page-title">
      <text class="title-text" bind:tap="onAddCompany">选择公司</text>
    </view>
    <!-- 页面描述 -->
    <view class="page-desc">
      <text class="desc-text">请选择求职者实际工作的用工方，如果您的业务为连锁职位二手单业务，请选择此职位的直接用工方。</text>
    </view>
    <!-- 有数据时显示列表 -->
    <view wx:if="{{companyList.length > 0}}" class="company-list-container">
      <scroll-view scroll-y="{{true}}" class="company-scroll-view" bindscrolltolower="onLoadMore" lower-threshold="100">
        <view wx:for="{{companyList}}" wx:key="id" class="company-item" data-item="{{item}}" bindtap="onSelectCompany">
          <view class="company-info">
            <view class="company-content">
              <view class="company-text-wrapper">
                <text class="company-name">{{item.processedName.name}}</text><text wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this" class="tag {{tag === '代招' ? 'agent-tag' : 'outsource-tag'}}">{{tag}}</text>
              </view>
            </view>
          </view>
          <image wx:if="{{item.selected}}" class="select-icon" src="https://cdn.yupaowang.com/yupao_common/66c0bf6d.png" />
        </view>
        <!-- 加载更多 -->
        <view wx:if="{{loading}}" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
        <view wx:elif="{{!hasMore}}" class="no-more">
          <text class="no-more-text">- 没有更多内容了 -</text>
        </view>
      </scroll-view>
    </view>
    <!-- 无数据时显示空状态 -->
    <view wx:else class="empty-container">
      <empty-box imgType="comlist" title="暂未添加客户公司" img-class="custom-empty" text-class="no-data-text">
        <m-button type="primary" btnTxt="去新增" custom-class="add-company-btn" bindtap="onAddCompany" />
      </empty-box>
    </view>
    <!-- 底部按钮 -->
    <m-button-footer wx:if="{{companyList.length > 0}}" bind:tap="onAddCompany">
      新增客户公司
    </m-button-footer>
  </view>
</view>