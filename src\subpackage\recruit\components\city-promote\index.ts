import { RootState } from '@/store'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'

interface CityData {
  id: number | string;
  name: string;
  pid?: number | string;
  [key: string]: any;
}

interface AddressResult {
  value: CityData[];
}

Component(
  class CityPromote extends wx.$.Component<CityPromote> {
    setInitData() {
      return {
        displayText: '',
      }
    }

    externalClasses = ['container-class']

    properties = {
      value: {
        type: Array,
        value: null,
      },
      placeholder: {
        type: String,
        value: '请选择推广城市',
      },
      title: {
        type: String,
        value: '推广城市',
      },
      publishCity: {
        type: Object,
        value: null,
      },
      titleVisible: {
        type: Boolean,
        value: true,
      },
      sourceId: {
        type: String,
        value: '2',

      },
      disabled: {
        type: Boolean,
        value: false,
      },
    }

    observers = {
      value() {
        this.updateDisplayText()
      },
    }

    /** 更新显示文本 */
    updateDisplayText() {
      const { value } = this.data
      let displayText = ''

      // value 可能是 undefined 或空数组，需要安全处理
      if (value && Array.isArray(value) && value.length > 0) {
        // 单选模式，只显示第一个城市名称
        const firstCity = value[0]
        if (firstCity && firstCity.name) {
          displayText = firstCity.name
        }
      }

      this.setData({ displayText })
    }

    async onPick() {
      await wx.$.u.waitAsync(this, this.onPick, [], 1000)

      if (this.data.disabled) {
        wx.$.msg('推广城市不可修改')
        return
      }

      const { value, title } = this.data
      // 多选情况下，areas 应该包含所有已选择的城市ID
      const areas = value && Array.isArray(value) && value.length > 0
        ? value.map(item => item.id)
        : []

      // 禁用香港(33)、澳门(34)、台湾(35)
      const disabledIds = [33, 34, 35]

      wx.$.openAddress(
        {
          areas,
          level: 2, // 推广城市只可选择市一级，若是直辖市，则选择"省"一级
          hideNation: true,
          type: 'job',
          title,
          disabledIds, // 禁用港澳台
          selectType: 'district',
          hasAllType: 'none',
          maxNum: 1, // 单选
          headType: 'search',
          forceCityOnly: true, // 无论是直辖市还是地级市，只展示到市一级
          publishCity: this.data.publishCity,
          point: {
            source_id: this.data.sourceId,
            button_name: this.data.displayText || this.data.placeholder,
          },
        },
        (result: AddressResult) => {
          this.onResolve(result)
        },
      )
    }

    /** 城市选择之后的回调函数 */
    async onResolve(result: AddressResult) {
      if (result && result.value) {
        this.savePromoteCities(result.value)

        // 检查推广城市与发布城市是否一致
        // const diff = await this.checkCityDifference(result.value, publishCity)
        // if (
        //   publishCity
        //   && diff
        // ) {
        //   // 推广城市与发布城市不一致，弹出确认弹窗
        //   this.showPromotionConfirm(result.value)
        // } else {
        //   // 一致或没有发布城市，直接保存
        //   this.savePromoteCities(result.value)
        // }
      }
    }

    /** 检查推广城市与发布城市是否不一致 */
    async checkCityDifference(
      promoteCities: CityData[],
      publishCity: CityData,
    ): Promise<boolean> {
      if (!publishCity || !publishCity.id) return false
      const areas = await wx.$.l.getAreaById(publishCity.id)

      const provinceId = wx.$.u.getObjVal(areas, 'province.id')
      const cityId = wx.$.u.getObjVal(areas, 'city.id')
      if (!provinceId && !cityId) return false

      // 如果推广城市中包含发布城市，则认为一致
      return !promoteCities.some(
        city => (String(city.id) === String(provinceId) || String(city.id) === String(cityId)),
      )
    }

    /** 显示推广确认弹窗 */
    async showPromotionConfirm(selectedCities: CityData[]) {
      // 使用CMS弹窗系统
      const popup = await dealDialogRepByApi('PromotionConfirm')

      if (popup) {
        wx.$.showModal({
          ...popup,
          success: userAction => {
            const { routePath } = userAction
            if (routePath === 'confirm') {
              // 点击确定，保存选择的城市
              this.savePromoteCities(selectedCities)
            }
            // 点击取消，不做任何操作，用户可以重新选择
          },
        })
      } else {
        // 如果CMS弹窗配置不存在，使用原生弹窗作为降级方案
        this.savePromoteCities(selectedCities)
      }
    }

    /** 保存推广城市 */
    savePromoteCities(cities: CityData[]) {
      this.setData({
        value: cities,
      })

      // 触发change事件
      this.triggerEvent('change', { value: cities })
    }

    /** 获取直辖市名称 */
    getMunicipalityName(id: number | string): string {
      const municipalityMap: Record<string, string> = {
        2: '北京市',
        25: '上海市',
        27: '天津市',
        32: '重庆市',
      }
      return municipalityMap[String(id)] || ''
    }

    useStore(state: RootState) {
      return {}
    }
  },
)
