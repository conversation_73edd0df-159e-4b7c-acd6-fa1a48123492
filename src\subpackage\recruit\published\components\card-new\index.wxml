<view class="card">
  <view class="card-content" bind:tap="onClick">
    <view class="head">
      <view class="head-box">
        <view style="max-width: calc({{630 - (item.isCompete ? 50 : 0) -(item.hiringModeText ? 72 : 0) - (item.status !== 1 ? 100 : 0) - (item.isEnd.code == 1 && item.checkInfo.isCheck.code == 2 && (item.vieBanner.bannerType == 12 || item.vieBanner.bannerType == 14) ? 150 : 0)}}rpx)" class="title {{item.status == 4 ? 'title-grey' : ''}}">
          {{item.title || item.detail}}
        </view>
        <view wx:if="{{item.isCompete}}" class="compete {{item.status == 4 ? 'compete-grey' : ''}}">竞</view>
        <view wx:if="{{item.hiringModeText}}" class="hiring-icon">{{item.hiringModeText}}</view>
        <!-- <view class="hiring-icon">代招</view> -->
      </view>
      <card-status wx:if="{{item.isEnd.code == 1 && item.checkInfo.isCheck.code == 2 && item.vieBanner.showBanner && (item.vieBanner.bannerType == 12 || item.vieBanner.bannerType == 14)}}" status="{{-1198}}" />
      <card-status wx:else status="{{item.status}}" />
      <image class="head-box-img" mode="scaleToFill" src="https://cdn.yupaowang.com/yupao_mini/resume_pub_nod.png" />
    </view>
    <view class="text-content {{item.status == 4 ? 'text-content-grey' : ''}}">
      <view class="text-item {{item.status == 4 ? 'text-item-grey' : ''}}" wx:for="{{item.showTags}}" wx:for-item="tag" wx:key="index">
        {{tag.name}}
      </view>
    </view>
  </view>
  <!-- 正在招 -->
  <block wx:if="{{item.status == 1}}">
    <view class="digital" bind:tap="onClick">
      <view class="digital-box" wx:if="{{item.status == 1 && topStatus == 1 && item.exposureNumStr}}">
        <text class="digital-text">{{item.exposureNumStr}}</text>
        曝光
      </view>
      <view class="digital-box">
        <text class="digital-text">{{item.browsedNum}}</text>
        查看
      </view>
      <view class="digital-box">
        <text class="digital-text">{{item.contactNum}}</text>
        联系
      </view>
    </view>
    <view class="actions">
      <view class="btn" style="width: 156rpx;" bind:tap="onClosed">关闭职位</view>
      <view class="btn" bind:tap="onSetting">修改</view>
      <view class="btn {{item.hasFrequencyLimit ? 'btn-refreshed' : ''}}" bind:tap="onRefresh">
        {{item.hasFrequencyLimit ? '已刷新' : '刷新'}}
      </view>
      <view wx:if="{{topStatus != 1}}" class="btn btn-primary" catch:tap="onJumpTop" data-text="我要置顶">我要置顶</view>
      <view wx:if="{{topStatus == 1 && urgentStatus != 1}}" class="btn btn-primary" catch:tap="onJumpUrgent" data-text="加急招">
        加急招
      </view>
    </view>

    <view class="footer-wrapper">
      <!-- 补充实名认证 -->
      <view wx:if="{{item.authInfo.guideRealNameAuth}}" class="footer" catch:tap="onRealName" data-buryingPoint-id="to_real_name">
        <view class="footer-left">
          <text>为了确保职位真实有效，请尽快完成实名认证</text>
        </view>
        <view class="footer-right">
          <view>去实名</view>
          <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
        </view>
      </view>


      <!-- 竞招职位体验卡引导升级 -->
      <block wx:if="{{item.isEnd.code == 1 && item.checkInfo.isCheck.code == 2 && item.vieBanner.showBanner && (item.vieBanner.bannerType == 12 || item.vieBanner.bannerType == 14)}}">
        <view class="footer" catch:tap="onUpgrade">
            <view class="footer-left">
              {{item.vieBanner.bannerContent}}
            </view>
            <view class="footer-right">
              <view>升级职位</view>
              <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
            </view>
        </view>
      </block>
      <!-- 引导置顶 -->
      <block wx:elif="{{stickyCountdown || topStatus == 1 || urgentStatus == 1}}">
        <view wx:if="{{stickyCountdown}}" class="footer" catch:tap="onJumpTop" data-buryingPoint-id="position_management_click">
          <view class="footer-left">
            <view style="width: 120rpx;">{{stickyCountdown}}</view>
            <text>置顶职位获得更多曝光</text>
          </view>
          <view class="footer-right">
            <view>去置顶</view>
            <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
          </view>
        </view>
        <!-- 置顶生效 -->
        <view wx:if="{{topStatus == 1}}" class="footer" bind:tap="onTopNav">
          <view class="footer-left">
            <image class="audit-msg-img" src="https://cdn.yupaowang.com/yupao_mini/published-a.png" mode="scaleToFill" />
            <text>置顶生效中</text>
          </view>
          <view class="footer-right">
            <view>查看效果</view>
            <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
          </view>
        </view>
        <!-- 加急招 已加急 -->
        <view wx:if="{{urgentStatus == 1}}" class="footer" catch:tap="onUrgentNav">
          <view class="footer-left">
            <image class="audit-msg-img" src="https://cdn.yupaowang.com/yupao_mini/published-b.png" mode="scaleToFill" />
            <text>加急招生效中</text>
          </view>
          <view class="footer-right">
            <view>查看效果</view>
            <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
          </view>
        </view>
      </block>
      <!-- 竞招延长 -->
      <view class="footer" wx:elif="{{item.vieBanner.showBanner}}" data-label="{{ item.vieBanner.canRenew?  '立即延长': ''}}" data-prompt-text="{{(item.vieBanner.bannerType == 3 && vieCountdown) ? ('竞招职位今日即将到期，剩余时间' + vieCountdown) : item.vieBanner.bannerContent  }}" catch:tap="onVie">
        <view class="footer-left">
          <text wx:if="{{item.vieBanner.bannerType == 3 && vieCountdown}}">竞招职位今日即将到期，剩余时间{{vieCountdown}}</text>
          <text wx:else>{{item.vieBanner.bannerContent}}</text>
        </view>
        <view class="footer-right" wx:if="{{item.vieBanner.canRenew}}">
          <view>立即延长</view>
          <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
        </view>
      </view>
      <!-- 完善引导 -->
      <view wx:elif="{{item.occWaitCompletes.length > 0 && index == 0}}" class="footer" catch:tap="onJumpPer" data-prompt-text="{{item.occWaitCompletes[0].itemNames}}">
        <view class="footer-left">
          有多个牛人想知道
          <block wx:for="{{item.occWaitCompletes[0].itemNames}}" wx:key="index" wx:for-item="tag">
            {{index > 0 ? '、' : ''}}{{tag}}
          </block>
        </view>
        <view class="footer-right">
          <view>去完善</view>
          <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
        </view>
      </view>
      <!-- 加急招引导 -->
      <view wx:elif="{{topStatus != 1 && index == 0}}" class="footer" catch:tap="onJumpUrgent" data-buryingPoint-id="position_management_click">
        <view class="footer-left">
          <text>曝光免费，查看才扣费！</text>
        </view>
        <view class="footer-right">
          <view>加急招</view>
          <icon-font type="yp-resume_pub_nod" color="#0092FF" size="24rpx" />
        </view>
      </view>
    </view>
  </block>
  <!-- 审核中 -->
  <block wx:if="{{item.status == 2}}">
    <view class="actions" wx:if="{{!(topStatus == 0 || topStatus == 1 || topStatus == 2)}}">
      <view class="btn btn-primary" bind:tap="onJumpTop" data-text="预约置顶">预约置顶</view>
    </view>
    <view class="audit-footer" wx:else>
      <view class="audit-msg">
        <image class="audit-msg-img" src="https://cdn.yupaowang.com/yupao_mini/published-a.png" mode="scaleToFill" />
        <text>已预约置顶</text>
      </view>
    </view>
  </block>
  <!-- 审核失败 -->
  <block wx:if="{{item.status == 3}}">
    <view class="actions">
      <view class="btn" bind:tap="onSetting">修改</view>
    </view>
    <view class="fail-footer">
      <view class="fail-msg">
        审核失败：{{checkFailMsg}}
        <text class="consultation" wx:if="{{jobPointSwitch && !hasTel}}" catch:tap="onContact">
          立即咨询
          <icon-font type="yp-resume_pub_nod" color="#e8362e" size="24rpx" />
        </text>
      </view>
    </view>
  </block>
  <!-- 已招满｜下线数据 -->
  <block wx:if="{{item.status == 4 || item.status== 5 && item.complianceOfflineReason.code}}">
    <view class="actions">
      <view class="btn" bind:tap="onSetting">修改</view>
      <view class="btn" bind:tap="onReSend">{{item.complianceOfflineReason.code ? "开始招聘" :"重新发布"}}</view>
    </view>
  </block>
  <!-- 待开放｜下线数据 -->
  <block wx:if="{{item.status == 5}}">
    <view class="actions" wx:if ="{{!!item.complianceOfflineReason.code}}">
      <view class="btn" bind:tap="onDeleteDraft">删除</view>
      <view class="btn" bind:tap="onEditDraft">修改</view>
      <view class="btn" bind:tap="onPublishDraft">开始招聘</view>
    </view>
    <view class="wait-footer" data-action="{{actionType}}" bindtap="handleDraftAction">
      <view class="wait-msg">{{item.draftReason || item.complianceOfflineReason.desc}}</view>
      <view class="wait-action" wx:if="{{actionType}}">
        <block wx:if="{{actionType == 1}}">补充资料</block>
        <block wx:if="{{actionType == 2}}">完善资质</block>
        <block wx:if="{{actionType == 3}}">咨询客服</block>
        <icon-font type="yp-resume_pub_nod" size="24rpx" color="rgb(255, 137, 4)" />
      </view>
    </view>
  </block>
</view>