/*
 * @Date: 2022-02-09 09:39:59
 * @Description: 一些请求或纯函数
 */

import { actions, dispatch } from '@/store/index'
import { LiteralText } from '../fast_issue/index/type'
import { nwSaveFilterStoreByIds } from '@/utils/helper/common/index'
import { javafetch } from '@/utils/request/index'

/** 通用弹框处理
 * @param msg: 没有匹配到弹框的toast提示处理
 */
async function handlerDialogCall(popup, res) {
  const { ignore = [], dialogData } = res
  const { item } = this.data
  const dialogIdentify = popup.dialog_identify || ''
  const areaId = item.cityId || item.provinceId

  if (ignore.includes(dialogIdentify)) return Promise.reject('静默失败')

  return new Promise(async (resolve) => {
    if (dialogIdentify == 'wjzqytc') {
      const tabType = wx.$.u.getObjVal(dialogData, 'template.tabType')
      const occupationV2Ids = item.occV2.map(i => {
        return i.occIds.join(',')
      }).join(',')
      const areaObj: any = await wx.$.l.getAreaById(item.cityId)
      const cities = areaObj.city?.id || areaObj.province?.id
      const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}&occupationV2Ids=${occupationV2Ids}&cities=${cities}`)
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      resolve({ code: -1 })
      return
    }
    // 实名认证次数使用完后，需强制认证-6191227990
    if (dialogIdentify === 'RealNameHaveNoTimes') {
      wx.$.r.push({ path: '/subpackage/member/realname/index' })
      resolve({ code: -1 })
      return
    }

    if (dialogIdentify === 'permit_due_popup') {
      const url = encodeURIComponent('/enterprise-verify?tabType=0')
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      resolve({ code: -1 })
      return
    }

    wx.$.showModal({
      ...popup,
      success: async (result) => {
        const { routePath } = result || { btnIndex: -1, routePath: '' }
        switch (`${routePath}`) {
          case 'modify2publish': // 重新发布标识
            republishByJobId(item.jobId, routePath)
            resolve({ code: -1 })
            break
          case 'modify': // 重新发布招工的付费弹框
          case 'refresh': // 刷新按钮标识
            break
          case 'toEarnPoints': { // 去赚积分按钮标识
            const query = {
              isFromPage: 'CLRefreshRecruitCard',
              jobId: item.jobId,
            }
            wx.$.toGetIntegral(query)
            resolve({ code: -1 })
            break
          }
          case 'viewDetails': // 账号异常的跳转处理
            wx.$.r.push({ path: '/subpackage/systips/index' })
            resolve({ code: -1 })
            break
          case 'toResume': // 刷新招工成功之后点击【查看工友信息】按钮
          case 'workerList': // 刷新招工成功之后点击【查看工友信息】按钮
            // 处理页面传入页面的路由参数
            nwSaveFilterStoreByIds(areaId, await wx.$.l.transformClsIdHidsByOccV2(item.occV2 || []), 'resume')
            wx.$.r.reLaunch({ path: '/pages/resume/index' })
            resolve({ code: -1 })
            break
          case 'top': { // 点击【去置顶】按钮
            wx.$.l.existJobTopSet(item.jobId, {
              showTab: 1,
            })
            resolve({ code: -1 })
            break
          }
          default:
            // 走到这里代表点击了取消按钮或者没有匹配到对应的按钮
            resolve({ code: -1 })
        }
        resolve(res)
      },
    })
  })
}

/** 检测刷新招工 */
export async function fetchRefreshCheck(params: YModels['POST/job/v2/manage/job/refresh/check']['Req']) {
  const res = await wx.$.javafetch['POST/job/v2/manage/job/refresh/check'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })
  wx.hideLoading()
  if (res.dialogType) {
    return res
  }

  if (!res.popup) {
    // 未匹配的任何弹框的处理
    if (res.code != 0) {
      wx.$.msg(res.message || '刷新失败')
    }
    return res
  }

  // 通用弹框
  return handlerDialogCall.call(this, res.popup, res)
}

/** 刷新招工 */
export async function fetchRefresh(params: YModels['POST/job/v2/manage/job/refresh/do']['Req']) {
  wx.$.loading('刷新中...')
  const res = await wx.$.javafetch['POST/job/v2/manage/job/refresh/do'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })

  wx.hideLoading()
  if (!res.dialogData) {
    wx.$.msg(res.message || res.code == 0 ? '刷新成功' : '刷新失败')
    return res
  }
  res.popup && handlerDialogCall.call(this, res.popup, res)
  return res
}

/** 重新发布 - 费用检查 */
export async function fetchRepublishCheck(params: YModels['POST/job/v2/manage/job/republish/check']['Req'], extra: Record<string, any> = {}) {
  const res = await wx.$.javafetch['POST/job/v3/manage/job/republish/check'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })
  wx.hideLoading({ noConflict: true })
  if (res.dialogType) {
    return res
  }

  if (!res.popup) {
    // 无任何弹框的处理
    if (res.code != 0) {
      wx.$.msg(res.message || '重新发布失败')
    }
    return res
  }

  // 通用弹框
  return handlerDialogCall.call(this, res.popup, { ...res, ...extra })
}

/** 重新发布 */
export async function fetchRepublish(params: YModels['POST/job/v2/manage/job/republish/do']['Req'], extra: Record<string, any> = {}) {
  const res = await wx.$.javafetch['POST/job/v3/manage/job/republish/do'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })
  if (res.dialogType) {
    return res
  }
  if (!res.popup) {
    // 无任何弹框的处理
    if (res.code != 0) {
      wx.$.msg(res.message || '重新发布失败')
    }
    return res
  }

  // 通用弹框
  handlerDialogCall.call(this, res.popup, { ...res, ...extra })
  return res
}

// 修改状态转新发布招工，单独查一次招工信息来获取城市地区
const republishByJobId = async (jobId: number, routePath) => {
  const { data, code } = await javafetch['POST/job/v3/manage/job/info']({ jobId })

  if (!code) {
    const areaId = data.urbanAreas.countyId || data.urbanAreas.cityId
    const areaData = await wx.$.l.getAreaById(areaId)
    wx.$.r.push({
      path: '/subpackage/recruit/fast_issue/index/index',
      query: {
        flag: routePath,
        occV2: data.occV2
          .map((it) => it.occIds)
          .flat()
          .join(','),
        content: data.detail,
        title: data.title,
        areaId: areaData.current.id || areaData.city.id,
        adCode: areaData.current.ad_code,
        location: `${data.location?.longitude || ''},${data.location?.latitude || ''}`,
        address: data.addressTitle,
      },
    })
  }
}

/** 埋点封装 */
export const point = (target, click_button) => {
  const { tabs, activeTab } = target.data
  const { buriedPointName } = tabs.find((item) => item.id === activeTab)
  // 埋点
  wx.$.collectEvent.event('rnPageClickButton', {
    page_name: buriedPointName,
    click_button,
  })
}

type IListItem = YModels['POST/job/v2/manage/job/list']['Res']['data']['data'][0]

/** @description 修改招工信息状态 请求参数 */
export interface RecruitStatusParams {
  /** @name 招工id */
  id: number
  /** @name 结束状态:1-停止招工,2-重新招工 */
  end_status: number
  /** @name 是否消耗积分进行修改 */
  is_expend_integral: number
  /** @name 地区id */
  area_id: number | string
  /** @name 用户是否置顶该条信息 */
  top: number | string
  /** @name 是否来源于--物流-付费修改成交状态-弹窗 */
  needPayConfirm?: number
  /** @name 是否必须传此参数为1 */
  isPayConfirmPop?: number
}

/** 处理招工状态: status
 * 0. 未知状态
 * 2. 审核中 isCheck = 1
 * 3. 未通过 isCheck = 0
 * 1. 正在招 isCheck = 2 && isEnd = 1
 * 4. 已招满 isCheck = 2 && isEnd = 2
 */
function headerStatus(item: IListItem): number {
  let status = 0
  const { checkInfo: { isCheck }, isEnd } = item || {}
  if (!isCheck || !isEnd) {
    return status
  }
  if (isCheck.code == 1) {
    status = 2
  } else if (isCheck.code == 0) {
    status = 3
  } else if (isCheck.code == 2 && isEnd.code == 1) {
    status = 1
  } else if (isCheck.code == 2 && isEnd.code == 2) {
    status = 4
  }
  return status
}

/** 列表数据的清洗方法,该方法处理单条招工记录的数据清洗 */
function formatDataItem(item: IListItem) {
  const status = headerStatus(item)
  return {
    ...item,
    status,
    hiringModeText: hiringModeMapping[item?.hiringMode],
    showTags: item?.showTags?.filter((item) => item.name), // 过滤有名字的
  }
}

/** 获取单条招工数据信息 */
export function getItemData(params: YModels['POST/job/v2/manage/job/list/item']['Req']) {
  return wx.$.javafetch['POST/job/v2/manage/job/list/item'](params).then(res => {
    if (res.code == 0) {
      // eslint-disable-next-line no-param-reassign
      res.data = formatDataItem(res.data)
    }
    return res
  })
}

/**
 * 自招 - directHiring，代招 - agencyHiring，猎头 - Headhunting（不展示）
 */
const hiringModeMapping = {
  directHiring: '自招',
  agencyHiring: '代招',
  // Headhunting: '猎头',
} as const

/** 获取列表数据及统一数据处理 */
export async function getListData(params) {
  if (params.type === 'stopRecruiting') {
    const res = await wx.$.javafetch['POST/job/v3/manage/job/listPro']({ ...params })
    const dataSource = wx.$.u.getObjVal(res, 'data.data', [])
    const paginator = wx.$.u.getObjVal(res, 'data.paginator', [])
    let hiringMode
    try {
      hiringMode = JSON.parse(dataSource.jobInfo).hiringMode
    } catch (error) {
      hiringMode = ''
    }
    return {
      data: dataSource.map((item) => {
        return {
          ...item.jobInfo,
          uuid: wx.$.u.guid(),
          status: 5,
          hiringMode,
          hiringModeText: hiringModeMapping[hiringMode] || '',
          showTags: item.showTags?.filter((item) => item.name), // 过滤有名字的
        }
      }),
      paginator,
    }
  }
  // 待开放
  if (params.type === 'waiting') {
    const res = await wx.$.javafetch['POST/job/v3/manage/job/listPro']({ ...params, type: 'draft' })
    const dataSource = wx.$.u.getObjVal(res, 'data.data', []).map(item => ({ ...item, ...(item.draftInfo || {}) }))
    const paginator = wx.$.u.getObjVal(res, 'data.paginator', [])
    let hiringMode
    try {
      hiringMode = JSON.parse(dataSource.ext).hiringMode
    } catch (error) {
      hiringMode = ''
    }

    return {
      data: dataSource.map((item) => {
        return {
          ...(item.draftInfo || {}),
          ...(item.jobInfo || {}),
          uuid: wx.$.u.guid(),
          status: 5,
          hiringMode,
          hiringModeText: hiringModeMapping[hiringMode] || '',
          showTags: item.showTags?.filter((item) => item.name), // 过滤有名字的
        }
      }),
      paginator,
    }
  }
  // 全部
  if (params.type === 'allV2') {
    const res = await wx.$.javafetch['POST/job/v3/manage/job/listPro']({ ...params, type: 'all' })
    const dataSource = wx.$.u.getObjVal(res, 'data.data', [])
    const paginator = wx.$.u.getObjVal(res, 'data.paginator', {})
    return {
      data: dataSource.map((item) => {
        if (item.infoType == 2) {
          let hiringMode
          let draftReasonCode
          try {
            const extInfo = JSON.parse(dataSource.draftInfo.ext)
            hiringMode = extInfo.hiringMode
            draftReasonCode = extInfo.reason_code
          } catch (error) {
            hiringMode = ''
          }
          return {
            ...item.draftInfo,
            hiringMode,
            hiringModeText: hiringModeMapping[hiringMode] || '',
            uuid: wx.$.u.guid(),
            draftReasonCode,
            status: 5,
            showTags: item.draftInfo?.showTags?.filter((item) => item.name), // 过滤有名字的
          }
        }
        let hiringMode
        let draftReasonCode
        try {
          const extInfo = JSON.parse(dataSource.draftInfo.ext)
          hiringMode = extInfo.hiringMode
          draftReasonCode = extInfo.reason_code
        } catch (error) {
          hiringMode = ''
        }
        const hiringModeText = hiringModeMapping[hiringMode] || ''
        return {
          ...item.jobInfo,
          hiringMode,
          hiringModeText,
          draftReasonCode,
          status: item.jobInfo?.complianceOfflineReason ? 5 : headerStatus(item.jobInfo),
          showTags: item.jobInfo?.showTags?.filter((item) => item.name), // 过滤有名字的
        }
      }),
      paginator,
    }
  }
  const res = await wx.$.javafetch['POST/job/v2/manage/job/list'](params)
  res.data.data = res.data.data.map((item) => formatDataItem(item))
  return res.data
}

/**
 * 获取dom元素的高度
 * @param dom 某个dom,采用.content或#content的形式
 * @param callback 回调函数，带一个高度值
 */
export async function getDomHeight(dom: string, callback: (height: Number) => void) {
  const query = wx.createSelectorQuery()
  query
    .select(dom)
    .boundingClientRect((res) => {
      callback(res.height)
    })
    .exec()
}

/** 跳转至发布招工页面或者工厂发布招工页 */
export function jumpFastIssue() {
  const path = '/subpackage/recruit/fast_issue/index/index'
  wx.$.r.push({ path })
}

/** 跳转至谁联系过我页面 */
export function jumpWhoContactedMe() {
  // const path = '/subpackage/recruit/whoContactedMe/index'
  const path = '/subpackage/member/myContactHistory/index'
  wx.$.r.push({ path, query: { tab_index: '1' } })
  dispatch(actions.messageActions.setState({ contact_unread: 0 }))
}

/** 发布草稿 */
export function publishDraft(draftId: LiteralText, extra: Record<string, any> = {}) {
  return wx.$.javafetch['POST/job/v3/manage/draft/job']({ draftId, ...extra, completeV3SourceFlag: true })
}

interface ICheckComplianceP extends Record<string, any> {
  classify_id: string
  area_id: string
  ignore?: string[]
}

/** 处理catch错误中的弹窗，将特定弹窗冒泡到页面上 */
const bizDialogThrowToPage = function _(dialogData: any) {
  const dialogIdentify = (dialogData || {}).dialogIdentify || ''
  if (['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(dialogIdentify)) {
    throw dialogData
  }
}

/**
 * 发布草稿调用
 * @param {LiteralText} draftId 职位信息ID
 * @param {ICheckComplianceP} params 城市工种数据
 * @param {Function} callback 成功回调，用于刷新页面等后续操作
 */
export async function prevCheckDraft(draftId: LiteralText, params: ICheckComplianceP, callback: (vk: any) => void) {
  const { extra = {}, ignore = [], ...newParams } = params
  wx.showLoading({ title: '发布中', mask: true })
  const response = await wx.$.javafetch['POST/job/v3/manage/draft/preCheck']({ draftId, ...extra, completeV3SourceFlag: true })
  /** response::data为空时，继续走后续流程，否则先判断是否有弹窗 */
  if (!response.data && response.code == 0) {
    wx.showLoading({ title: '发布中', mask: true })
    return publishDraft(draftId)
      .then(handleDraftResponse(newParams))
      .then((response) => {
        wx.$.msg('发布成功')
        callback(response)
      }).catch((error) => {
        console.error(error)
        bizDialogThrowToPage(error)
      })
  }
  return handleDraftResponse(newParams)(response)
    .then(() => {
      wx.showLoading({ title: '发布中', mask: true })
      return publishDraft(draftId, extra)
        .then(handleDraftResponse(newParams))
        .then((response) => {
          wx.$.msg('发布成功')
          callback(response)
        }).catch((error) => {
          console.error(error)
          bizDialogThrowToPage(error)
        })
    })
    .catch((error) => {
      console.error(error)
      bizDialogThrowToPage(error)
    })
}

/**
 * @desc 处理发布草稿返回值
 * @param params 跳转到找活页面的路由参数
 * @returns
 */

const handleDraftResponse = (params) => async (response: any) => {
  const { dialogData, popup, code, message } = response || {}
  const { ignore = [] } = params
  const { dialogIdentify } = dialogData || {}
  wx.hideLoading({ noConflict: true })
  if (code == 30020203) {
    setTimeout(() => {
      wx.$.msg(response.message)
    }, 200)

    const pages = getCurrentPages()
    const curPage = pages[pages.length - 1]
    if (curPage.route == 'subpackage/recruit/published/index') {
      curPage.jumpToTotal?.()
    } else {
      wx.$.nav.reLaunch('/subpackage/recruit/published/index')
    }

    return Promise.reject()
  }
  /** 未匹配到弹窗，继续发布 */
  if (!popup || !dialogData || ignore.includes(dialogIdentify)) return Promise.resolve(response)
  return new Promise(async (resolve, reject) => {
    if (['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(dialogIdentify)) {
      const e = { ...dialogData }
      /** 获取JobId */
      e.jobId = wx.$.u.getObjVal(response, 'data.data.id', '')
      reject(e)
      return
    }

    if (dialogIdentify == 'wjzqytc') {
      const tabType = wx.$.u.getObjVal(dialogData, 'template.tabType')
      const occupationV2Ids = params.classify_id.split(',').map(i => i.split('@')[0]).join(',')
      const areaObj: any = await wx.$.l.getAreaById(params.area_id)
      const cities = areaObj.city?.id || areaObj.province?.id
      const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}&occupationV2Ids=${occupationV2Ids}&cities=${cities}`)
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      reject()
      return
    }

    //  若用户未完成实名认证-6191227990
    if (dialogIdentify === 'RealNameHaveNoTimes' || dialogIdentify === 'RealNameHaveTimes') {
      wx.$.r.push({ path: '/subpackage/member/realname/index' })
      reject()
      return
    }

    if (dialogIdentify === 'permit_due_popup') {
      const url = encodeURIComponent('/enterprise-verify?tabType=0')
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      reject()
      return
    }

    wx.$.showModal({
      ...popup,
      success: async ({ routePath }) => {
        switch (routePath) {
          /** 确认继续发布 */
          case 'confirm':
          case 'continueReleaseInfo':
            resolve(response)
            break
          /** 取消发布 */
          case 'cancel': {
            wx.$.r.reLaunch({ path: '/pages/resume/index', params })
            reject()
            break
          }
          /** 企业实名跳转 */
          case 'toCompanyAuth': {
            reject()
            wx.$.r.reLaunch({ path: '/subpackage/member/firmAuth/index', query: { origin: 'publishRecruitPop' } })
            break
          }
          /** 获取积分跳转 */
          case 'toEarnPoints': {
            reject()
            wx.$.toGetIntegral({
              isFromPage: 'CLPublishRecruit',
            })
            break
          }
          case 'toResume': // 跳转到牛人列表
            wx.$.r.push({ path: '/pages/resume/index' })
            break
          // case 'toPurchase': {
          //   const tabType = wx.$.u.getObjVal(dialogData, 'template.tabType')
          //   const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}`)
          //   wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
          //   break
          // }
          /** 未知阻断 */
          default:
            reject()
            break
        }
      },
    })
  })
}
