import { MapStateToData, connectPage } from '@/store/index'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { debounce } from '@/utils/tools/common/index'

let reqParams = {
  currentPage: 1,
  pageSize: 15,
}

const mapStateToData: MapStateToData = state => {
  return {
    userState: state.storage.userState,
  }
}

Page(
  connectPage(mapStateToData)({
    data: {
      isManage: false, // 状态 是否处于管理状态
      list: [],
      state: 'more',
      loading: true,
      totalRecord: 0,
      maxTotal: 0,
    },
    onManageHandle() {
      this.setData({ isManage: !this.data.isManage })
    },
    toAddHandle() {
      wx.$.router.push({
        path: '/subpackage/map/add-edit/index',
        events: {
          refreshHandle: () => {
            this.getList(true)
          },
        },
      })
    },
    onLoad() {
      this.getCompanyInfo()
      this.getList(true)
    },
    onShow() {
      this.setData({ isManage: false })
    },
    async getCompanyInfo() {
      const { userId } = this.data.userState
      const { data } = await wx.$.javafetch[
        'POST/enterprise/v1/enterpriseHomepage/getEnterpriseCard'
      ]({
        userId,
        tenantKey: 'YPZP',
      })
      this.setData({ companyInfo: data })
    },
    onCardOperateHandle: debounce(async function (e) {
      if (this.data.isManage) {
        this.deleteFun(e.currentTarget.dataset.id)
      } else {
        wx.$.router.push({
          path: '/subpackage/map/add-edit/index',
          query: { id: e.currentTarget.dataset.id },
          events: {
            deleteSuccess: () => {
              const newList = this.data.list.filter(
                item => item.id != e.currentTarget.dataset.id,
              )
              this.setData({
                list: newList,
                totalRecord: this.data.totalRecord - 1,
              })
            },
            refreshHandle: () => {
              this.getList(true)
            },
          },
        })
      }
    }, 500),
    async deleteFun(id, key = 'address_confirm') {
      try {
        const popup = await dealDialogRepByApi(key)
        if (popup) {
          const res = await wx.$.showModal({
            ...popup,
          })
          if (res.itemClass != 'cancel') {
            this.deleteReq(id)
          }
        } else {
          this.deleteReq(id)
        }
      } catch (e) {
        console.log(e)
      }
    },
    async deleteReq(id) {
      try {
        const { list, totalRecord } = this.data
        wx.showLoading({ title: '删除中' })
        await wx.$.javafetch['POST/account/v1/userAddress/delete']({ id })
        const newList = list.filter(item => item.id != id)
        this.setData({ list: newList, totalRecord: totalRecord - 1 })
        wx.hideLoading()
        await wx.$.msg('删除成功')
      } catch (e) {
        wx.hideLoading()
      }
    },
    onLoadHandle() {
      this.getList()
    },
    async getList(refresh?: boolean) {
      try {
        if (refresh) {
          reqParams = {
            currentPage: 1,
            pageSize: 15,
          }
          this.noMore = false
          this.setData({ list: [], loading: true })
          wx.showLoading({ title: '加载中' })
        } else {
          const oldL = this.data.list
          reqParams.createdAt = oldL[oldL.length - 1].createdAt
        }
        if (this.noMore) {
          return
        }
        this.loading = true
        if (!refresh) {
          this.setData({ state: 'loading' })
        }
        const { data } = await wx.$.javafetch[
          'POST/account/v1/userAddress/pageQuery'
        ]({ ...reqParams })
        const { list, paginator, maxTotal } = data
        const showList = refresh ? list : [...this.data.list, ...list]
        this.setData({
          list: showList,
          loading: false,
          maxTotal,
          totalRecord: paginator.totalRecord,
        })
        if (showList.length >= paginator.totalRecord) {
          this.noMore = true
          this.setData({ state: 'finish' })
        } else {
          this.noMore = false
          this.setData({ state: 'more' })
          reqParams.currentPage++
        }
        this.loading = false
        wx.hideLoading()
      } catch (e) {
        console.log(e)
        wx.hideLoading()
      }
    },
  }),
)
