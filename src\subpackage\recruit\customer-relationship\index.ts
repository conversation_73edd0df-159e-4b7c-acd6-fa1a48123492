/*
 * @Description: 发布招工-选择公司页面
 */
import { tools } from '@/utils/index'
import { MapStateToData, connectPage } from '@/store/index'

/** 初始化状态管理数据 */
const mapStateToData: MapStateToData = state => {
  return {}
}

Page(
  connectPage(mapStateToData)({
    data: {
      isIOS: tools.validator.isIos(),
      companyList: [], // 公司列表
      loading: false, // 加载状态
      hasMore: true, // 是否还有更多数据
      page: 1, // 当前页码
      pageSize: 20, // 每页数量
      selectedCompany: null, // 选中的公司
    },

    /** 生命周期函数--监听页面加载 */
    onLoad(options) {
      if (options?.hiringClient?.hiringClientId) {
        // 发布页面传值回显
        this.setData({
          selectedCompany: {
            id: options.hiringClient.hiringClientId,
            name: options.hiringClient.hiringClientName,
            cooperativeModes: options.hiringClient.cooperativeModes,
          },
        })
      }
    },

    /** 生命周期函数--监听页面显示 */
    onShow() {
      // 页面显示时可以刷新数据
      this.loadCompanyList()
    },

    /** 页面相关事件处理函数--监听用户下拉动作 */
    onPullDownRefresh() {
      this.refreshCompanyList()
    },

    /** 页面上拉触底事件的处理函数 */
    onReachBottom() {
      this.onLoadMore()
    },

    /** 加载公司列表 */
    async loadCompanyList(isRefresh = false) {
      if (this.data.loading) return

      const page = isRefresh ? 1 : this.data.page

      this.setData({ loading: true })

      try {
        // 使用项目封装的javafetch方法调用真实API
        const res = await wx.$.javafetch['POST/job/v3/manage/job/jobHiringClients']({
          page,
          pageSize: this.data.pageSize,
        })

        if (res.code === 0) {
          // 后端返回结构为 { data: { data: [...], paginator: {...} } }
          const responseData = res.data || {}
          const listFromApi = responseData.hiringClients || responseData.data || []
          const paginator = responseData.paginator || {}

          // 转换API数据格式并处理标签显示
          const formattedList = listFromApi.map((client: any) => {
            const baseItem = {
              id: client.hiringClientId,
              name: client.hiringClientName,
              cooperativeModes: client.cooperativeModes || [],
              selected: false,
            }

            // 处理标签显示
            const tags = this.getTagsFromCooperativeModes(baseItem.cooperativeModes)

            return {
              ...baseItem,
              // 兼容原有的标签字段
              isAgent: tags.includes('代招'),
              isOutsource: tags.includes('派遣&外包'),
              // 处理名称显示
              processedName: this.processCompanyNameWithTags(baseItem),
              // 标签数组
              tags,
            }
          })

          const newList = isRefresh ? formattedList : [...this.data.companyList, ...formattedList]

          // 判断是否还有更多数据，优先使用分页信息
          let hasMore = false
          if (paginator && typeof paginator.currentPage === 'number' && typeof paginator.totalPage === 'number') {
            hasMore = paginator.currentPage < paginator.totalPage
          } else {
            hasMore = listFromApi.length === this.data.pageSize
          }

          this.setData({
            companyList: newList,
            hasMore,
            page: page + 1,
            loading: false,
          })

          if (isRefresh) {
            wx.stopPullDownRefresh()
          }
        } else {
          throw new Error(res.message || '请求失败')
        }
      } catch (error) {
        console.error('加载公司列表失败:', error)
        this.setData({ loading: false })
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none',
        })
      }
    },

    /** 刷新公司列表 */
    refreshCompanyList() {
      this.setData({ page: 1, hasMore: true })
      this.loadCompanyList(true)
    },

    /** 加载更多 */
    onLoadMore() {
      if (this.data.hasMore && !this.data.loading) {
        this.loadCompanyList()
      }
    },

    /** 选择公司 */
    onSelectCompany(e: any) {
      const { item } = e.currentTarget.dataset

      // 更新选中状态
      const updatedList = this.data.companyList.map((company: any) => ({
        ...company,
        selected: company.id === item.id,
      }))

      this.setData({
        companyList: updatedList,
        selectedCompany: item,
      })

      // 返回上一页并传递选中的公司信息
      setTimeout(async () => {
        await wx.$.nav.event({
          HiringClient: {
            hiringClientId: item.id,
            hiringClientName: item.hiringClientName,
            hiringMode: item.jobHiringMode,
          },
        })
        wx.$.nav.back()
      }, 200)
    },

    /** 新增公司 */
    onAddCompany() {
      console.log('新增公司')
      // 跳转到新增公司关系页面
      const url = '/enterprise-verify?tabType=1'
      wx.$.r.push({
        path: `/subpackage/web-view/index?isLogin=true&url=${encodeURIComponent(url)}`,
      })
    },

    /** 处理企业名称和标签的显示 */
    processCompanyNameWithTags(item: any) {
      const { name, cooperativeModes = [] } = item

      // 根据cooperativeModes构建标签文本
      let tagsText = ''
      cooperativeModes.forEach((mode: any) => {
        if (mode.cooperativeType === 1) {
          // 派遣&外包
          tagsText += '派遣&外包'
        } else if (mode.cooperativeType === 2) {
          // 代招
          tagsText += '代招'
        }
      })

      // 估算字符宽度 (这里使用简单估算，实际项目中可能需要更精确的计算)
      // 假设每行可以显示约18个中文字符
      const maxCharsPerLine = 18
      const maxTotalChars = maxCharsPerLine * 2 // 两行

      // 标签大约占用的字符数 (包括间距)
      const tagChars = tagsText.length + 2 // 加上间距

      // 可用于企业名称的字符数
      const availableChars = maxTotalChars - tagChars

      if (name.length <= availableChars) {
        // 名称不需要截断
        return {
          name,
          needEllipsis: false,
          tags: this.getTagsFromCooperativeModes(cooperativeModes),
        }
      }
      // 名称需要截断
      const truncatedName = name.substring(0, availableChars - 1)
      return {
        name: `${truncatedName}...`,
        needEllipsis: true,
        tags: this.getTagsFromCooperativeModes(cooperativeModes),
      }
    },

    /** 根据cooperativeModes获取标签信息 */
    getTagsFromCooperativeModes(cooperativeModes: any[] = []) {
      const tags: string[] = []
      cooperativeModes.forEach((mode: any) => {
        if (mode.cooperativeType === 1) {
          tags.push('派遣&外包')
        } else if (mode.cooperativeType === 2) {
          tags.unshift('代招')
        }
      })
      return tags
    },
  }),
)
