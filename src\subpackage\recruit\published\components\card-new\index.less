.card {
  border-radius: 24rpx;
  background-color: #fff;
  margin: 16rpx 24rpx 0 24rpx;
  overflow: hidden;
}

.card-content {
  padding: 32rpx 24rpx 0 24rpx;
}

.head {
  display: flex;
  align-items: center;
}

.head-box {
  display: flex;
  flex: 1;
  align-items: center;
  width: 0;
}

.title {
  .ellip(1);
  color: #000000d9;
  font-weight: bold;
  font-size: 30rpx;
}

.head-box-img {
  width: 24rpx;
  height: 24rpx;
}

.title-grey {
  color: #00000073;
}

.compete {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e8362e;
  font-size: 20rpx;
  color: #e8362e;
  border-radius: 8rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
  transform: translateY(-1rpx);
}

.compete-grey {
  color: #00000040;
  border: 1rpx solid #00000040;
}

.status {
  margin-left: auto;
  padding-left: 16rpx;
  font-size: 26rpx;
  color: #0092ff;
}

.text-content {
  margin: 16rpx 0;
  display: flex;
  align-items: center;
  color: #000000a6;
}

.text-item {
  font-size: 26rpx;
  display: flex;
  align-items: center;

  &::after {
    content: '';
    width: 2rpx;
    height: 24rpx;
    background: #000000a6;
    display: block;
    margin: 0 16rpx;
  }

  &:last-child::after {
    display: none;
  }
}

.text-content-grey {
  color: #00000073;
}

.text-item-grey {
  &::after {
    background: #00000073;
  }
}

.digital {
  margin-bottom: 24rpx;
  font-size: 26rpx;
  color: #00000073;
  padding-left: 24rpx;
  display: flex;
  align-items: center;
}

.digital-text {
  padding-right: 8rpx;
}

.digital-box {
  &::after {
    display: inline;
    content: '·';
    font-size: 26rpx;
    padding: 0 8rpx;
  }
  &:last-child::after {
    display: none;
  }
}

.actions {
  display: flex;
  align-items: center;
  padding: 0 24rpx 32rpx 24rpx;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 152rpx;
  height: 72rpx;
  border-radius: 16rpx;
  background: #f5f7fc;
  font-size: 26rpx;
  color: #000000a6;
  margin-left: 16rpx;

  &:first-child {
    margin-left: auto;
  }

  &:active {
    opacity: 0.8;
  }
}

.btn-primary {
  background: #e0f3ff;
  color: #0092ff;
}

.btn-refreshed {
  color: #00000040;
}

.footer {
  background: #e0f3ff;
  font-size: 26rpx;
  color: #0092ff;
  display: flex;
  height: 76rpx;
  padding: 0 24rpx;
  align-items: center;
  border-top: 1rpx solid #d0ebff;

  &:first-child {
    border-top: none !important;
  }
  
}

.footer-left {
  .ellip(1);
  flex: 1;
  display: flex;
  align-items: center;
}

.footer-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.fail-footer {
  position: relative;
  padding: 24rpx 20rpx;
  background: #ffebec;
}

.fail-msg {
  .ellip(2);
  color: #e8362e;
  font-size: 26rpx;
}

.wait-footer {
  padding: 24rpx 20rpx;
  background: #ffefde;
  display: flex;
  flex-direction: row;
  justify-content: space-between
}



.wait-msg {
  .ellip(2);
  color: #ff8904;
  font-size: 26rpx;
}
.wait-action {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 16rpx;
  flex-shrink: 0;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 32rpx;
  color: rgb(255, 137, 4);
}

.audit-footer {
  background: #e0f3ff;
  padding: 24rpx 20rpx;
}

.audit-msg-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.audit-msg {
  display: flex;
  align-items: center;
  color: #0092ff;
  font-size: 26rpx;
}

.consultation{
  margin-left: 16rpx;
}

.hiring-icon {
  // height: 36rpx;
  display: flex;
  padding: 1rpx 4rpx;
  align-items: center;
  justify-content: center;
  border: 2rpx solid @text45;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
  flex-direction: row;
  flex-shrink: 0;
  color: @text45;
  transform: translateY(-1rpx);
}