/*
 * @Date: 2022-04-01 09:26:20
 * @Description: 卡片列表
 */

import { VERSION } from '@/config/app'
import { subscribeComponent } from '@/lib/mini-component-page/index'
import { store, MapStateToData, connect, dispatch, actions, storage, messageQueue } from '@/store/index'
import {
  cardViewHistory, handlerRecruitRepeat,
  replaceAvatar, filterFullList, isReportEvent, listExposure, dealRecruitCard4, reportRecruitList4,
} from '@/utils/helper/list/index'

import { getDom, getMenuButtonBoundingClientRect, guid } from '@/utils/tools/common/index'
import dayjs from '@/lib/dayjs/index'
import { autoResumePop } from '@/utils/helper/resume/index'

import miniConfig from '@/miniConfig/index'
import { dealDialogRepByApi, postResume1DataSave } from '@/utils/helper/dialog/index'

import { getPageCode } from '@/utils/helper/resourceBit/index'
import { getFilterConditionCount, handleFilterConditionToFetch } from '@/pages/index/components/card-list/utils'
import { getCardMode } from '@/utils/helper/list/cardTitleUtils/utils'
import { toLogin } from '@/utils/helper/common/toLogin'
import { isToComplaintOrClassify } from '@/utils/helper/common/index'

const { height, top } = getMenuButtonBoundingClientRect()
const mapStateToData: MapStateToData = (state) => {
  const { storage, classify } = state
  return {
    // 工种文案
    classText: classify.recruitLabel,
    // 排序
    recruitSearchSort: storage.recruitSearchSort,
    recruitOcc2Value: classify.recruitOcc2Value,
    // 是否登录
    login: storage.userState.login,
    /** 结构化筛选结果 */
    filterCondition: state.structFilter.filterConditionObj.searchPage?.condition || {},

    /** 结构化筛选模板 */
    filterConditionTem: state.structFilter.filterConditionObj.searchPage?.templateConfig || [],
  }
}

Component(subscribeComponent({
  // 下拉刷新
  async onPullDownRefresh() {
    const { formatMoreWords } = this.data
    isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
    this.resetState()
    this.onRefresh(1, this.data.keywords, true, formatMoreWords, false).finally(() => {
      wx.stopPullDownRefresh()
    })
    wx.$.selectComponent.call(this, '#subscribe').then((comp) => comp.getGuideSubscribe())
  },
  // 触底刷新
  onReachBottom() {
    const { formatMoreWords } = this.data
    if (!this.data.noData && this.data.hasMore) {
      this.onRefresh(this.page + 1, this.data.keywords, this.isFresh, formatMoreWords)
    }
  },
})(connect(mapStateToData)({
  properties: {
    params: {
      type: Object,
      value: {},
    },
    /** 用户是否重新选择区域。用户手动选择地区时传入true。 */
    reselectArea: { type: null, value: false },
    /** 排序规则 */
    sortData: { type: null, value: { id: 2, name: '综合排序' } },
    /** 列表来源 */
    listSource: {
      type: String,
      value: '',
    },
    /** 展示实名工友共享标签的页面 */
    pageShowAuth: {
      type: Boolean,
      value: false,
    },
    // 是否属于搜索场景--到搜索列表页后，工种改成“热门工种”
    isFromScenarioExtension: {
      type: Boolean,
      value: false,
    },
    // 是否是从招工搜索结果页面的工种选择器
    isBelongListRecruitResult: {
      type: Boolean,
      value: false,
    },
    // 招工-搜索结果页的已选择的GS词
    formatMoreWords: {
      type: null,
      value: [],
    },
    /** 是否只需在主站的招工大列表和搜索结果列表显示的功能 */
    isFromMainRecruitList: {
      type: Boolean,
      value: false,
    },
    /** 是否是从含有筛选工种的列表来的（主站/工厂的列表。物流的没用这个组件） */
    isFromOccVList: {
      type: Boolean,
      value: false,
    },
    /** 从哪个页面跳转过来的 (recruitIndex: 招工首页) */
    fromPage: {
      type: String,
      value: '',
    },
    /** 页面路由参数 */
    query: {
      type: Object,
      value: {},
    },
    /** 搜索词来源 */
    keywords_source: {
      type: String,
      value: '',
    },
    /** 是否是搜索底纹词 */
    patternWords: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    // 列表到顶部的距离
    listScrollTop: 0,
    keywords: '',
    list: [], // 列表数据
    loading: true, // 加载状态
    hasMore: false, // 是否有更多
    noData: false, // 没有更多了
    isNullStatus: 0, // 用于空状态判断，后台返回的
    year: new Date().getFullYear(), // 备案号年份
    version: VERSION, // 版本号
    calcHeight: '0px', // 头部和tabbar的高度和
    // 第一次加载列表是否已完成
    firstLoaded: false,
    /** 拨打电话时间间隔-埋点使用 */
    startTime: dayjs().unix(),
    /** 首次弹出联系老板弹窗，不弹vip弹窗 */
    isFirstCallPhone: false,
    /** 评价弹窗是否加入中间号获取真实号码 */
    isMidGetTel: {
      isShow: false,
      tel: '',
      action: 2,
    },
    /** 招工详情评价内容控制器 */
    evaluateContentControl: {
      content: [],
      show: false,
      expression: 0,
    },
    /** 是否点击拨打电话 */
    isCallPhone: false,
    /** 列表缓存超时自动刷新的时间 */
    _cacheExpiredSecond: 0,
    /** 广告的unitId */
    advertUnitId: '',
    info: null,
    resumeAreaId: '',
    // 搜索的职位
    occV2: [],
    /** 解构化筛选去除不限选项的条件数量 */
    filterConditionCount: 0,
    /** card 是否是v5 */
    isV5Card: false,
    /** 是否验证名企标签 */
    isVerifyFamous: true,
    /** 是否是招聘新卡片 */
    isNewZPCard: true,
    /** 新DD卡片实验 */
    joblist_newDDcard: 'oldDD',
  },
  lifetimes: {
    async created() {
      this.clickIndex = null
      this.listIds = []

      const isV5Card = await wx.$.u.getAbUi('JobList', 'NewList')
      this.setData({
        isV5Card,
      })
    },
    async ready() {
      const headerHeight = height + top
      const calcHeight = `calc(${headerHeight}px + 98rpx)`
      const abOk = await wx.$.u.getAbUi('WX_xiaochengxu', 'xiaochengxuNB')
      // 是否验证名企标签
      const isVerifyFamous = await wx.$.u.isAbUi('joblist_MQtag', 'ismqtag')
      // 是否是招聘新卡片
      const isNewZPCard = await wx.$.u.isAbUi('joblist_newZPcard', 'newZP')
      // 新DD卡片实验
      const joblist_newDDcard = await wx.$.u.getAbUiStgyId('joblist_newDDcard')
      // console.log('搜索页兼职卡片实验组:', {
      //   isVerifyFamous,
      //   isNewZPCard,
      //   joblist_newDDcard,
      // })
      this.setData({
        calcHeight,
        advertUnitId: abOk ? miniConfig.advert.recruitUnitId : '',
        isVerifyFamous,
        isNewZPCard,
        joblist_newDDcard,
      })
    },
    detached() {
      isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
    },
  },
  pageLifetimes: {
    async show() {
      // 获取tab期望职位mode 用于判断卡片模式
      this.getTabMode()
      const isRef = this.automaticRefresh(true)
      if (isRef) {
        storage.removeSync('isShowPopup')
        storage.removeSync('showRealTel')
        storage.removeSync('showRealTelState')
        return
      }
      const isShowPopup = storage.getItemSync('isShowPopup')
      const { info, isCallPhone, list } = this.data
      const { recruitCallFrom } = store.getState().index
      let showAutoPop = false
      if (recruitCallFrom === 'recruitSearch') {
        // 自动生成简历
        showAutoPop = await autoResumePop((autoPopDialog) => this.triggerEvent('onAutoPop', { autoPopDialog }), true)
      }
      const currentCard = (list && info ? list.find(item => item.id == info.id) || info : info) || {}
      /** 中间号通话结束弹窗(自动生成简历优先级大于评价弹窗) */
      const currentPage = wx.$.r.getCurrentPage()
      if (isShowPopup == currentPage.route && info && info.id && !showAutoPop) {
        this.setData({ isCallPhone: false })
        wx.$.l.showMidPopup(0, 'job', info.id, null, { from: 3 }).then((res: any) => {
          /** 收藏成功更新页面 */
          if (res?.type == '3') {
            this.setData({
              evaluateContentControl: {
                show: !currentCard.isWhiteCollar,
              },
              /** 是否加入中间号获取真实号码 */
              isMidGetTel: {
                isShow: true,
                tel: res.tel,
                action: 2,
              },
            })
          } else if (res?.type == '4') {
            this.setData({
              evaluateContentControl: {
                show: !currentCard.isWhiteCollar,
              },
              isMidGetTel: {
                isShow: false,
                tel: '',
                action: 2,
              },
            })
          }
        })
      } else {
        // 增加定时器，因评价弹框和首页弹框冲突，需要判断是否有评价弹框弹出
        setTimeout(() => {
          storage.setItemSync('isShowPopup', '')
        }, 20)
      }
      if (!isShowPopup && isCallPhone && !showAutoPop) {
        this.setData({
          evaluateContentControl: {
            show: !currentCard.isWhiteCollar,
          },
          isCallPhone: false,
          isMidGetTel: {
            isShow: false,
            tel: '',
            action: 2,
          },
        })
      }
      if (this.back) {
        /** 水印处理 */
        const list = cardViewHistory.getHistoryList('recruit', this.data.list, 'jobId')
        const { login } = storage.getItemSync('userState')
        let expendIntegral = null
        if (login) {
          const { data } = await wx.$.javafetch['POST/integral/v1/memberIntegral/getMemberIntegral']({})
          expendIntegral = Number(data.expendIntegral)
        }
        const nList = list.map(item => {
          return Object.assign(dealRecruitCard4(item))
        })
        this.setData({
          list: nList,
        })
        this.data.list?.length && this.onListScroll(2)
      }
      this.back = true
    },

    hide() {
      this.setData({
        evaluateContentControl: {
          show: false,
        },
      })
      this.back = true
      isReportEvent.call(this, this.data.list, (res) => this.uploadStatisticsData.call(this, res))
    },
  },
  methods: {
    resetState() {
      this.clickIndex = null
      this.listIds = []
    },
    getTabMode() {
      const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
      const selectSecondaryClassifyTabId = storage.getItemSync('selectSecondaryClassifyTabId')
      const classifyTabClassify = storage.getItemSync('classifyTabClassify')

      // 使用封装的卡片模式判断函数
      const { cardMode, debug: debugInfo } = getCardMode(
        selectClassifyTabId,
        selectSecondaryClassifyTabId,
        classifyTabClassify,
      )

      console.log('搜索页卡片列表tab信息:', debugInfo)
      this.setData({ cardMode })
    },
    updateFilterCondition() {
      this.onRefresh(1, this.data.keywords, true, this.data.formatMoreWords, false)
    },
    // 判断列表是否需要自动刷新
    automaticRefresh(isListEm = false) {
      const { _cacheExpiredSecond } = this.data
      if (_cacheExpiredSecond > 0) {
        const now = new Date().getTime()
        if (now > _cacheExpiredSecond) {
          wx.pageScrollTo({ scrollTop: 0 })
          this.setData({ _cacheExpiredSecond: 0 })
          if (isListEm) {
            this.setData({ list: [] })
          }
          const { formatMoreWords, keywords } = this.data
          this.onRefresh(1, keywords, true, formatMoreWords, true, {
            refreshSuccess: () => {
              setTimeout(() => {
                wx.$.msg('信息列表已刷新')
              }, 100)
            },
          })
          return true
        }
      }
      return false
    },
    // page: 多少页, clear 是否立即清楚数据
    async onRefresh(
      npage = 1,
      keywords = '',
      isnFresh = false,
      formatMoreWordArr = [],
      showloading = true,
      options = { refreshSuccess: () => { }, keywordRec: '' },
    ) {
      const { sortData,
        isBelongListRecruitResult,
        isFromScenarioExtension,
        firstLoaded,
        _cacheExpiredSecond,
        reselectArea } = this.data

      const isChoosed = store.getState().storage.common.isHaveChoosedRecruitResultlist
      const page = npage
      const isFresh = isnFresh
      this.page = page
      this.isFresh = isFresh
      this.setData({ loading: true, keywords })
      this.pageSize = 15
      // 判断有工种数据情况
      await messageQueue(state => state.classify.requestData)
      // 获取工种数据及label，id
      const { recruitOcc2Value } = store.getState().classify

      const cls: any = {}
      cls.occV2 = recruitOcc2Value
      if (isBelongListRecruitResult && isFromScenarioExtension && !isChoosed) {
        cls.occV2 = [{ industry: -1, occIds: [] }]
      }
      this.setData({ occV2: cls.occV2 })
      let newDataList = []
      try {
        if (page == 1) {
          firstLoaded && showloading && wx.$.loading('加载中...')
        }
        /** 结构化筛选数据 */
        const filterCondition = handleFilterConditionToFetch(this.data.filterCondition)
        const searchParams: any = {
          currentPage: page,
          pageSize: this.pageSize,
          keywords,
          useIpLocation: !!store.getState().storage.userLocationCity?.isFirst,
          gsList: [],
          filterCondition,
          ...cls,
        }
        const curPage = wx.$.nav.getThisPage(this)
        const myOptions = curPage ? curPage.options : {}

        if (myOptions.keywordRec) {
          searchParams.keywordRec = JSON.parse(myOptions.keywordRec)
        }

        const { userLocationCity } = store.getState().storage
        const { recruitCityObj, id, recruitSearchCityObj } = userLocationCity || {} as any
        const { citys: recCitys } = recruitCityObj || {}
        const { citys: seachCitys } = recruitSearchCityObj || {}
        const nsearchCitys = seachCitys || recCitys || []
        if (wx.$.u.isArrayVal(nsearchCitys)) {
          const { id: areaId } = nsearchCitys[0] || {}
          searchParams.areaId = areaId
          searchParams.areaIds = nsearchCitys.filter(Boolean).map(item => Number(item.id))
        } else {
          searchParams.areaId = id || '1' // 当前城市
          searchParams.areaIds = [Number(searchParams.areaId)]
        }
        const { userLocation, L_IS_GEO_AUTH } = store.getState().storage
        if (L_IS_GEO_AUTH == 1) {
          const [longitude, latitude] = userLocation.split(',')
          if (longitude && latitude) {
            searchParams.location = { longitude, latitude }
          }
        }

        if (formatMoreWordArr.length > 0) {
          searchParams.gsList = formatMoreWordArr
        }
        // 判断是否超时，如果超时自动刷新列表数据
        if (_cacheExpiredSecond > 0 && page != 1) {
          const isRre = this.automaticRefresh()
          if (isRre) {
            return newDataList
          }
        }
        let data
        if (!searchParams.keywords && !searchParams.keywordRec) {
          const nParams: any = {
            currentPage: searchParams.currentPage,
            pageSize: searchParams.pageSize,
            areaId: Number(searchParams.areaId || '1'), // 当前城市
            areaIds: (searchParams.areaIds || []).filter(Boolean),
            useIpLocation: !!store.getState().storage.userLocationCity?.isFirst,
            downFlush: isFresh ? 1 : 0,
            occV2: searchParams.occV2,
            location: searchParams.location,
            listType: 1,
            filterCondition,
          }
          if (this.time && page != 1) {
            nParams.lastTime = this.time
          }
          if (nParams.occV2.length) {
            // 过滤industry为空的数据
            nParams.occV2 = nParams.occV2.filter((item) => item.industry)
          }
          const listRes = await wx.$.javafetch['POST/job/v3/list/job/list'](nParams as any, { hideMsg: true, isNoToken: true })
          data = (listRes || {}).data || {}
          this.time = data.lastTime
        } else {
          searchParams.listType = sortData.id
          searchParams.reselectArea = reselectArea
          delete searchParams.areaId
          const searchRes = await wx.$.javafetch['POST/job/v2/search/job/search'](searchParams, { hideMsg: true })
          data = (searchRes || {}).data || {}
        }

        const { cacheExpiredSecond } = data || {}
        if (searchParams.currentPage == 1 && cacheExpiredSecond && cacheExpiredSecond > 0) {
          const futureTime = dayjs().add(cacheExpiredSecond, 'second').valueOf()
          this.setData({ _cacheExpiredSecond: futureTime })
        }
        !this.data.firstLoaded && setTimeout(() => this.setData({ firstLoaded: true }), 150)
        // 是否为空状态
        this.setData({
          hasMore: (data.list || []).length != 0,
          page,
        })
        // 如果是第一页并且数据为0，就展示空数据
        if (page == 1 && (!data.list || data.list.length == 0)) {
          this.setData({ noData: true, list: [] })
          return newDataList
        }
        // !这里进行值过滤
        if (page == 1) {
          /// 如果是第一页将listIds设置为空数组
          this.listIds = []
        }
        // 当日登录后从主站招工列表页进入招工搜索页，产生搜索行为，在招工搜索结果页请求第二页时弹出自动生成简历弹窗
        if (page == 2 && this.data.fromPage == 'recruitIndex') {
          // 自动生成简历
          autoResumePop((autoPopDialog) => {
            this.triggerEvent('onAutoPop', { autoPopDialog })
          }, true)
        }
        // 处理重复数据
        const result = handlerRecruitRepeat(this.listIds, data.list || [])
        // 获取重复数据id
        this.listIds = result.listIds
        // 水印处理
        await cardViewHistory.setHistoryList('recruit', result.dataList, 'jobId')
        const list = cardViewHistory.getHistoryList('recruit', result.dataList, 'jobId').map((item) => ({ ...item, image: replaceAvatar(item.image) }))
        // 处理列表招满情况
        const finalList = filterFullList.call(this, searchParams, data, list, () => {
          this.onRefresh(this.page + 1, keywords, this.isFresh)
        })
        // 处理卡片数据
        const myNewList = finalList.map((item, index) => {
          return Object.assign(dealRecruitCard4(item), {
            guid: guid(),
            location_id: index + 1,
            search_result: keywords,
            currentPage: searchParams.currentPage,
            // 当前信息是第几页
            pagination: searchParams.currentPage,
            // 当前信息是第currentPage页的第几条数据
            pagination_location: index + 1,
            source: '搜索结果',
            source_id: '2',
            keywords_source: this.data.keywords_source,
          })
        })
        // 如果是第一页，那么就直接赋值
        if (page == 1) {
          // 第一页时初始化招满属性
          this.initFullProps()
          // 只赋值5条
          const currList = myNewList.slice(0, 5)
          this.setData({ list: currList, noData: false })
          // 等待200ms赋值剩下的
          setTimeout(() => {
            this.setData({ list: currList.concat(myNewList.slice(5)), noData: false })
          }, 200)
          options.refreshSuccess && options.refreshSuccess()
          newDataList = [...myNewList]
          return newDataList
        }

        // 如果不是第一页就对某个数组下标赋值
        const { length } = this.data.list
        const newList = myNewList.reduce((obj, item, i) => {
          item.location_id = i + length + 1
          // eslint-disable-next-line no-param-reassign
          obj[`list[${i + length}]`] = item
          return obj
        }, { noData: false })
        newDataList = newList
        this.setData(newList)
      } finally {
        if (page == 1) {
          firstLoaded && showloading && wx.hideLoading()
        }
        this.data.list?.length && this.onListScroll(page)
        this.setData({ loading: false })
        !this.data.firstLoaded && setTimeout(() => this.setData({ firstLoaded: true }), 150)
      }

      return newDataList
    },
    // 初始化招满信息
    initFullProps() {
      this.fullProps = {
        /** 是否显示已过滤已招满信息提示 */
        isShowFullTips: false,
        /** 已招满过滤提示信息 */
        fullInfo: {
          /** 连续加载次数（连续加载X页都是已招满) */
          continuityNum: 0,
          /** 表示是否已经弹出过弹窗 */
          isShow: false,
          /** 已招满数量 */
          fullNum: 0,
          /** 已招满对应的页数 */
          fullPage: 0,
        },
      }
    },
    onClickTopBtn() {
      onClickTopBtn.call(this)
    },
    onClickDetail() {
      dispatch(actions.recruitDetailActions.setIsWarp({ isGone: true, isClickBack: false }))
    },
    /** 监听列表滚动上报埋点 */
    onListScroll(page) {
      const { listScrollTop } = this.data
      if (!listScrollTop) {
        getDom('.box').then((res) => {
          const top = res ? res.top : 0
          this.setData({ listScrollTop: top })
          listExposure.call(this, {
            page,
            elementId: '.card-item',
            top: -top,
            callback: (res) => this.uploadStatisticsData.call(this, res),
          })
        })
        return
      }
      listExposure.call(this, {
        page,
        elementId: '.card-item',
        top: -listScrollTop,
        callback: (res) => this.uploadStatisticsData.call(this, res),
      })
    },
    /** 埋点上报 */
    async uploadStatisticsData(res) {
      const item = res.item || {}
      // 是否展示名企标签
      const isVerifyFamous = await wx.$.u.isAbUi('joblist_MQtag', 'ismqtag')
      const display_label_source = item.companyInfo?.enterpriseIcon?.url && isVerifyFamous ? [1] : [-99999]
      let active_label = ''
      if (this.data.isV5Card) {
        active_label = item.replyInfo && (item.replyInfo.time || item.replyInfo.count) ? `${item.replyInfo.time}${item.replyInfo.time && item.replyInfo.count ? '·' : ''}${item.replyInfo.count}` : item.activeDate
      }
      reportRecruitList4(res, {
        search_result: this.data.keywords,
        source: '搜索结果',
        source_id: '2',
        active_label,
        display_label_source,
      })
    },

    /** 卡片底部联系老板按钮直接拨打电话 */
    async onCallPhoneBtn(e) {
      wx.$.l.clickBossReport('5')
      const { isFromScenarioExtension, isBelongListRecruitResult } = this.data
      this.setData({ info: {} })
      wx.$.l.callPhoneBtnOfList.call(this, e.detail, {
        refresh: async () => {
          /** 水印处理 */
          const list = cardViewHistory.getHistoryList('recruit', this.data.list, 'jobId')
          const { login } = storage.getItemSync('userState')
          let expendIntegral = null
          if (login) {
            const { data } = await wx.$.javafetch['POST/integral/v1/memberIntegral/getMemberIntegral']({})
            expendIntegral = Number(data.expendIntegral)
          }
          const nList = list.map((item) => {
            if (item.jobId == e.detail.jobId) {
              item.isLook = true
            }
            return Object.assign(dealRecruitCard4(item))
          })
          this.setData({
            list: nList,
          })
        },
        page_name: '招工-搜索结果页',
        click_entry: '5',
        isFromScenarioExtension,
        isBelongListRecruitResult,
      })
    },
    /** 聊一聊 */
    async onGoToChat({ detail: item }) {
      wx.$.l.clickBossReport('10')
      if (item.isEnd && item.isEnd.code == 1) {
        this.triggerEvent('onGoToChat', item)
      }
      if (item.isEnd.code == 2) {
        wx.$.msg('信息已招满，暂不支持与老板联系')
        return
      }
      const { login } = store.getState().storage.userState
      const pageCode = await getPageCode()
      /** 未登录，去登录 */
      if (!login) {
        wx.$.alert({
          content: '登录后才能创建聊天',
          confirmText: '登录账号',
          cancelIcon: true,
        }).then(() => {
          toLogin(true).then(() => {
          })
        })
        return
      }
      // 存储首页发布找活名片弹窗 v4.0.0 的数据
      postResume1DataSave('contact')
      // 点击聊一聊如果未查看电话，需要先查看电话
      const { isLook } = item || {}
      // if ) {
      const isOnlyPreCheck = isLook
      const scene = 1
      const param: any = {
        jobId: item.jobId,
        scene,
        lookType: 2,
        isPrivacy: false,
        todayCallNum: storage.getItemSync('lookJobNum'),
      }
      const { recruitOcc2Value } = this.data
      const occV2Param = recruitOcc2Value

      param.occV2 = occV2Param
      const { buryingPoint } = store.getState().recruitDetail
      if (buryingPoint.info.backend_id) {
        param.algorithmId = buryingPoint.info.backend_id
      }
      await wx.$.l.recruitTelChat(param, {
        query: {
          pageCode,
        },
        // isOnlyPreCheck,
      }, {
        failCallPhoneReport: () => {
          wx.$.l.goToChatReport(item, { click_entry: '10', get_status: '0' })
        },
        succesCallPhoneReport: (res) => {
          wx.$.l.goToChatReport(item, { click_entry: '10', get_status: item.isLook ? '2' : '1', chatRes: res })
        },
        chatCall: async (res) => {
          if (!isOnlyPreCheck) {
            const { data } = res || {}
            const { priceInfo } = data || {}
            const { isExpenseIntegral } = priceInfo || {}
            wx.$.l.setLookJobNum(isExpenseIntegral)
            const nList = this.data.list.map((i) => {
              if (i.jobId == item.jobId) {
                i.isIm = true
              }
              return i
            })
            this.setData({
              list: nList,
            })
          }

          dispatch(actions.timmsgActions.setState({ recruitInfo: item }))
          wx.$.l.initGroup(item.jobId)
        },
      })
    },
    /**
         * 点击合作意向，里面的投诉按钮
         */
    async onComplaint({ detail }) {
      this.setData({ evaluateContentControl: { show: false } })
      const pageCode = getPageCode()
      // 获取一个弹窗的配置，如果有，则需要弹出弹窗
      const { data } = await wx.$.javafetch['POST/audit/v1/complaint/checkIntegralState']({ infoId: String(detail.id || 0) })
      if (data?.dialogData?.dialogIdentify) {
        const result = await dealDialogRepByApi(data?.dialogData?.dialogIdentify, data?.dialogData?.template, pageCode)
        wx.$.showModal({
          ...result,
          pageCode,
        })
      }
      /** 去投诉页(记得投诉成功时在投诉页面修改当前页面的投诉状态) */
      isToComplaintOrClassify({ id: detail.id, projectId: '1100', targetUserId: 0, targetId: '', complaintSource: '1004' })
    },
    /** 评价弹框关闭 */
    onEvalClose() {
      this.setData({
        // info: {},
        evaluateContentControl: {
          show: false,
        },
      })
    },
    /** 获取简历期望工作城市 */
    async getResumeArea() {
      const { login } = store.getState().storage.userState
      if (!login) {
        return '1'
      }
      let { resumeAreaId } = this.data
      if (!resumeAreaId) {
        const { data } = await wx.$.javafetch['POST/resume/v3/base/resumeHopeArea']().catch(err => err)
        const list = (data && data.list) || []
        resumeAreaId = (list[0] && list[0].id) || '1'
        this.setData({ resumeAreaId })
      }
      return resumeAreaId
    },
  },
  observers: {
    filterCondition(v) {
      this.setData({
        filterConditionCount: getFilterConditionCount(v),
      })
    },
  },

})))

// 点击置顶
async function onClickTopBtn() {
  const { login } = store.getState().storage.userState
  /** 发布招工页面 */
  const publishRecruit = '/subpackage/recruit/fast_issue/index/index'
  /** 已发布招工列表 */
  const publishedRecruit = '/subpackage/recruit/published/index'
  if (login) {
    try {
      const { data } = await wx.$.javafetch['POST/job/v2/manage/job/list']({ currentPage: 1, pageSize: 2, type: 'all' })
      if (data.data.length > 1) {
        wx.$.r.push({
          path: publishedRecruit,
          query: {
            reback: 'toptips',
          },
        })
      } else if (data.data.length == 1) {
        const item: any = data.data[0] || {}
        // 信息审核中 或者 信息审核通过
        const { checkInfo, topData } = item
        const { isCheck } = checkInfo || {}
        if (isCheck && (isCheck.code == 1 || isCheck.code == 2)) {
          const { jobTopStatus, defaultAreaId } = topData || {}
          // 从未置顶 || 置顶已过期
          if (jobTopStatus.code == -1 || jobTopStatus.code == 3) {
            // wx.$.r.push({
            //   path: recruitTop,
            //   query: {
            //     subscribe: 0,
            //     job_id: item.jobId,
            //     defaultTopArea: defaultAreaId,
            //   },
            // })
            wx.$.l.existJobTopSet(item.jobId)
          } else if (jobTopStatus.code == 2) {
            // wx.$.r.push({
            //   path: recruitTop,
            //   query: {
            //     job_id: item.jobId,
            //   },
            // })
            wx.$.l.existJobTopSet(item.jobId)
          } else {
            wx.$.r.push({
              path: publishedRecruit,
              query: {
                reback: 'toptips',
              },
            })
          }
        } else {
          wx.$.r.push({
            path: publishedRecruit,
            query: {
              reback: 'toptips',
            },
          })
        }
      } else {
        wx.$.r.push({
          path: publishRecruit,
        })
      }
    } catch (err) {
      wx.$.r.push({ path: publishRecruit })
    }
  } else {
    wx.$.r.push({
      path: publishRecruit,
    })
  }
}
