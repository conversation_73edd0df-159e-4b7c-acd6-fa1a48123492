/*
 * @Date:
 * @Description: 信息流
 */

import { storage } from '@/store/index'
import { dealDialogApi } from '@/utils/helper/dialog/index'
import { getExposureConfig } from '@/utils/helper/list/index'
import { getDom, getSystemInfoSync, rpxToPx } from '@/utils/tools/common/index'
import { randIntNumber } from '@/utils/tools/validator/index'
import { handleTemData } from './utils'

/** 生成一个80-90范围的随机数 */
const perfection = randIntNumber(80, 90)
/** 在竖屏正方向下的安全区域 */
const { screenHeight, safeArea } = getSystemInfoSync()

Component({
  properties: {
    /** 信息流数据 */
    infoFlow: {
      type: Object,
      value: {},
    },
    top: {
      type: Number,
      value: 0,
    },
  },
  data: {
    perfection,
    /** 待完善数据 */
    tmpList: [],
    shouNum: 0,
    hasCalculateTag: false, // 计算标签宽度完成在现实，避免加号按钮抖动
  },
  observers: {
    infoFlow() {
      this.initData()
      this.observerItems = 0
      this.reportExposure()
    },
  },
  lifetimes: {
    detached() {
      this.pageSwitch()
    },
  },
  pageLifetimes: {
    hide() {
      this.pageSwitch()
    },
  },
  methods: {
    initData() {
      const { infoFlow } = this.data
      const item = infoFlow.tmpList[0] || {}
      const showNum = wx.$.u.getObjVal(infoFlow, 'tmpList.0.controlAttr.labelList.length', 0)
      this.setData({ tmpList: wx.$.u.deepClone(infoFlow.tmpList), showNum })
      if (item.controlTypeCode == 'LABEL' || item.controlTypeCode == 'POPUP_LABEL') {
        this.getLabelShowNums()
      } else {
        // 不用计算标签的类型，直接显示
        this.setData({ hasCalculateTag: true })
      }
    },

    /** 打开完善项弹窗 */
    async onClickComplete(e) {
      const isRealName = await this.getCompleteInfo()
      if (isRealName) {
        return
      }
      const tmpList = wx.$.u.deepClone(this.data.tmpList) || []
      const { index } = e.currentTarget.dataset || {}
      if (tmpList.length && (index || index === 0)) {
        tmpList[0].controlAttr.labelList[index].checked = true
        tmpList[0].value = [tmpList[0].controlAttr.labelList[index].code]
      }
      this.reportGl(2)
      wx.$.collectEvent.event('worklist_improve_resume_click', { type: (wx.$.u.isArrayVal(tmpList) && tmpList[0].reportType) || '4' })
      this.triggerEvent('openPop', tmpList)
    },
    /**
     *
     * type: 1. improveResumeExposure曝光埋点  2.improveResumeClick 点击埋点
     * type_id 类型id---1是个人信息、2是求职期望
     * */
    reportGl(type) {
      const { infoFlow } = this.data
      const item = infoFlow.tmpList[0] || {}
      const types = { 1: 'improveResumeExposure', 2: ' improveResumeClick' }
      const name = types[type]
      if (name) {
        const params = {
          type_id: [1, 3].includes(item.group) ? '1' : '2',
          ...(type === 2 ? { click_bottom: '去完善' } : {}),
        }
        wx.$.collectEvent.event(name, params)
      }
    },

    /** 埋点曝光逻辑 */
    reportExposure() {
      getDom.call(this, '.info-flow').then(async () => {
        // 获取曝光时长配置
        const { appearTime } = await getExposureConfig()
        this.appear_time = appearTime
        setTimeout(() => {
          this.observer = this.createIntersectionObserver({ thresholds: [0, 1], observeAll: true })
          const bottom = safeArea ? screenHeight - safeArea.bottom : 0
          const { tmpList, top } = this.data
          this.observer.relativeToViewport({ top: -top, bottom: -(rpxToPx(98) + bottom) })
          this.observer.observe('.info-flow', (res) => {
            const { intersectionRatio } = res
            if (this.isNoMoreExposure) {
              return
            }
            if (intersectionRatio === 0 && this.observerItems) {
              /** 曝光时长 */
              const exposureTime = (res.time - this.observerItems) / 1000
              if (exposureTime > Number(appearTime)) {
                this.reportGl(1)
              }
              this.observerItems = 0
              this.isNoMoreExposure = true
              return
            }
            if (intersectionRatio === 1 && !this.observerItems) {
              this.observerItems = res.time
              wx.$.collectEvent.event('worklist_improve_resume_exposure', { type: tmpList[0].reportType || '4' })
            }
          })
        }, 0)
      })
    },

    /** 页面切换时判断是否在曝光状态 */
    pageSwitch() {
      const now = new Date().getTime()
      const exposureTime = (now - this.observerItems) / 1000
      if (this.observerItems && !this.isNoMoreExposure && (exposureTime > Number(this.appear_time))) {
        this.reportGl(1)
        this.isNoMoreExposure = true
      }
    },

    /** 计算标签+加号位置 */
    getLabelShowNums() {
      let { showNum } = this.data
      try {
        const query = wx.createSelectorQuery().in(this) // 创建一个查询对象
        query.selectAll('.complete-info').boundingClientRect() // 查询 scroll-view 组件的数据的位置信息
        query.exec((res) => {
          if (res && res.length) {
            const maxHeight = rpxToPx(84)
            const item = res[0][0]
            const { height } = item || {}
            if (height && height > maxHeight) {
              showNum -= 1
              this.setData({ showNum })
              if (showNum > 0) {
                this.getLabelShowNums()
                return
              }
            }
            this.setData({ hasCalculateTag: true })
          } else {
            this.setData({ hasCalculateTag: true })
          }
          // const
        })
      } catch (err) {
        console.error(err)
        this.setData({ hasCalculateTag: true })
      }
    },
    async onClose() {
      const popup = await dealDialogApi({ dialogIdentify: 'guanbierciqueren' })
      if (popup) {
        wx.$.showModal(popup).then(({ routePath }) => {
          if (routePath === 'completeClose') {
            const { nextShowDay } = storage.getItemSync('resumeInfoFlow') || {}
            storage.setItem('resumeInfoFlow', {
              closeTime: new Date().getTime(),
              nextShowDay: !nextShowDay ? 2 : nextShowDay === 2 ? 5 : 7,
            })
            this.setData({ tmpList: [] })
          }
        })
      }
    },

    /** 获取完善信息以及实名状态 */
    async getCompleteInfo() {
      try {
        const selectClassifyTabId = storage.getItemSync('selectClassifyTabId')
        const params = {} as any
        if (!selectClassifyTabId || selectClassifyTabId.isRecommend) {
          params.scene = '1'
        } else {
          params.occId = Number(selectClassifyTabId.occIds[0])
        }
        if (!params.scene && selectClassifyTabId.industry !== -1) {
          params.positionType = selectClassifyTabId.positionType || 1
        }
        const { data, code } = await wx.$.javafetch['POST/resume/v3/guide/getGuideData'](params).catch((err) => err || {})
        if (code != 0 || !data) {
          return false
        }
        const { ifRealName } = data
        if (['birthday', 'sex', 'userName'].includes(this.data.tmpList[0].controlCode) && ifRealName) {
          wx.$.msg('您已实名，无需再修改。')
          this.setData({ tmpList: [] })
          return true
        }
        const tmpList = handleTemData(data, this.data.tmpList)
        this.setData({ tmpList })
      } catch (err) {
        console.error(err)
      }
      return false
    },
  },
})
