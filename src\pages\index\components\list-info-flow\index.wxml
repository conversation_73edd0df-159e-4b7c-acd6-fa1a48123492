
<!-- 完善项 -->
<view class="info-flow complete-content" wx:if="{{tmpList.length}}" catch:tap="onClickComplete">
  <view class="close" catch:tap="onClose"><icon-font type="yp-guanbi" size="32rpx" color="rgba(0,0,0,0.25)" /></view> 
  <view class="complete-tip">{{tmpList[0].title}}<text class="tip-gray" wx:if="{{tmpList[0].controlAttr.dataObj.ifMulti && tmpList[0].controlAttr.dataObj.limitNum > 1}}">【多选】</text></view>
  <view class="complete-text">{{tmpList[0].subtitle}}</view>

  <!-- 1.输入框控件 2.带单位的输入框控件-->
  <view class="complete-input" wx:if="{{tmpList[0].controlTypeCode == 'INPUT_STRING' || tmpList[0].controlTypeCode == 'INPUT_NUMBER' || tmpList[0].controlTypeCode == 'INPUT_WITH_UNIT' || tmpList[0].infoFlowType == 'input' }}">
    <view wx:if="{{tmpList[0].placeholder}}">{{tmpList[0].placeholder}}</view>
    <view wx:else>请输入{{tmpList[0].controlName}}</view>
    <icon-font wx:if="{{tmpList[0].controlCode == 'birthday'}}" type="yp-mid_jt" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
    <view class="input-right" wx:if="{{tmpList[0].controlTypeCode == 'INPUT_WITH_UNIT'}}">
      {{tmpList[0].controlAttr.dataObj.unit}}
    </view>
  </view>

  <!-- 3.点选控件 -->
  <view class="complete-info-box" wx:elif="{{tmpList[0].controlTypeCode == 'SPECIAL_CONTROL' || tmpList[0].controlTypeCode == 'LABEL' || tmpList[0].controlTypeCode == 'POPUP_LABEL'}}">
    <view class="complete-info {{hasCalculateTag ? '' : 'hide'}}" >
      <view class="attrList-item" wx:for="{{tmpList[0].controlAttr.labelList}}" wx:if="{{index < showNum}}" catch:tap="onClickComplete" data-index="{{index}}" wx:key="index">{{item.name}}</view>
      <view class="attrList-item add-icon" catch:tap="onClickComplete" wx:if="{{tmpList[0].controlTypeCode !== 'SPECIAL_CONTROL'}}">
        <icon-font type="yp-tianjia1" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
      </view>
    </view>
  </view>
  
    <!-- 4.自定义-点控-性别 -->
  <view class="custom-label" wx:elif="{{tmpList[0].controlTypeCode == 'RADIO'}}">
    <view class="custom-label-item" wx:for="{{tmpList[0].controlAttr.labelList}}" catch:tap="onClickComplete" data-index="{{index}}" wx:key="index">{{item.name}}</view>
  </view>

</view>
