import { MapStateToData, connect } from '@/store/index'
import { PLTools } from '@/utils/index'
import { getBrowseHistoryService, initIsShowToppingBtnStatus } from './api-server'
import { buryingPointOfEmptyBtnClick } from './burying-point'
import resource from '@/components/behaviors/resource'
import { subscribeComponent } from '@/lib/mini-component-page/index'
import { fetchResumeExist } from '@/utils/helper/resume/index'

const mapStateToData: MapStateToData = (state) => {
  return {
    role: state.storage.userChooseRole,
  }
}

/**
 * @name 浏览记录
 */
Component(
  subscribeComponent({})(
    connect(mapStateToData)({
      properties: {
        tabHeight: {
          type: String,
          value: '',
        },
        advertUnitId: {
          type: String,
          value: '',
        },
      },
      data: {
        seeMeParams: PLTools.getDefaultPLData({ pageSize: 15 }),
        browseMeParams: PLTools.getDefaultPLData({ pageSize: 15 }),
        browseMe: {},
        /** 广告的unitId */
        // advertUnitId: '',
        isShowTopping: false, // 名片是否在置顶
        hasResume: false, // 是否有名片

        // ------ 推荐工作列表所需数据
        // 查看招工是否直接跳转到首页
        isToIndex: false,
        // 推荐列表首次加载完成
        recommendFirstLoaded: false,
        // 获取推荐列表参数
        recommendParams: {
          currentPage: 1,
          pageSize: 15,
          lastTime: '',
        },
      },
      lifetimes: {
        async ready() {
          this.init()
        },
      },
      pageLifetimes: {
        show() {
        },
      },
      methods: {
        async init() {
          getBrowseHistoryService.call(this, { isRefresh: true })
          const { exist } = await fetchResumeExist()
          const isShowTopping = exist ? await initIsShowToppingBtnStatus() : false
          this.setData({ isShowTopping, hasResume: exist })
          // const abOk = await wx.$.u.getAbUi('WX_xiaochengxu', 'xiaochengxuNB')
          // this.setData({
          //   advertUnitId: abOk ? miniConfig.advert.callAndBrowseUnitId : '',
          // })
          ENV_IS_SWAN && resource.lifetimes.attached.call(this)
        },
        async onPullDownRefresh() {
          try {
            this.setData({ refreshing: true })
            await getBrowseHistoryService.call(this, { isRefresh: true })
          } finally {
            setTimeout(() => {
              this.setData({ refreshing: false })
            }, 5000)
          }
        },
        onReachBottom() {
          const { role, seeMeParams, browseMeParams } = this.data
          if (PLTools.isLoadingOrFinishState(role == 2 ? seeMeParams : browseMeParams)) {
            wx.$.selectComponent.call(this, '#recommend-list').then(async (widget) => widget.onReachBottom())
            return
          }
          getBrowseHistoryService.call(this, { isRefresh: false })
        },
        viewBoosPublishJobInfo(e) {
          const { userId, userName } = e.detail
          wx.$.r.push({ path: '/subpackage/resume/viewlog/index', query: { userId, userName } })
        },
        /**
        * @name 推荐列表-附近适合你的工作
        * @param {object} params page-页数 labelId-下一次触发label的id
        */
        async onService({ detail: { callback, params } }) {
          const { recommendFirstLoaded } = this.data
          callback({ recommendList: [], labelList: [], isLoaded: true })
          !recommendFirstLoaded && this.setData({ recommendFirstLoaded: true })
        },

        // 去加急
        async toTopping() {
          // wx.$.r.push({ path: '/subpackage/topset/topmset/index', query: { top_from: 'view_resume' } })
          const urlTop = encodeURIComponent('/urgent-resume?epc=active_jiajijianlih5')
          wx.$.r.push({
            path: `/subpackage/web-view/index?url=${urlTop}&isLogin=true`,
          })
        },

        // ------ 页面列表相关部分
        // 空数据的按钮跳转事件
        async clickEmptyBtn(e) {
          await wx.$.u.waitAsync(this, this.clickEmptyBtn, [e], 500)
          const btnType = e.currentTarget.dataset.type
          buryingPointOfEmptyBtnClick(btnType)
          const jumpPath = {
            /** 空状态-发布招工信息按钮-点击埋点 */
            zg: '/subpackage/recruit/fast_issue/index/index',
            /** 空状态-发布找活名片按钮-点击埋点 */
            zh: '/subpackage/resume/resume_publish/index?resume=1&ltype=1&ptype=perfect&optype=back',
            /** 我看过谁-空状态-查看好活按钮-点击埋点 */
            look_zg: '/pages/index/index',
            /** 我看过谁-空状态-查看找活按钮-点击埋点 */
            look_zh: '/pages/resume/index',
          }
          wx.$.r.push({ path: jumpPath[btnType] })
        },
      },
    }),
  ),
)
