<view class="picker-container container-class">
    <view class="picker-title-line" wx:if="{{titleVisible}}" >
        <view class="picker-title-text">{{title}}</view>
        <!-- <image src="https://cdn.yupaowang.com/yupao_common/87debc5c.png" class="picker-tips-ic" mode="aspectFill"/> -->
        <promote-tips></promote-tips>
    </view>
    <view class="picker-content" bind:tap="onPick">
        <view class="picker-content-text {{disabled ? 'disabled' : ''}}" wx:if="{{displayText}}">{{displayText}}</view>
        <view class="picker-content-placeholder" wx:else>{{placeholder}}</view>
        <icon-font type="yp-yp-icon_mbx_vip" size="40rpx" color="rgba(0, 0, 0, 0.25);" />
    </view>
</view>