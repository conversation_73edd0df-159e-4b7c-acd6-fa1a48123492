import { IAddressParams } from '@/lib/mini/utils/index.d'
import type { ITreeArea } from '@/utils/helper/location/type.d'
import { isByIdRegion, isByAdcodeRegion } from '@/utils/helper/location/index'
import { TTreeFullVal, TTreeVal } from './index.d'

/** 热门城市的id */
export const hotID = 'hot'
/** 港澳台的父级id */
export const hmtID = 'hmt'

/** 页面默认的地址配置 */
export const addrConfDef = {
  type: 'resume',
  /** 埋点使用 source_id 1-招工列表、2-发布招工、3-找活列表、4-编辑招工、5-编辑找活名片、6-招工搜索中间页、7-招工搜索结果页、8-找活搜索中间页、9- 找活搜索结果页 、10-发布找活名片、11- 新牛人引导 */
  point: { source_id: '' },
  headType: null,
  /** 多选类型, district: 代表只能单个城市区域多选 */
  selectType: null,
  /** 页面标题 */
  title: '选择城市',
  /** 页面UI显示的最大级数 */
  level: 3,
  /** 最大可选数量 */
  maxNum: 1,
  /** 已选中的地址集合 */
  areas: [],
  /** 是否多选 */
  isMultiple: false,
  /** 需要禁用的地址id数组，禁用的地址无法被选中 */
  disabledIds: [],
  /** 是否隐藏全国的地址选项 */
  hideNation: true,
} as IAddressParams & {
  isMultiple?: boolean
}

/** 页面地址配置 */
const addrConf = { ...addrConfDef }

const areaTreeFull = {
  /** 所有省份数组 */
  province: [] as TTreeFullVal,
  /** 所有城市，key值用省id作为key */
  city: {},
  /** 所有区域，key值用城市id作为key */
  district: {},
}

/** 清空地址数据 */
function clearAreaTreeFull() {
  if (`${addrConf.type}`.indexOf('job') === 0) {
    Object.assign(areaTreeFull, {
      province: [],
      city: {},
      district: {},
    })
  }
}

/** 港澳台 */
const areaHMT: ILocation.TAreaData = {
  id: hmtID,
  ad_code: '0',
  pid: '-1',
  name: '港澳台',
  ad_name: '港澳台',
  letter: 'gangaotai',
  initials: 'gat',
  level: 1,
}

/** 热门城市 */
export const areaHot: ILocation.TAreaData = {
  id: hotID,
  ad_code: '0',
  pid: '-2',
  name: '热门',
  ad_name: '热门',
  letter: 'remen',
  initials: 'rm',
  level: 1,
}

/** 热门城市地区数据 */
async function getHotArea(quanguo: ILocation.TAreaData) {
  const hotData: ILocation.TAreaData = {
    ...areaHot,
    children: [],
  }
  const { children } = hotData

  const areaHosts = [] as ILocation.TAreaData[]
  const { hotCities } = await getAreaConfig()

  if (wx.$.u.isArrayVal(hotCities)) {
    const areas = await wx.$.l.getAreaSearches(hotCities, 'id')
    areas.forEach(item => {
      if (item.current) {
        areaHosts.push(item.current)
      }
    })
  }

  children.push(
    { ...quanguo, pid: hotID, level: 2 },
    ...areaHosts,
  )

  return hotData
}

/** 港澳台地区数据 */
function getHMTArea(areas) {
  const hmtData: ILocation.TAreaData = {
    ...areaHMT,
    children: areas.map(({ children, ...item }) => ({ ...item, level: 2, pid: hmtID })),
  }
  return hmtData
}

/** 设置全部地区数据 */
async function setAreaTreeFull() {
  clearAreaTreeFull()
  const treeFull = { province: [], city: {}, district: {} }
  const { provinces: sortP } = await getAreaConfig()
  const treeData = await wx.$.l.getAreaTreeData()

  /** 删除并返回末尾三个地址港澳台 */
  const hmtData = getHMTArea(treeData.splice(-3, 3))
  treeData.push(hmtData)

  /** 删除第一个地址，插入热门城市 */
  const hotData = await getHotArea(treeData[0])
  treeData.splice(0, 1, hotData)

  treeData.forEach(({ children: listC, ...itemP }) => {
    /** 存储省份数据 */
    treeFull.province.push({ ...itemP })

    /** 二级地址 */
    if (itemP.id != '1') {
      // 处理全地址逻辑
      if (itemP.id == '0' || itemP.id == hotID || itemP.id == hmtID) {
        // 全国和热门港澳台不需要全
        treeFull.city[itemP.id] = []
      } else {
        treeFull.city[itemP.id] = [{ ...itemP, isFull: true, level: 2 }]
      }
    }
    if (wx.$.u.isArrayVal(listC)
        && wx.$.u.isArrayVal(treeFull.city[itemP.id], 0)
    ) {
      listC.forEach(({ children: listD, ...itemC }) => {
        /** 存储城市数据 */
        treeFull.city[itemP.id].push({ ...itemC })

        /** 三级地址 */
        if (itemC.id != '1' && !isByAdcodeRegion(itemC.ad_code)) {
          treeFull.district[itemC.id] = [{ ...itemC, isFull: true, level: 3 }]
        }
        if (listD && wx.$.u.isArrayVal(listD) && listD[0].id) {
          /** 存储城市区域数据 */
          listD.forEach(({ children, ...itemD }) => {
            treeFull.district[itemC.id].push({ gid: itemP.id, ...itemD })
          })
        }
      })
    }
  })

  /** 排序 */
  if (wx.$.u.isArrayVal(sortP)) {
    const temp = treeFull.province

    // 存储排序之后的值，这里首先删除和组装第一个元素
    const sortProvince = [temp.shift()]

    // 循环排序
    sortP.forEach(id => {
      const index = temp.findIndex(item => item.id == id)
      if (index > -1) {
        const delVal = temp.splice(index, 1)
        sortProvince.push(delVal[0])
      }
    })

    // 剩下的元素进行组合
    treeFull.province = sortProvince.concat(temp)
  }

  return treeFull
}

/** 获取热门城市id */
export async function getHotIds(): Promise<string[]> {
  const hot = areaTreeFull.province[0].id
  let hots = areaTreeFull.city[hot]
  if (!wx.$.u.isArrayVal(hots)) {
    const { children } = await getHotArea({
      ad_name: '中华人民共和国',
      ad_code: '100000',
      level: 1,
      id: 1,
      initials: 'qg',
      letter: 'quanguo',
      name: '全国',
      pid: 0,
    })
    hots = children
  }
  return hots.map(item => {
    return `${item.id}`
  })
}

/** 设置地址页的配置数据  */
export function getAddrConfig() {
  const routerData = wx.$.nav.getData()
  const isMultiple = routerData.maxNum > 1
  Object.assign(addrConf, {
    /** 是否多选 */
    isMultiple,
    ...routerData,
  })
  return {
    ...addrConf,
  }
}

/** 获取城市配置信息 */
export const getAreaConfig = (() => {
  /** 职位配置：热门地址id和省级排序 */
  const jobAreaConf = {
    /** 热门城市 */
    hotCities: [],
    /** 省份 */
    provinces: [],
  } as Required<YModels['POST/lbs/v2/area/areaConfig']['Res']['data']>

  /** 简历配置：热门地址id和省级排序 */
  const resumeAreaConf = {
    /** 热门城市 */
    hotCities: [],
    /** 省份 */
    provinces: [],
  } as Required<YModels['POST/lbs/v2/area/areaConfig']['Res']['data']>

  return async () => {
    const config = getAddrConfig()
    const isJob = config.type === 'job'
    if (isJob && wx.$.u.isArrayVal(jobAreaConf.hotCities)) {
      return jobAreaConf
    }
    if (!isJob && wx.$.u.isArrayVal(resumeAreaConf.hotCities)) {
      return resumeAreaConf
    }

    const res = await wx.$.javafetch['POST/lbs/v2/area/areaConfig']({
      // 业务类型 2:job, 1:resume
      type: isJob ? 2 : 1,
    }).catch(err => err)

    if (res && res.data && wx.$.u.isArrayVal(res.data.hotCities)) {
      if (isJob) {
        Object.assign(jobAreaConf, {
          ...res.data,
        })
      } else {
        Object.assign(resumeAreaConf, {
          ...res.data,
        })
      }
    }
    return isJob ? jobAreaConf : resumeAreaConf
  }
})()

/** 获取地址配置 */
export const getAreaTreeFull = (() => {
  const jobAreaTreeFull = { province: [], city: {}, district: {} }
  const resumeAreaTreeFull = { province: [], city: {}, district: {} }
  return async () => {
    const isJob = addrConf.type === 'job'
    let areaFull = isJob ? jobAreaTreeFull : resumeAreaTreeFull
    const cityIds = Object.keys(areaFull.city || {})

    if (wx.$.u.isArrayVal(areaFull.province, 30) && wx.$.u.isArrayVal(cityIds, 30)) {
      Object.assign(areaTreeFull, {
        ...areaFull,
      })
      return areaFull
    }

    areaFull = await setAreaTreeFull()

    isJob ? Object.assign(jobAreaTreeFull, {
      ...areaFull,
    }) : Object.assign(resumeAreaTreeFull, {
      ...areaFull,
    })

    Object.assign(areaTreeFull, {
      ...areaFull,
    })
    return areaFull
  }
})()

/**
 * 通过地址id获取地址信息
 */
export async function getSelectSearches(areas: (string | number)[] = []) {
  const areaSearches = await wx.$.l.getAreaSearches(areas)
  const hotCityArr = areaTreeFull.city[hotID] // 热门地址
  const deleteAddr: TTreeFullVal = []
  const selectAddr: TTreeFullVal = []
  let isHot = false
  let areaIsRegion = false
  /** 展开的地址信息 */
  let areaInfo: ITreeArea = { current: '', province: '', city: '', district: '', special: '' }

  /** 过滤一些数据 */
  let areaInfos = []
  if (wx.$.u.isArrayVal(areaSearches)) {
    areaInfos = areaSearches.filter(item => {
      if (!item.current) {
        return false
      }
      if (addrConf.hasAllType === 'city'
        && addrConf.type === 'resume'
        && !item.special
        && item.current.level < 2) {
        deleteAddr.push(item.current)
        return false
      }
      return true
    })
  }

  if (!wx.$.u.isArrayVal(areaInfos)) {
    return {
      selectAddr,
      /** 是否删除某些地址 */
      deleteAddr,
      areaInfo,
      /** 是否展开的热门城市 */
      isHot,
      /** areaInfo是否是直辖市 */
      areaIsRegion,
    }
  }

  areaInfos.forEach(item => {
    const current = item.current ? { ...item.current } : ''
    let isFull = false
    if (current) {
      isFull = current.level == 1
      if (addrConf.level == 3 && current.level == 2 && wx.$.u.isArrayVal(areaTreeFull.district[current.id])) {
        isFull = true
      }
      if (isFull && current.id != 1 && current.level < 3) {
        // 这里加1的表示选中的全地址
        current.level += 1
      }

      selectAddr.push({
        ...current,
        isFull,
      })
    }
  })

  if (addrConf.selectType !== 'resumePositionTab') {
    /** 搜索处于热门地址的信息 */
    areaInfo = areaInfos.find(({ current, special }) => {
      const areaCu: any = current || {}
      if (areaCu.pid == 1 && !special) {
        // 为省的时候, 非直辖市或者特别行政区
        return false
      }
      if (addrConf.level == 2) {
        // 处理显示为两层地址的逻辑
        if (areaCu.level == 3) {
          return false
        }
        if (!!special && areaCu.level == 2) {
          return false
        }
        return hotCityArr.some(({ id }) => id == areaCu.id)
      }
      return hotCityArr.some(({ id }) => id == areaCu.id || id == areaCu.pid)
    })
  }

  if (!areaInfo || !areaInfo.current) {
    // 不在热门地址的情况，将数组第一个元素赋值给areaInfo
    [areaInfo] = areaInfos
  } else {
    isHot = true
  }

  areaIsRegion = !!areaInfo.special
  // areaInfo.province ? isByIdRegion(areaInfo.province.id) : false

  return {
    selectAddr,
    areaInfo,
    /** areaInfo是否是直辖市 或者特别行政区 */
    areaIsRegion,
    /** 是否展开的热门城市 */
    isHot,
    /** 删除的某些地址 */
    deleteAddr,
  }
}

/** 设置选中状态 */
export function setSelect(areaTree: TTreeFullVal, areaId: string | number) {
  const newAreaTree = wx.$.u.deepClone(areaTree) || []
  /** 设置选中状态 */
  const areaFind = newAreaTree.find(item => item.id == areaId)
  if (areaFind) {
    areaFind.checked = true
  }
  return newAreaTree
}

/** 获取当前地址省市区id
 * @param isHot 当前是否打开的热门
 */
function getAreaId(area: TTreeVal): {
  provinceId: string | number
  cityId: string | number
  districtId: string | number
} {
  const { level, isFull, id, pid, gid } = area

  if (level == 1) {
    return { provinceId: id, cityId: '', districtId: '' }
  }
  if (level == 2) {
    return isFull
      ? { provinceId: id, cityId: '', districtId: '' }
      : { provinceId: pid, cityId: id, districtId: '' }
  }

  return isFull
    ? { provinceId: pid, cityId: id, districtId: '' }
    : { provinceId: gid, cityId: pid, districtId: id }
}

/** 根据addrConf配置信息处理地址数据
 * @param tree - 树形数据
 * @param level - 代表第一列第二列还是三列 默认 1
 */
export function handlerData<T extends TTreeFullVal>(tree: T, level: 1 | 2 | 3) {
  if (!wx.$.u.isArrayVal(tree)) {
    return [] as T
  }
  const pShowType = ['all', 'province'] // 全省份的显示逻辑
  const cShowType = ['all', 'city'] // 全城市的显示逻辑

  return tree.filter((item) => {
    let bool = true
    switch (level) {
      case 1:
        bool = item.id != 1 || !addrConf.hideNation
        break
      case 2: {
        const isRegion = isByAdcodeRegion(tree[0].ad_code, 'region')
        if (addrConf.hasAllType === 'noneHideRegion') {
          /** 如果是直辖市的时候，只保留全地址; 不是直辖市的时候，去掉全地址 */
          bool = isRegion ? item.isFull : !item.isFull
        } else if (item.isFull && addrConf.hasAllType && !isRegion) {
          /** 不是直辖市的时候 */
          bool = pShowType.indexOf(addrConf.hasAllType) !== -1
        }
        break
      }
      case 3:
        if (item.isFull && addrConf.hasAllType) {
          bool = cShowType.indexOf(addrConf.hasAllType) !== -1
        }
    }
    /* if (item.isFull && addrConf.hasAllType === 'none') {
      bool = false
    } */
    return bool
  })
}

/** 处理选中事件 */
export function handlerSelect(area: TTreeVal, selecting: TTreeVal[], isHot = false) {
  const oneArea: TTreeFullVal = handlerData(wx.$.u.deepClone(areaTreeFull.province), 1)
  let level = +area.level
  let twoArea: TTreeFullVal = []
  let threeArea: TTreeFullVal = []
  let { provinceId, cityId, districtId } = getAreaId(area)
  const isRegion = isByAdcodeRegion(area.ad_code) // 是否是直辖市或者特别行政区

  if (isHot) { // 点击的是热门地址
    if (isRegion) {
      level += 1
      cityId = provinceId
    }
    provinceId = hotID
  }
  /** 选择的是否是末级地址 */
  let isEnd = area.id == 1 // 如果为全国的话，就为true
  switch (level) {
    case 1:
      twoArea = wx.$.u.deepClone(areaTreeFull.city[provinceId]) || []
      break
    case 2:
      isEnd = true
      twoArea = wx.$.u.deepClone(areaTreeFull.city[provinceId]) || []
      if (addrConf.level == 3 && !area.isFull && !addrConf.forceCityOnly) {
        if (isRegion && isHot) {
          threeArea = wx.$.u.deepClone(areaTreeFull.city[cityId]) || []
        } else {
          threeArea = wx.$.u.deepClone(areaTreeFull.district[cityId]) || []
        }
      }
      if (addrConf.level == 3 && !addrConf.forceCityOnly) {
        isEnd = !wx.$.u.isArrayVal(threeArea)
      }
      break
    case 3:
      isEnd = true
      twoArea = wx.$.u.deepClone(areaTreeFull.city[provinceId]) || []
      if (!addrConf.forceCityOnly) {
        if (isRegion && isHot) {
          threeArea = wx.$.u.deepClone(areaTreeFull.city[cityId]) || []
        } else {
          threeArea = wx.$.u.deepClone(areaTreeFull.district[cityId]) || []
        }
      }
      break
  }

  if (!isEnd
    && addrConf.level == 3
    && !addrConf.forceCityOnly
    && !wx.$.u.isArrayVal(threeArea)
    && wx.$.u.isArrayVal(twoArea)
    && wx.$.u.isArrayVal(selecting)) {
    const openHot = area.id === hotID
    // 这里处理第三列没有数据的显示逻辑

    const selectData = selecting.find(item => {
      if (item.gid) {
        return twoArea.some((two) => two.id == item.pid)
      }
      if (item.isFull) {
        return twoArea.some((two) => two.id == item.id)
      }
      if (openHot) { // 点击热门地址根地址
        return twoArea.some((two) => two.id == item.pid)
      }
      return false
    })
    if (selectData && selectData.pid) {
      if (selectData.gid) {
        provinceId = selectData.gid
        cityId = selectData.pid
        districtId = selectData.id
      } else {
        provinceId = selectData.pid || provinceId
        cityId = selectData.id
        districtId = selectData.id
      }
      if (openHot && isByAdcodeRegion(selectData.ad_code)) {
        // 展开的是热门城市下的直辖市的特殊情况
        const areaId = isByIdRegion(selectData.id) ? cityId : provinceId
        threeArea = wx.$.u.deepClone(areaTreeFull.city[areaId]) || []
      } else {
        threeArea = wx.$.u.deepClone(areaTreeFull.district[cityId]) || []
      }
    }
  }

  return {
    /** 第一列数据 */
    oneArea,
    /** 第二列数据 */
    twoArea,
    /** 第三列数据 */
    threeArea,
    /** 当前选择地址的省id */
    provinceId,
    /** 当前选择地址的城市id */
    cityId,
    /** 当前选择地址的区域id */
    districtId,
    /** 是否是直辖市或者特别行政区 */
    isRegion,
    /** 是否是末级地址 */
    isEnd,
  }
}
