/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

import { connectPage, MapStateToData, store } from '@/store/index'
import { isIos } from '@/utils/tools/validator/index'

const recorderManager = wx.getRecorderManager()

const mapStateToData: MapStateToData = (state) => {
  const { message } = state
  const { imGlobalSwitch } = message || {}
  const { maxMessageWordCountLimit } = imGlobalSwitch || {}
  return {
    maxMessageWordCountLimit,
  }
}

// 录音部分参数
const recordOptions = {
  duration: 60200, // 录音的时长，单位 ms，最大值 600000（10 分钟）
  sampleRate: 44100, // 采样率
  numberOfChannels: 1, // 录音通道数
  encodeBitRate: 192000, // 编码码率
  format: 'aac', // 音频格式，选择此格式创建的音频消息，可以在即时通信 IM 全平台（Android、iOS、微信小程序和 Web）互通
}

let startPageY = 0
let movePageY = 0
let timer
Component(connectPage(mapStateToData)({
  properties: {
    shortcutbtnData: {
      type: Array,
      value: [
        {
          icon: 'yp-call-out',
          name: '拨打电话',
          type: 'call',
        },
      ],
    },
    // 拨打电话是否显示
    isShowTelBtn: { type: Boolean, value: false },
    emojiShow: { type: Boolean, value: false },
    inputFocus: { type: Boolean, value: true },
    // 判断白板是否需要显示
    isShowWhilte: { type: Boolean, value: true },
    // 底部工具栏是否显示
    toolbarShow: { type: Boolean, value: false },
    // 是否显示快捷功能
    isShortcutShow: { type: Boolean, value: true },
  },
  data: {
    // 是否显示表情包
    emShow: false,
    // 聊天内容
    chat: '',
    // 是否显示语音图标
    isRecorderShow: true,
    // 语音按钮文本
    yyBtntext: '按住说话',
    // 是否是按住语音按钮
    yyBtnPress: false,
    /** 是否显示录音动图 */
    yuYinGif: true,
    /** 当前录音倒计时开始 */
    djsTime: 60,
    /** 录音倒计时文案 */
    btmTxt: '',
    /** 键盘弹起高度 */
    keyboardHeight: 0,
    /** */
    isHide: false,
    isIos: isIos(),
  },
  observers: {
    emojiShow(emojiShow) {
      this.setData({ emShow: emojiShow })
    },
    chat(chat) {
      this.setData({ cursor: chat.length })
    },
  },
  lifetimes: {
    ready() {
      // 2.2 监听录音结束事件，录音结束后，调用 createAudioMessage 创建音频消息实例
      recorderManager.onStop((res) => {
        if (timer) {
          clearInterval(timer)
        }
        this.setData({ yyBtntext: '按住说话', yyBtnPress: false, btmTxt: '' })
        if (res.duration < 1000) {
          wx.$.msg('说话时间太短', 1000)
          return
        }
        if (this.data.yuYinGif) {
          this.triggerEvent('chatSend', { value: res, type: 'audio' })
        }
        this.setData({ yuYinGif: true })
      })
      recorderManager.onStart(() => {
        this.onDjs()
      })
      recorderManager.onInterruptionBegin(() => {
        this.onTouchend()
      })
      recorderManager.onError(async () => {
        this.onTouchend()
      })
    },
  },
  pageLifetimes: {
    show() { },
    hide() {
      this.setData({ inputFocus: false })
    },
  },
  methods: {
    // 最后5秒倒计时
    onDjs() {
      let t = 60
      if (timer) {
        clearInterval(timer)
      }
      timer = setInterval(() => {
        t -= 1
        if (t < 0) {
          clearInterval(timer)
        } else if (t == 0) {
          this.setData({ btmTxt: '说话时间太长' })
        } else if (t <= 5) {
          this.setData({ btmTxt: `将在${t}秒后结束录制` })
        }
      }, 1000)
    },
    // 点击更具工具栏
    onMoreToolBar() {
      this.triggerEvent('moreToolBarClick', {})
    },
    // 切换聊天类型
    onChatTypeClick() {
      const { conversation } = store.getState().timmsg
      const { rightsStatusInfo } = conversation || {}
      const { sendMsg } = rightsStatusInfo || {}
      if (sendMsg && !sendMsg.value) {
        this.triggerEvent('chatTypeClick', { type: 'audio' })
        return
      }
      this.onChangeAudio()
    },
    onChangeAudio() {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const that = this
      const isRecorderShow = !this.data.isRecorderShow
      wx.getSetting({
        success(res) {
          if (!res.authSetting['scope.record']) {
            wx.authorize({
              scope: 'scope.record',
              success() {
                that.setData({ isRecorderShow, inputFocus: isRecorderShow })
                that.triggerEvent('chatTypeClick', { isRecorderShow })
              },
              fail() {
                wx.$.confirm({
                  content: '若点击不授权，将无法使用语音功能',
                  cancelText: '不授权',
                  confirmText: '授权',
                  auth: true,
                  only: true,
                  intercept: true,
                })
              },
            })
          } else {
            that.setData({ isRecorderShow, inputFocus: isRecorderShow })
            that.triggerEvent('chatTypeClick', { isRecorderShow })
          }
          if (isRecorderShow) {
            that.onTtBlur()
          }
        },
      })
    },
    // 常用语击事件
    async onShorcutBtn(e) {
      await wx.$.u.waitAsync(this, this.onShorcutBtn, [e], 2000)
      const { type } = e.currentTarget.dataset
      this.triggerEvent('shorcutBtn', { type })
    },
    // 文档框叛变表情图标点击事件
    onEmojiShow() {
      const emShow = !this.data.emShow
      this.triggerEvent('emojiShow', { emShow })
      if (emShow) {
        this.onTtBlur()
      }
      this.setData({ isRecorderShow: true, inputFocus: !emShow })
    },
    onInputChange(e) {
      const { value } = e.detail
      const { maxMessageWordCountLimit, chat } = this.data
      const valueArr = Array.from(value)
      if (valueArr.length > maxMessageWordCountLimit) {
        wx.$.msg(`单条消息最大可发送${maxMessageWordCountLimit}字`)
      }

      if (chat.length >= maxMessageWordCountLimit && valueArr.length > maxMessageWordCountLimit) {
        this.setData({ chat })
        return
      }
      this.setData({ chat: valueArr.slice(0, maxMessageWordCountLimit).join('') })
    },
    // 表情点击事件
    onEmojiClick(e) {
      const { em } = e.detail
      const { maxMessageWordCountLimit, chat } = this.data
      const nChat = chat + em
      if (nChat.length > maxMessageWordCountLimit) {
        wx.$.msg(`单条消息最大可发送${maxMessageWordCountLimit}字`)
        return
      }
      this.setData({ chat: nChat })
    },
    // 发送聊天
    async onChatSend(e) {
      await wx.$.u.waitAsync(this, this.onChatSend, [e], 1000)
      const chag = this.data.chat || ''
      if (!chag || !chag.trim()) {
        return
      }

      const { conversation } = store.getState().timmsg
      const { rightsStatusInfo } = conversation || {}
      const { sendMsg } = rightsStatusInfo || {}
      if (!sendMsg || !sendMsg.value) {
        const isRecorderShow = !this.data.isRecorderShow
        this.triggerEvent('chatTypeClick', { isRecorderShow, type: 'msg', value: chag })
        return
      }
      this.triggerEvent('chatSend', { value: chag })
      const sData: any = { }
      const { type } = e.target.dataset
      if (type == 'btn') {
        sData.keyboardHeight = 0
      }
      this.setData(sData)
    },
    //
    async onTouchstart(e) {
      startPageY = e.touches[0].pageY
    },
    // 开始录音
    onLongpress() {
      if (this.data.yyBtnPress) {
        return
      }
      this.setData({ yyBtntext: '松开结束', yyBtnPress: true })
      recorderManager.start(recordOptions)
    },
    //
    onTouchend() {
      if (!this.data.yyBtnPress) {
        return
      }
      this.setData({ yyBtntext: '按住说话', yyBtnPress: false, btmTxt: '' })
      recorderManager.stop()
    },
    async onEmojiDel() {
      const { chat } = this.data
      if (chat) {
        // 匹配形如 [笑脸] 的末尾内容
        const match = chat.match(/\[([^\[\]]+)\]$/)
        const newEmojiList = await wx.$.l.NewEmojiData()
        const emojiList = await wx.$.l.EmojiData()

        let nChat = ''
        if (match && newEmojiList.includes(match[0])) {
          // 如果匹配到了，并且是合法表情，则整体删除
          nChat = chat.slice(0, -match[0].length)
        } else if (match && emojiList.includes(match[0])) {
          // 如果匹配到了，并且是合法表情，则整体删除
          nChat = chat.slice(0, -match[0].length)
        } else {
          // 否则删除最后一个字符
          nChat = chat.slice(0, -1)
        }
        this.setData({ chat: nChat })
      }
    },
    //
    onTouchmove(e) {
      if (!this.data.yyBtnPress) {
        return
      }
      movePageY = e.touches[0].pageY
      const yDistance = startPageY - movePageY
      if (yDistance >= 0) {
        if (yDistance <= 20 && !this.data.yuYinGif) {
          this.setData({ yuYinGif: true })
        } else if (yDistance > 20 && this.data.yuYinGif) {
          this.setData({ yuYinGif: false })
        }
      } else if (yDistance < 0) {
        if (yDistance > -20 && !this.data.yuYinGif) {
          this.setData({ yuYinGif: true })
        } else if (yDistance <= -20 && this.data.yuYinGif) {
          this.setData({ yuYinGif: false })
        }
      }
    },
    // 键盘高度发生变化
    bindkeyboardheightchange(e) {
      const { isHide, isShowWhilte } = this.data
      if (isHide || !isShowWhilte) {
        return
      }
      if (e?.detail?.height == 0) {
        return
      }
      const height = (e?.detail?.height || 0)
      this.triggerEvent('bindkeyboardheightchange', { ...e.detail, height })
      this.setData({ keyboardHeight: height, isHide: true })
    },
    onTtBlur() {
      this.setData({ keyboardHeight: 0 })
      setTimeout(() => {
        this.setData({ isHide: false })
      }, 150)
      this.triggerEvent('keyboardblur')
    },
    /** 禁止滚动操作 */
    onDisableMove() { },
  },
}))
