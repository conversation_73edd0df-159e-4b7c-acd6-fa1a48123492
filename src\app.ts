/*
 * @Date: 2022-07-07 15:56:28
 * @Description: app 初始化
 */

import '@/lib/mini/core'
import { store, Provider, dispatch, actions, storage } from '@/store/index'
import {
  autoUpdate,
  buryingPoint,
  init,
  getQueryParam,
  collectShareLaunch,
  handleOnShowRedirect,
  handleLaunchOption,
  getLaunchConfig,
} from '@/utils/init'
import { getHomeConfig } from '@/utils/getHomeConfig/index'
import { decode } from './utils/tools/common/index'
import { reportSnByScene } from './utils/taskSn'
import { toLogin } from './utils/helper/common/toLogin'
import { fetchLoginUserInfo } from './utils/helper/login/utils'
import { changeUserRole } from './utils/helper/member/communicate'
import { interruptAscribeFirmAuth } from './utils/firmAuth'

// 重写 wx.hideLoading 方法
if (wx.hideLoading) {
  // 保存原始的 hideLoading 函数引用（可能是代理函数）
  const originalHideLoading = wx.hideLoading
  // 使用Object.defineProperty重定义hideLoading属性
  Object.defineProperty(wx, 'hideLoading', {
    value(options?: any) {
      console.log('调用重写的 hideLoading 函数 - 通过Object.defineProperty', options)
      // 调用原始函数，确保 noConflict: true 被添加
      return originalHideLoading.call(wx, { noConflict: true, ...options })
    },
    writable: true,
    configurable: true,
  })
}

if (ENV_IS_SWAN || ENV_IS_TT) {
  wx.hideTabBar()
}

// 小程序初始化
try {
  getHomeConfig()
  init()
  setTimeout(() => {
    autoUpdate()
  }, 5000)
} catch (err) {
  // eslint-disable-next-line no-console
  console.log('初始化err', err)
}

let firstAppRoute = false

const webFPathArr = [
  'subpackage/member/firmAuth/index',
  'subpackage/member/firmAuthorization/index',
  'subpackage/member/firmCertificate/index',
  'subpackage/member/firmCompanyLicense/index',
  'subpackage/member/firmIdentityAuth/index',
  'subpackage/member/firmLegalAuth/index',
  'subpackage/member/firmLicense/index',
  'subpackage/member/firmProxyAuth/index',
  'subpackage/member/firmVariousVerify/index',
  'subpackage/member/webFirmAuth/index',
  'subpackage/member/realname/index',
  'subpackage/member/camera/index',
  'subpackage/common/watermark_camera/index',
  'subpackage/mp-ecard-sdk/index/index',
  'subpackage/web-view/index',
  'subpackage/certification/replenish/index',
  'subpackage/certification/submit/index',
  'subpackage/member/change-account/index',
  'subpackage/mp-ecard-sdk/protocol/service/index',
  'subpackage/mp-ecard-sdk/protocol/eid/index',
  'subpackage/member/unbindRealname/mandatory_certification/index',
  'subpackage/member/unbindRealname/unbind_one/index',
  'subpackage/member/unbindRealname/unbind_two/index',
  'subpackage/member/unbindRealname/unbind_three/index',
]

App(
  Provider({ store })({
    onLaunch(option) {
      console.log('onLaunch:', option)
      const { query } = option
      if (query.shareAgg == 'm9ttyl8jJXSNTQO' || query.scene === 'shareAgg%3Dm9ttyl8jJXSNTQO') {
        const isLogin = !query.isPostLogin && store.getState().storage.userState.login
        changeUserRole(1, isLogin)
        option.query = {
          track_seed: 'm9ttyl8jJXSNTQO',
          refid: '*********',
          url: '/company-authentication',
          isPostLogin: true,
        }
      }
      storage.setItemSync('APP_LAUNCH_TIMESTAMP', Date.now())
      // 角色问题重定向
      wx.onAppRoute((res) => {
        dispatch(actions.configActions.setState({ currentPages: { length: getCurrentPages().length, path: res.path } }))
        const { isWebFirmAuth } = store.getState().user.webTokenData
        // web端企业认证流程来源中断或完成流程后清除数据
        if (isWebFirmAuth && !webFPathArr.includes(res.path)) {
          dispatch(actions.userActions.setWebTokenData({
            isWebFirmAuth: false,
            token: '',
            authId: '',
            webFromData: {},
            shortToken: '',
          }))
          const { userId } = store.getState().storage.userState
          wx.$.collectEvent.config({ user_unique_id: userId || '' })
        }

        // 落地页归因企业认证流程中断跳出或者完成流程需要清空jobid，后续发布成功跳转不打开回显缓存job信息
        interruptAscribeFirmAuth(res.path)

        if (!firstAppRoute) {
          firstAppRoute = true
          return
        }
        // 如果是重定向到首页 判断一下角色，对应的端对不对，如果不对就重定向一下
        if (res.path === 'pages/index/index' && wx.$.u.getBC() == 1) {
          // dispatch(actions.storageActions.setItem({ key: 'userChooseRole', value: 1 }))
          wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' })
        }
      })
      if (ENV_IS_WEAPP) {
        // wujianjun 的分享关联埋点
        setTimeout(() => {
          const query = getQueryParam(option.query)
          const sharer_id = query.refid || query.refId || '-99999'
          if (!query.shareAgg) {
            collectShareLaunch({
              sharer_id,
              userAcq: query.userAcq || '-99999',
            })
          }
        }, 500)
        /** 获取配置 */
        getLaunchConfig()
        // 冷启动清除changeToolsPage（简历筛选项）
        dispatch(actions.storageActions.setItem({
          key: 'changeToolsPage', value: {},
        }))
        // 第一次进入小程序初始话简历列表职位tab
        dispatch(actions.storageActions.setItem({
          key: 'resumeTabInitInit',
          value: false,
        }))
        // 添加环境埋点上报 用来判断在正式环境把测试站的发上去了
        if (wx.getAccountInfoSync) {
          const accountInfo = wx.getAccountInfoSync()
          const { envVersion } = accountInfo.miniProgram
          if (envVersion == 'release') {
            wx.$.collectEvent.event('account_info', {
              envVersion,
            })
          }
        }
        // 清空分享弹窗数据
        dispatch(actions.storageActions.setItem({
          key: 'shareCardData',
          value: {
            isShareModal: false,
            title: '',
            img: '',
            path: '',
          },
        }))
      }
    },

    async onShow(option) {
      if (option.path !== 'pages/index/index') {
        firstAppRoute = true
      }
      const { query } = option
      if (query.shareAgg == 'm9ttyl8jJXSNTQO' || query.scene === 'shareAgg%3Dm9ttyl8jJXSNTQO') {
        option.query = {
          track_seed: 'm9ttyl8jJXSNTQO',
          refid: '*********',
          url: '/company-authentication',
          isPostLogin: true,
        }
      }
      // 处理启动时的参数
      handleLaunchOption(option)
      // 处理页面重定向逻辑
      const optionParams = await handleOnShowRedirect(option)
      wx.$.u.cache('app', 'show', true)
      buryingPoint(optionParams)
      // app跳转到小程序支付（工行路径）
      if (optionParams.query.payProductChannel == 'APP') {
        return
      }
      reportSnByScene(optionParams)
      // eslint-disable-next-line sonarjs/cognitive-complexity
      setTimeout(async () => {
        // 等待页面初始化完成
        const options = { ...optionParams }
        if (options.query.scene) {
          const scene = decodeURIComponent(options.query.scene)
          scene.split('&').forEach((item) => {
            const [key, value] = item.split('=')
            options.query[key] = value
          })
        }

        const { timLoginState } = store.getState().storage.common
        if (timLoginState == 2) {
          if (wx.$.l && wx.$.l.reTimLogin) {
            wx.$.l.reTimLogin()
          }
        } else {
          dispatch(actions.messageActions.getGlobalSwitch())
        }

        wx.$.l.getClassifyConfig()

        /** 获取二维码的链接 */
        if (options.query.q) {
          const scene = decodeURIComponent(options.query.q)
          scene.split('&').forEach((item) => {
            const [key, value] = item.split('=')
            options.query[key] = value
          })
        }
        const share_refid = decode(options.query.refid) || ''
        const c_refid = share_refid.indexOf('key')

        const { login, userId } = store.getState().storage.userState || {}
        if (options.query.isNeedLogin) {
          if (!login) {
            toLogin(true)
          } else if (String(userId) == share_refid.substring(0, c_refid)) {
            wx.$.r.reLaunch({ path: '/subpackage/member/getintegral/index' })
          }
        }
        if (login) {
          fetchLoginUserInfo()
        }
        /** 在启动的时候埋点 */
        if (options.query.share_page && options.query.share_path) {
          const reportObj = {
            share_details: options.query.is_image == 1 ? '图片分享' : '',
            share_page_name: options.query.share_page,
            share_page_path: options.query.share_path,
            share_type: options.query.is_image == 1 ? '' : '微信',
            track_id_1: share_refid.substring(0, c_refid),
          }

          wx.$.collectEvent.event('shareSourceOpenApplet', reportObj)
        }
        /** 触达中心触达效果数据回收-飞书6019166 */
        if (options.query.reach_msg_id) {
          wx.$.javafetch['POST/reach/v1/reach/report/reachResultRecoveryReport']({
            msgId: options.query.reach_msg_id, event: 'RECEIVE_CLICKED',
          }, { hideMsg: true })
        }
      })
    },
    onHide() {
      firstAppRoute = false
    },
    onPageNotFound() {
      wx.$.r.push({ path: '/pages/index/index' })
    },
  }),
)
