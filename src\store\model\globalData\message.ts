import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  getImChatList,
  sortSysImChatList,
} from './utils'
import storage from '../storage/storage'
import { messageQueue, store } from '@/store/index'
import { toJSON } from '@/utils/tools/formatter/index'

export const defaultState = {
  /** 站内信未读消息数量 */
  messageNumber: 0,
  /** 会员中心红点服务返回红点数 */
  uesrMessageNum: 0,
  /** 任务中心的任务数量 */
  taskCount: 0,
  /** tabbar招工红点是否显示 */
  recruitBadge: false,
  /** tabbar找活红点是否显示 */
  resumeBadge: false,
  /** tabber找活的免费数量 */
  resumeFreeBadge: 0,
  /** 消息红点是否显示 */
  msgBadge: 0,
  /** 系统消息单聊数据 */
  systemSingleChat: {} as any,
  /** 后端通过腾讯的groupId获取的列表数据 */
  myMsgGroup: [],
  /** 置顶群组数据 */
  myTopMsgGroup: [],
  /** 不合适，不感兴趣列表 */
  myDislikeMsgGroup: [],
  /** 通过接口获取的群组所有数据 key:群组ID */
  myMsgGroupOjb: {},
  /** 腾讯接口获取的群组所有数据 key:群组ID */
  myTenMsgGroupOjb: {},
  /** 判断是否已在新建群组数据 */
  groupHas: {},
  /** 是否正在执行sessionlist */
  isSessionList: false,
  /** 群组会话-IM入口开关 */
  imGlobalSwitch: null as any,
  /** 卡券的信息状态 */
  cardVoucherInfo: {
    /** 是否有新的卡券 */
    hasNewVoucher: false,
    /** 是否有新的卡券 */
    showKaoBao: false,
  },
  /** 判断是否已请求过IM会话列表 */
  isRequestIm: false,
  /** 判断是否已经请求过后端接口 */
  messageListRefresh: false,
  // 临时消息列表
  imChatList: [],
  // 不合适/不感兴趣的临时消息列表
  dislikeImChatList: [],
  /** 新招呼未读数量 */
  newConverNum: 0,
  /** 仅沟通未读数量 */
  onlyCommunicateNum: 0,
  /** 有交换未读数量 */
  alreadyExchangeTelNum: 0,
  /** IM是否已进行登录请求 */
  imlogin: false,
  /** 会员中心-红点父节点 */
  member_center: {},
  /** 消息-红点父节点 */
  information: {},
  /** 消息翻页最后一条消息 */
  nextReqMessageID: '',
  /** 不合适/不感兴趣的消息翻页最后一条消息 */
  dislikeNextReqMessageID: '',
  /** 回调消息,用于处理重复回调消息 */
  repeatMsg: {},
  /** im 包是否已加载 */
  isImPageOk: false,
  /** 判断当前微信小程序最低基础版本库是否大于等于2.4.0 */
  isSdkVer240: false,
  /** 判断是否在创建会话中 */
  isCreateSession: false,
  isSyncCompleted: false,
  /** 会话列表是否正在更新中 */
  isUpdateConversation: false,
  /** 会话是否在更新中 */
  updateConversationMsgIdList: [],
  updateConversationMsgId: '',
}

const { name, reducer, actions } = createSlice({
  name: 'message',
  initialState: { ...defaultState },
  reducers: {
    setState(state, { payload }: PayloadAction<Record<string, any>>) {
      Object.assign(state, payload)
    },
    setTaskCount(state, { payload }) {
      state.taskCount = payload
    },
    setMsgAdd(state, { payload }) {
      state.msgBadge += payload
    },
    setMsgBadgeAppBadge(state, { payload }) {
      state.msgBadge = payload
    },
    // 清楚会话列表相关的数据
    clearCoverData(state) {
      state.imChatList = []
      state.dislikeImChatList = []
      state.myMsgGroupOjb = {}
      state.myTenMsgGroupOjb = {}
      state.myMsgGroup = []
      state.myTopMsgGroup = []
      state.systemSingleChat = {}
      state.groupHas = {}
      state.nextReqMessageID = ''
      state.dislikeNextReqMessageID = ''
      state.isRequestIm = false
    },
    resetState(state) {
      Object.assign(state, { ...defaultState })
    },
  },
})

/**
 * @description IM消息为空(公共)
 */
const imDataListEmpty = (count) => async (dispatch) => {
  dispatch(actions.setMsgBadgeAppBadge(count))
  dispatch(
    actions.setState({
      myMsgGroup: [],
    }),
  )
}

/**
 * 退出会话页面时,清空未读
 * @param converId 会话id
*/
const outConvClearNum = (converId) => async (dispatch, getState) => {
  const { myMsgGroupOjb, msgBadge, myTopMsgGroup, myMsgGroup, imChatList, nextReqMessageID } = getState().message
  const oMyMsgGroupOjb = wx.$.u.isEmptyObject(myMsgGroupOjb) ? {} : myMsgGroupOjb
  const oGroup = { ...(oMyMsgGroupOjb[converId] || {}) }
  const { conversationID, finallyUnReadNumber = 0 } = oGroup
  if (conversationID) {
    const sData: any = {}

    if (!oGroup.finallyDesc) {
      const converRes = await wx.$.tim.getConversationList([conversationID])
      const conversationList = wx.$.u.deepClone(converRes.data.conversationList)
      let ngConversation
      if (wx.$.u.isArrayVal(conversationList)) {
        const conver = wx.$.u.deepClone(conversationList[0] || {})
        ngConversation = await wx.$.l.assConverPre(conver)
      }
      if (ngConversation) {
        const { conversationGroupList, finallyLabel, finallyDesc, timestamp, message_time, isPinned } = ngConversation
        oGroup.finallyLabel = finallyLabel
        oGroup.finallyDesc = finallyDesc
        oGroup.conversationGroupList = conversationGroupList
        oGroup.timestamp = timestamp
        oGroup.message_time = message_time
        oGroup.isPinned = isPinned
      }
    }

    // eslint-disable-next-line no-restricted-globals
    const nMsgBadge = msgBadge - (isNaN(finallyUnReadNumber) ? 0 : finallyUnReadNumber)
    sData.msgBadge = nMsgBadge >= 0 ? nMsgBadge : 0
    oGroup.finallyUnReadNumber = 0
    sData.myMsgGroupOjb = { ...oMyMsgGroupOjb, [oGroup.conversationID]: oGroup }
    if (oGroup.timestamp) {
      const sortItem = { conversationID, timestamp: Number(oGroup.timestamp) }
      if (oGroup.isPinned) {
        sData.myTopMsgGroup = await wx.$.l.sortMsgGroup(myTopMsgGroup, sortItem)
      } else {
        sData.myMsgGroup = await wx.$.l.sortMsgGroup(myMsgGroup, sortItem)
      }
      const { conversationGroupList } = oGroup || {}
      const oConversationGroupList = wx.$.u.isArrayVal(conversationGroupList) ? conversationGroupList : []
      const { ctab, poptab } = storage.getItemSync('imtyps') || {}
      if (ctab == 'ALL' || oConversationGroupList.includes(ctab) || (ctab == 'OTHER' && (oConversationGroupList.includes(poptab) || (poptab == 'UNREAD' && finallyUnReadNumber > 0)))) {
        const nImChatList = await wx.$.l.sortImChatList(sData.myMsgGroupOjb, imChatList, sortItem)
        sData.imChatList = nImChatList
        // 处理翻页位置
        if (nextReqMessageID == conversationID) {
          sData.nextReqMessageID = nImChatList[nImChatList.length - 1].conversationID
        }
      }
    }
    dispatch(actions.setState(sData))
  }
}

/**
 * 退出会话页面时,清空数量和部分数据变更
*/
const outConvChange = (lastMsg) => async (dispatch, getState) => {
  const { myMsgGroupOjb, myTopMsgGroup, myMsgGroup, msgBadge, imChatList, nextReqMessageID, myDislikeMsgGroup, dislikeImChatList } = getState().message
  const oMyMsgGroupOjb = wx.$.u.isEmptyObject(myMsgGroupOjb) ? {} : myMsgGroupOjb
  const oGroup = { ...(oMyMsgGroupOjb[lastMsg.conversationID] || {}) }
  const { conversationID, conversationGroupList, finallyUnReadNumber = 0 } = oGroup
  const oConversationGroupList = wx.$.u.isArrayVal(conversationGroupList) ? conversationGroupList : []
  if (conversationID) {
    const sData: any = {}
    // eslint-disable-next-line no-restricted-globals
    const nMsgBadge = msgBadge - (isNaN(finallyUnReadNumber) ? 0 : finallyUnReadNumber)
    sData.msgBadge = nMsgBadge >= 0 ? nMsgBadge : 0
    oGroup.finallyUnReadNumber = 0
    const { txt } = await wx.$.l.msgTypeTransformation(lastMsg)
    oGroup.finallyDesc = txt
    oGroup.message_time = await wx.$.l.dateTransformation(lastMsg.time)
    const { isRevoked, flow, isPeerRead, type, payload } = lastMsg || {}
    const { data } = payload || {}
    const { type: ptype } = data || {}
    const peerReadTypes = await wx.$.l.peerReadTypes()
    const peerReadPtypes = await wx.$.l.peerReadPtypes()
    if (!isRevoked && oConversationGroupList.includes('NEW_CONVERSATION') && flow == 'in') {
      oGroup.finallyLabel = '[新招呼]'
    } else if (!isRevoked && flow == 'out' && ((peerReadTypes || []).includes(type) || (peerReadPtypes || []).includes(`${ptype}`))) {
      oGroup.finallyLabel = isPeerRead ? '[已读]' : '[送达]'
    } else {
      oGroup.finallyLabel = ''
    }
    sData.myMsgGroupOjb = { ...oMyMsgGroupOjb, [oGroup.conversationID]: oGroup }
    const sortItem = { conversationID: lastMsg.conversationID, timestamp: Number(lastMsg.time) }
    if (oConversationGroupList.includes('MISMATCH')) {
      sData.myDislikeMsgGroup = await wx.$.l.sortMsgGroup(myDislikeMsgGroup, sortItem)
    } else if (oGroup.isPinned) {
      sData.myTopMsgGroup = await wx.$.l.sortMsgGroup(myTopMsgGroup, sortItem)
    } else {
      sData.myMsgGroup = await wx.$.l.sortMsgGroup(myMsgGroup, sortItem)
    }
    const { ctab, poptab } = storage.getItemSync('imtyps') || {}
    if (oConversationGroupList.includes('MISMATCH')) {
      if (wx.$.u.isArrayVal(dislikeImChatList)) {
        const nDislikeImChatList = await wx.$.l.sortMsgGroup(dislikeImChatList, sortItem)
        sData.dislikeImChatList = nDislikeImChatList
        sData.dislikeNextReqMessageID = (sData.dislikeImChatList[sData.dislikeImChatList.length - 1] || {}).conversationID || ''
      }
    } else if (ctab == 'ALL' || oConversationGroupList.includes(ctab) || (ctab == 'OTHER' && (oConversationGroupList.includes(poptab) || (poptab == 'UNREAD' && finallyUnReadNumber > 0)))) {
      const nImChatList = await wx.$.l.sortImChatList(sData.myMsgGroupOjb, imChatList, sortItem)
      sData.imChatList = nImChatList
      // 处理翻页位置
      if (nextReqMessageID == conversationID) {
        sData.nextReqMessageID = nImChatList[nImChatList.length - 1].conversationID
      }
    }
    dispatch(actions.setState(sData))
  }
}

/**
 * 收到消息回调 myMsgGroupOjb中的消息数量变化
 * @param isNo 是否需要重新获取群组信息 true:不需要 false:需要
 * @param conversation 会话信息
 * @param isNoAddNum 未读数量是否需要增加
*/
// eslint-disable-next-line sonarjs/cognitive-complexity
const changeMsgObjNum = (msg) => async (dispatch, getState) => {
  const { ID } = msg || {}

  await dispatch(actions.setState({ updateConversationMsgIdList: [...store.getState().message.updateConversationMsgIdList, ID] }))
  if (!store.getState().message.updateConversationMsgId) {
    await dispatch(actions.setState({ updateConversationMsgId: ID }))
  }
  await messageQueue((state) => {
    const { updateConversationMsgId, updateConversationMsgIdList } = state.message || {}
    return ID == updateConversationMsgId && updateConversationMsgIdList.includes(updateConversationMsgId)
  })

  setTimeout(() => {
    const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item !== ID)
    dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
  }, 5000)
  const { curCvsId } = getState().timmsg
  const { myMsgGroupOjb, myTenMsgGroupOjb, myTopMsgGroup, myMsgGroup,
    groupHas, imChatList, nextReqMessageID, myDislikeMsgGroup, dislikeImChatList } = getState().message
  const { flow, payload, isRevoked, isPeerRead, type, conversationID } = msg || {}
  const oMyMsgGroupOjb = wx.$.u.isEmptyObject(myMsgGroupOjb) ? {} : myMsgGroupOjb
  let gConversation = { ...(oMyMsgGroupOjb[conversationID] || {}) }

  if (groupHas[conversationID]) {
    const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item != ID)
    dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
    return
  }

  const { data } = payload || {}
  const nData = toJSON(data)
  const { type: nType, content, transmitType } = nData || {}
  const { subType } = content || {}
  if (['660.1', '610.1', '710.1'].includes(`${nType}`)) {
    if (subType == 4) {
      const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item != ID)
      dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
      return
    }
  } else if (transmitType == 2 || (nType == '390.1' && gConversation.conversationID)) {
    if (nType == 999999) {
      wx.$.l.handleTransmitType(msg)
    } else {
      const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item != ID)
      dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
    }
    return
  }
  let newGroup
  const sData: any = {}
  let sortMyMsgGroupOjb = {}
  const converRes = await wx.$.tim.getConversationList([conversationID])
  const conversationList = wx.$.u.deepClone(converRes.data.conversationList)
  let ngConversation
  if (wx.$.u.isArrayVal(conversationList)) {
    const conver = wx.$.u.deepClone(conversationList[0] || {})
    ngConversation = await wx.$.l.assConverPre(conver)
  }
  const sortItem = { conversationID, timestamp: Number(ngConversation.timestamp) }
  if (gConversation.conversationID) {
    if (curCvsId != gConversation.conversationID) {
      gConversation.finallyUnReadNumber = ngConversation.finallyUnReadNumber
    }
    // const { txt } = await wx.$.l.msgTypeTransformation(msg)
    gConversation.finallyLabel = ''
    gConversation.finallyDesc = ngConversation.finallyDesc
    const { conversationGroupList } = gConversation || {}

    const peerReadTypes = await wx.$.l.peerReadTypes()
    const peerReadPtypes = await wx.$.l.peerReadPtypes()
    if (flow == 'in' && wx.$.u.isArrayVal(conversationGroupList) && conversationGroupList.includes('NEW_CONVERSATION')) {
      gConversation.finallyLabel = '[新招呼]'
    } else if (!isRevoked && flow == 'out' && ((peerReadTypes || []).includes(type) || (peerReadPtypes || []).includes(`${nType}`))) {
      gConversation.finallyLabel = isPeerRead ? '[已读]' : '[送达]'
    }
    gConversation.message_time = await wx.$.l.dateTransformation(msg.time)
    sData.myMsgGroupOjb = { ...myMsgGroupOjb, [conversationID]: gConversation }
    newGroup = gConversation.isPinned ? [...myTopMsgGroup] : [...myMsgGroup]
    sortMyMsgGroupOjb = sData.myMsgGroupOjb || {}
  } else {
    dispatch(actions.setState({ groupHas: { ...groupHas, [msg.to]: 1 } }))
    if (!wx.$.u.isEmptyObject(ngConversation)) {
      gConversation = { ...ngConversation }
      const nMyTenMsgGroupOjb = { ...(myTenMsgGroupOjb || {}) }
      nMyTenMsgGroupOjb[conversationID] = gConversation
      await dispatch(actions.setState({ myTenMsgGroupOjb: nMyTenMsgGroupOjb }))
      await getImChatList([conversationID])
      const { myMsgGroupOjb } = store.getState().message
      newGroup = gConversation.isPinned ? [...myTopMsgGroup] : [...myMsgGroup]
      sortMyMsgGroupOjb = { ...myMsgGroupOjb }
    }
  }
  if (!sortMyMsgGroupOjb[conversationID]) {
    const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item !== ID)
    dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
    return
  }
  const { conversationGroupList, finallyUnReadNumber } = gConversation
  const oConversationGroupList = wx.$.u.isArrayVal(conversationGroupList) ? conversationGroupList : []

  if (oConversationGroupList.includes('MISMATCH')) {
    const nMyDislikeMsgGroup = [...(myDislikeMsgGroup || [])]
    const idx = nMyDislikeMsgGroup.findIndex(item => item.conversationID == conversationID)
    if (idx > 0) {
      nMyDislikeMsgGroup.splice(idx, 1)
      sData.myDislikeMsgGroup = [sortItem, ...nMyDislikeMsgGroup]
    }
  } else if (newGroup && newGroup.length > 0) {
    const idx = newGroup.findIndex(item => item.conversationID == conversationID)
    if (idx > 0) {
      newGroup.splice(idx, 1)
      sData[gConversation.isPinned ? 'myTopMsgGroup' : 'myMsgGroup'] = [sortItem, ...newGroup]
    }
  }
  const { ctab, poptab } = storage.getItemSync('imtyps') || {}
  if (oConversationGroupList.includes('MISMATCH')) {
    if (wx.$.u.isArrayVal(dislikeImChatList)) {
      const nDislikeImChatList = await wx.$.l.sortMsgGroup(dislikeImChatList, sortItem)
      sData.dislikeImChatList = nDislikeImChatList
      sData.dislikeNextReqMessageID = (sData.dislikeImChatList[sData.dislikeImChatList.length - 1] || {}).conversationID || ''
    }
  } else if (ctab == 'ALL' || oConversationGroupList.includes(ctab) || (ctab == 'OTHER' && (oConversationGroupList.includes(poptab) || (poptab == 'UNREAD' && finallyUnReadNumber > 0)))) {
    const nImChatList = await wx.$.l.sortImChatList(sortMyMsgGroupOjb, imChatList, sortItem)
    sData.imChatList = nImChatList
    // 处理翻页位置
    if (nextReqMessageID == conversationID && nImChatList.length) {
      sData.nextReqMessageID = nImChatList[nImChatList.length - 1].conversationID
    }
  }
  await dispatch(actions.setState(sData))
  const nPpdateConversationMsgIdList = store.getState().message.updateConversationMsgIdList.filter(item => item != ID)
  dispatch(actions.setState({ updateConversationMsgId: wx.$.u.isArrayVal(nPpdateConversationMsgIdList) ? nPpdateConversationMsgIdList[0] : '', updateConversationMsgIdList: nPpdateConversationMsgIdList }))
  setTimeout(() => {
    dispatch(fetchImMessageNumber())
  }, 200)
  // 新招呼分组比较慢，需要延后请求新招呼
  setTimeout(() => {
    dispatch(fetchMsgTabNum())
  }, 1000)
}

/**
 * 收到系统消息回调 myMsgGroupOjb中的消息数量变化
 * @param isNo 是否需要重新获取群组信息 true:不需要 false:需要
 * @param conversation 会话信息
*/
const changeMsgChageObjNum = (msg) => async (dispatch, getState) => {
  const { systemSingleChat, myMsgGroup, imChatList, nextReqMessageID, myMsgGroupOjb } = getState().message
  let oSystemSingleChat = { ...systemSingleChat }
  const { conversationID } = msg

  if (!systemSingleChat.conversationID) {
    oSystemSingleChat = {
      accountUserId: msg.from,
      conversationID,
      image: 'https://staticscdn.zgzpsjz.com/images/content/********/****************.png',
      title: '系统消息',
      uri: '/subpackage/member/system_info/index',
      jump_type: 1,
      show_number: 0,
      page_unique_index: 'system_message',
      desc: '系统消息',
      timestamp: Number(msg.time),
    }
  }
  const sortItem = { conversationID, timestamp: Number(msg.time), type: 'sys' }
  const nMyMsgGroup = await wx.$.l.sortMsgGroup(myMsgGroup, sortItem)
  const sMsgData: any = { myMsgGroup: nMyMsgGroup }
  if (imChatList.length > 0) {
    const { ctab } = storage.getItemSync('imtyps') || {}
    if (ctab == 'ALL') {
      const nImChatList = await sortSysImChatList(myMsgGroupOjb, imChatList, sortItem)
      sMsgData.imChatList = nImChatList
      // 处理翻页位置
      if (nextReqMessageID == conversationID && nImChatList.length) {
        sMsgData.nextReqMessageID = nImChatList[nImChatList.length - 1].conversationID
      }
    }
  }
  const nSystemSingleChat = await wx.$.l.setSystemMsgByCallBackMsg(oSystemSingleChat, msg)
  if (!nSystemSingleChat) {
    return
  }
  sMsgData.systemSingleChat = nSystemSingleChat
  dispatch(actions.setState(sMsgData))
  setTimeout(() => {
    dispatch(fetchMsgTabNum())
    dispatch(fetchImMessageNumber())
  }, 200)
}

/** 设置置顶 */
const setGroupTop = (conversationID, isPinned, callback?) => async (dispatch, getState) => {
  const { myMsgGroupOjb, myTopMsgGroup, myMsgGroup } = getState().message
  const oMyMsgGroupOjb = wx.$.u.isEmptyObject(myMsgGroupOjb) ? {} : myMsgGroupOjb
  const nItem = { ...oMyMsgGroupOjb[conversationID], isPinned }
  wx.$.tim.pinConversation({ conversationID: nItem.conversationID, isPinned }).then(async () => {
    let nMyMsgGroup = [...myMsgGroup]
    let nMyTopMsgGroup = [...myTopMsgGroup]
    let oItem: any = {}
    if (nItem.isPinned) {
      const idx = nMyMsgGroup.findIndex((item) => item.conversationID == conversationID)
      if (idx >= 0) {
        oItem = { ...nMyMsgGroup[idx] }
        nMyMsgGroup.splice(idx, 1)
        nMyTopMsgGroup = await wx.$.l.sortMsgGroup(nMyTopMsgGroup, oItem)
      }
    } else {
      const idx = nMyTopMsgGroup.findIndex((item) => item.conversationID == conversationID)
      if (idx >= 0) {
        oItem = { ...nMyTopMsgGroup[idx] }
        nMyTopMsgGroup.splice(idx, 1)
        nMyMsgGroup = await wx.$.l.sortMsgGroup(nMyMsgGroup, oItem)
      }
    }
    const sData = {
      myMsgGroup: nMyMsgGroup,
      myTopMsgGroup: nMyTopMsgGroup,
      myMsgGroupOjb: { ...oMyMsgGroupOjb, [conversationID]: nItem },
    }
    await dispatch(actions.setState(sData))
    if (nItem.isPinned) {
      wx.$.msg('已置顶')
    } else {
      wx.$.msg('已取消置顶')
    }
    callback?.success && callback?.success()
  }).catch((err) => {
    callback?.fail && callback?.fail(err)
  })
}

/** 获取tab未读数量  */
const fetchMsgTabNum = () => async (dispatch) => {
  /** 获取新招呼未读数量  */
  dispatch(fetchConverCount('NEW_CONVERSATION', 'newConverNum'))
  /** 获取仅沟通未读数量  */
  dispatch(fetchConverCount('ONLY_COMMUNICATE', 'onlyCommunicateNum'))
  /** 获取有交换未读数量  */
  dispatch(fetchConverCount('ALREADY_EXCHANGE_TEL', 'alreadyExchangeTelNum'))
}
/** 
 * @description  获取未读数量  
 * @param {String} groupName  分组名称
 * @param {String} sType  存储的key
 * */
const fetchConverCount = (groupName, sType, simtyps = false) => async (dispatch) => {
  const isReady = wx.$.tim && wx.$.tim.isReady()
  if (!isReady) {
    return
  }
  const imResponse = await wx.$.tim.getConversationList({ groupName })
  const { data } = imResponse || {}
  const { conversationList } = data || {}
  let nConversationList: Array<any> = wx.$.u.deepClone(conversationList)
  if (groupName == 'ALREADY_EXCHANGE_TEL') {
    const wxImResponese = await wx.$.tim.getConversationList({ groupName: 'ALREADY_EXCHANGE_WEIXIN' })
    nConversationList = nConversationList.concat(wx.$.u.deepClone(wxImResponese.data.conversationList))
    const fileImResponese = await wx.$.tim.getConversationList({ groupName: 'ALREADY_EXCHANGE_RESUME_FILE' })
    nConversationList = nConversationList.concat(wx.$.u.deepClone(fileImResponese.data.conversationList))
  }
  let ctab = 'ALL'
  if (nConversationList && nConversationList.length > 0) {
    let num = 0
    nConversationList.forEach((item: any) => {
      if (item.unreadCount > 0 && item.messageRemindType != 'AcceptNotNotify' && item.messageRemindType != 'Discard') {
        num++
      }
    })
    if (num > 0) {
      ctab = 'ONLY_COMMUNICATE'
    }
    dispatch(actions.setState({ [sType]: num }))
  } else {
    dispatch(actions.setState({ [sType]: 0 }))
  }

  const imtyps = storage.getItemSync('imtyps')
  const { first } = imtyps || {}
  if (simtyps && groupName == 'ONLY_COMMUNICATE' && first) {
    storage.setItemSync('imtyps', { ...imtyps, ctab, first: false })
  }
}

/**
 * @description 获取会话未读消息总数
 * */
const fetchImMessageNumber = () => async (dispatch) => {
  try {
    const isReady = wx.$.tim && wx.$.tim.isReady()
    if (!isReady) {
      return
    }
    const msgBadge = wx.$.tim.getTotalUnreadMessageCount()
    dispatch(actions.setState({ msgBadge }))
  } catch (err) {
    // console.log('err', err)
  }
}

/**
 * @description 清楚所有会话的未读数量
 * */
const clearImMessageUnReadNumber = () => async (dispatch, getState) => {
  const { myMsgGroupOjb, msgBadge } = getState().message
  let num = 0
  Object.keys(myMsgGroupOjb || {}).forEach((key) => {
    const item = myMsgGroupOjb[key]
    num += item.finallyUnReadNumber
  }, {})
  if (msgBadge == 0 && num == 0) {
    wx.$.msg('暂无未读消息')
    return
  }

  wx.$.tim.setAllMessageRead().then(async () => {
    const { myMsgGroupOjb, systemSingleChat } = getState().message
    const nSystemChat = {
      ...systemSingleChat,
      show_number: 0,
    }

    const nObj = Object.keys(myMsgGroupOjb || {}).reduce((acc: any, key) => {
      // eslint-disable-next-line no-param-reassign
      acc[key] = { ...myMsgGroupOjb[key], finallyUnReadNumber: 0 }
      return acc
    }, {})
    await dispatch(actions.setState({ systemSingleChat: nSystemChat, myMsgGroupOjb: nObj, msgBadge: 0 }))
    wx.$.msg('已标记所有未读消息为已读')
  })
}

/**
 * @description 在tabber切换就需要请求消息数据
 * @param userId 当前登录用户的id
 */
const fetchTabbarMyMessageNumber = () => async (dispatch, getState) => {
  const { login } = getState().storage.userState
  if (!login || !ENV_IS_WEAPP) {
    // 没有登录直接返回空对象
    return {}
  }
  const res = await wx.$.javafetch['POST/cms/reddot/v1/redDot/queryNodesRedNum']({ funcCodes: ['member_center', 'information'] })
  const { list } = res.data || { list: [] }
  const sData: any = {}
  if (list) {
    list.forEach(it => {
      const nit = { ...it }
      nit.show = (nit.showWay == 1 && nit.number > 0) || (nit.showWay == 3 && nit.number > 0)
      nit.badgeType = nit.showWay == 1 ? 'dot' : 'count'
      if (nit.showWay == 1) {
        nit.number = 0
      }
      sData[nit.funcCode] = nit
    })
  }

  dispatch(actions.setState({ ...sData }))
  return { ...defaultState }
}

const getGlobalSwitch = () => async (dispatch, getState) => {
  const info = wx.getSystemInfoSync()
  const { SDKVersion } = info || {}
  dispatch(actions.setState({ isSdkVer240: compareVersion(SDKVersion, '2.4.0') >= 0 }))

  const { login } = getState().storage.userState
  if (login) {
    const res = await wx.$.javafetch['POST/reach/v2/im/config/query']()
    const { code, data } = res || {}
    if (code == 0) {
      const { systemAccountList, toolbarIcon, b2cSayHelloAgainContent, maxMessageWordCountLimit, sendPhoneNumberInterceptSwitch, phoneNumberRegularExpression } = data || {}
      dispatch(actions.setState({ imGlobalSwitch: { accountList: systemAccountList, toolbarIcon, b2cSayHelloAgainContent, maxMessageWordCountLimit: maxMessageWordCountLimit || 300, sendPhoneNumberInterceptSwitch, phoneNumberRegularExpression } }))
    }
  }
}
/**
 * 比较版本号大小,格式xx.xx.xx
 * 输出 -1，表示 version1 < version2
 * 输出 1，表示 version1 >version2
 * 输出 0，表示相等
 * */
function compareVersion(version1, version2) {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)

  // 补齐短的版本号为三位
  while (v1Parts.length < 3) v1Parts.push(0)
  while (v2Parts.length < 3) v2Parts.push(0)

  // 比较每一部分
  for (let i = 0; i < 3; i++) {
    if (v1Parts[i] > v2Parts[i]) return 1
    if (v1Parts[i] < v2Parts[i]) return -1
  }

  // 如果所有部分都相等，返回 0
  return 0
}

export const messageName = name
export const messageReducer = reducer
export const messageActions = {
  outConvClearNum,
  fetchTabbarMyMessageNumber,
  imDataListEmpty,
  changeMsgObjNum,
  changeMsgChageObjNum,
  outConvChange,
  setGroupTop,
  getGlobalSwitch,
  fetchImMessageNumber,
  fetchMsgTabNum,
  clearImMessageUnReadNumber,
  fetchConverCount,
  ...actions,
}
