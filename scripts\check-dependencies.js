const { exec } = require('child_process')
const fs = require('fs').promises
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
}

// 严重性级别映射
const severityLevels = {
  good: { color: 'green', icon: '✔', priority: 1 },
  low: { color: 'indigo', icon: '▪', priority: 2 },
  moderate: { color: 'orange', icon: '⚠', priority: 3 },
  high: { color: 'purple', icon: '⚠', priority: 4 },
  critical: { color: 'red', icon: '✖', priority: 5 },
}

// 配置选项
const config = {
  packageManager: 'pnpm', // 可以改为 'npm', 'yarn'
  registry: 'https://registry.npmjs.org/',
  timeout: 20000, // 60秒超时
  retries: 3,
  alternativeRegistries: [
    'https://registry.npmmirror.com/',
    'https://registry.yarnpkg.com/',
  ],
}

// 检测包管理器
async function detectPackageManager() {
  try {
    await fs.access('pnpm-lock.yaml')
    return 'pnpm'
  } catch { }

  try {
    await fs.access('yarn.lock')
    return 'yarn'
  } catch { }

  try {
    await fs.access('package-lock.json')
    return 'npm'
  } catch { }

  // 默认返回 pnpm
  return 'pnpm'
}

/** 执行带超时的命令 */
function execWithTimeout(command, timeout = 20000) {
  return new Promise((resolve, reject) => {
    const child = exec(command, {
      maxBuffer: 1024 * 1024 * 10,
    }, (error, stdout, stderr) => {
      if (timer) clearTimeout(timer)
      if (error) {
        error.stdout = command.includes('yarn') ? disposeYarnStdout(stdout) : stdout
        error.stderr = stderr
        reject(error)
      } else {
        resolve({ stdout, stderr })
      }
    })

    const timer = setTimeout(() => {
      child.kill()
      reject(new Error(`Command timed out after ${timeout}ms: ${command}`))
    }, timeout)
  })
}

// 分析网络超时错误
function analyzeTimeoutError(error) {
  const errorStr = error.toString()
  if (errorStr.includes('ETIMEDOUT') || errorStr.includes('timeout')) {
    return true
  }
  return false
}

// 执行 audit 检查
async function checkDependencies(packageManager) {
  console.log(`${colors.blue}${colors.bold}═══════════════════════════════════════════════════${colors.reset}`)
  console.log(`${colors.blue}${colors.bold}     依赖安全检查工具 - Package Audit Analyzer${colors.reset}`)

  // 检测包管理器
  if (!packageManager) {
    packageManager = await detectPackageManager()
  }
  console.log(`${colors.cyan}检测到包管理器: ${colors.bold}${packageManager}${colors.reset}`)
  console.log(`${colors.cyan}正在执行安全审计...${colors.reset}\n`)

  let command
  switch (packageManager) {
    case 'pnpm':
      command = `pnpm audit --registry ${config.registry} --prod --json`
      break
    case 'yarn':
      command = `yarn audit --registry ${config.registry} --prod --json`
      break
    case 'npm':
      command = `npm audit --registry ${config.registry} --prod --json`
      break
    default:
      command = `pnpm audit --registry ${config.registry} --prod --json`
  }

  try {
    const { stdout, stderr } = await execWithTimeout(command, config.timeout)

    // 尝试解析 JSON 输出
    if (stdout) {
      try {
        const auditData = JSON.parse(stdout)
        const result = analyzeAuditResults(auditData, packageManager)
        return { type: 'json', value: result }
      } catch (parseError) {
        console.log(`\n${colors.yellow}审计输出不是 JSON 格式，显示原始输出:${colors.reset}\n`)
        return { type: 'string', value: stdout }
      }
    }
  } catch (error) {
    // pnpm audit 在有严重漏洞时会返回非零退出码

    if (error.stdout) {
      try {
        const auditData = JSON.parse(error.stdout)
        const result = analyzeAuditResults(auditData, packageManager)
        return { type: 'json', value: result }
      } catch (parseError) {
        // 检查是否是超时错误
        if (analyzeTimeoutError(error)) {
          return { type: 'error', value: error.stdout, message: `${parseError.message}\n ${error.message}` }
        }
        return { type: 'string', value: error.stdout }
      }
    } else {
      // 检查是否是超时错误
      if (analyzeTimeoutError(error)) {
        return { type: 'error', value: error.stdout, message: `${error.message}` }
      }
      return { type: 'string', value: error.stdout }
    }
  }
}

// 分析  audit 结果
function analyzeAuditResults(auditData, packageManager) {
  const tAuditData = disposeData(auditData, packageManager)
  return analyzePnpmAdvisories(tAuditData)
}

// 统一数据格式
function disposeData(auditData, packageManager) {
  if (packageManager == 'npm') return disposeNpm(auditData)
  if (packageManager == 'yarn') return disposeYarn(auditData)
  return auditData
}

// 处理yarn stdout
function disposeYarnStdout(stdout) {
  const res = stdout.trim().split('\n')?.map(v => JSON.parse(v))
  return res?.length ? JSON.stringify(res) : ''
}

// 处理yarn 格式
function disposeYarn(auditData) {
  const advisories = auditData.filter(v => v.type === 'auditAdvisory').reduce((p, n) => {
    const { data } = n
    const { advisory } = data
    return {
      ...p,
      ...{
        [advisory.id]: {
          title: advisory?.title,
          severity: advisory.severity,
          module_name: advisory.module_name,
          recommendation: advisory.recommendation || `更新 ${advisory.module_name} 到 ${advisory?.patched_versions || '最新版本'}`,
          url: advisory.references,
          vulnerable_versions: advisory.vulnerable_versions,
          patched_versions: advisory.patched_versions,
          cves: advisory.cves,
        },
      },
    }
  }, {})
  const metadata = auditData?.find(v => v.type === 'auditSummary')?.data || {}
  return { metadata, advisories }
}
// 处理npm数据格式
function disposeNpm(auditData) {
  const advisories = Object.values(auditData.vulnerabilities).reduce((p, n) => {
    const { via } = n
    const target = via?.[0] || {}
    return {
      ...p,
      ...{
        [via.source]: {
          title: target?.title,
          severity: n.severity,
          module_name: n.name,
          recommendation: `更新 ${n.name} 到 ${n?.fixAvailable?.version || '最新版本'}`,
          url: target.url,
          vulnerable_versions: target.range,
          patched_versions: n.fixAvailable.version,
          cves: target.cves,
        },
      },
    }
  }, {})
  return { metadata: auditData.metadata, advisories }
}
// // 分析 pnpm advisories 格式 - 返回简化的结构化数据
function analyzePnpmAdvisories(auditData) {
  const { advisories } = auditData

  // 初始化简化的结果对象
  const result = {
    summary: {
      total: 0,
      critical: 0,
      high: 0,
      moderate: 0,
      low: 0,
    },
    vulnerabilities: [],
  }

  // 处理 advisories
  if (advisories) {
    Object.values(advisories).forEach(advisory => {
      const severity = advisory.severity.toLowerCase()
      result.summary[severity] = (result.summary[severity] || 0) + 1
      result.summary.total++
      // 只保留关键信息
      const vulnerability = {
        severity,
        packageName: advisory.module_name,
        recommendation: advisory.recommendation || `更新 ${advisory.module_name} 到 ${advisory.patched_versions || '最新版本'}`,
        referenceUrl: advisory.url || advisory.references || '',
        vulnerableVersions: advisory.vulnerable_versions,
        patchedVersions: advisory.patched_versions,
        cves: advisory.cves || [],
        title: advisory?.title,
      }

      result.vulnerabilities.push(vulnerability)
    })
  }

  // 按严重性排序漏洞
  result.vulnerabilities.sort((a, b) => {
    const aSeverity = severityLevels[a.severity].priority
    const bSeverity = severityLevels[b.severity].priority
    return bSeverity - aSeverity
  })

  // // 生成建议
  result.recommendations = generateRecommendations(result.summary, 'pnpm')

  return result
}

// 生成建议
function generateRecommendations(summary, packageManager) {
  if (summary.total === 0) return {
    type: 'good',
    message: '未发现安全漏洞，依赖安全状态良好',
    priority: 0,
  }

  // 根据严重级别生成建议
  if (summary.critical > 0) return {
    type: 'critical',
    message: `发现 ${summary.critical} 个严重漏洞，需要立即修复！`,
    priority: 5,
  }

  if (summary.high > 0) return {
    type: 'high',
    message: `发现 ${summary.high} 个高危漏洞，建议尽快修复`,
    priority: 4,
  }

  if (summary.moderate > 0) return {
    type: 'moderate',
    message: `发现 ${summary.moderate} 个中等漏洞，建议计划修复`,
    priority: 3,
  }

  // 添加通用建议
  return {
    type: 'low',
    message: '定期运行审计命令检查依赖安全性',
    priority: 1,
  }
}

const request = async (url, body, headers = {}, method = 'POST') => {
  return new Promise(async (r, j) => {
    const params = {
      method, headers: { 'Content-Type': 'application/json; charset=utf-8', ...headers },
    }
    if (method == 'POST') params.body = JSON.stringify(body)
    try {
      const response = await fetch(url, params)
      response.json().then((d) => r(d)).catch((e) => j(e))
    } catch (e) {
      j(e)
    }
  })
}
const notify = async (report, message) => {
  request('https://open.feishu.cn/open-apis/bot/v2/hook/716336f7-e7db-4619-908b-16f084aad0ab', { msg_type: 'interactive', card: message }).then((d) => {
    if (d.code != 0) console.error(`通知失败:${JSON.stringify(d)}`)
  }).catch(e => console.error(`通知失败err:${JSON.stringify(e)}`))
}

const getTemplate = async (data, depository) => {
  const { value: report, type } = data

  const vulnerabilitiesImportant = report.vulnerabilities.filter(v => ['high', 'critical'].includes(v.severity))

  if (!vulnerabilitiesImportant?.length) return false
  return {
    schema: '2.0',
    config: {
      update_multi: true,
      locales: [
        'en_us',
        'ja_jp',
      ],
      style: {
        text_size: {
          normal_v2: {
            default: 'normal',
            pc: 'normal',
            mobile: 'heading',
          },
        },
      },
    },
    card_link: {
      url: '',
    },
    body: {
      direction: 'vertical',
      padding: '12px 12px 12px 12px',
      elements: type == 'json' ? [
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `总计：${report.summary.total}个风险；严重：${report.summary.critical}个；高危：${report.summary.high}个；中等：${report.summary.moderate}个；低危：${report.summary.low}个`,
            text_size: 'normal_v2',
            text_align: 'left',
            text_color: severityLevels?.[report.recommendations?.type]?.color || 'red',
          },
          margin: '0px 0px 0px 0px',
        },
        {
          tag: 'hr',
          margin: '0px 0px 0px 0px',
        },
        ...vulnerabilitiesImportant.map(v => {
          return {
            tag: 'div',
            text: {
              tag: 'plain_text',
              content: `包名：${v.packageName}\n等级：${v.severity}\n漏洞：${v.title}\n建议：${v.recommendation}\n参考链接：${v.referenceUrl}\nCVE：${v.cves}\n影响版本：${v.vulnerableVersions}\n补丁版本：${v.patchedVersions}`,
              text_size: 'notation',
              text_align: 'left',
              text_color: 'default',
            },
            icon: {
              tag: 'standard_icon',
              token: 'buzz_outlined',
              color: 'orange',
            },
            margin: '0px 0px 0px 0px',
          }
        }),
      ] : [
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `解析异常-原始输出：${report}`,
            text_size: 'normal_v2',
            text_align: 'left',
            text_color: 'red',
          },
          margin: '0px 0px 0px 0px',
        },
      ],
    },
    header: {
      title: {
        tag: 'plain_text',
        content: `等级：${report.recommendations.type} |  提示：${report.recommendations.message}`,
        i18n_content: {
          en_us: 'You Have an Order Pending Confirmation',
          ja_jp: '注文確認待ち',
        },
      },
      subtitle: {
        tag: 'plain_text',
        content: `仓库：${depository}`,
      },
      template: severityLevels?.[report.recommendations?.type]?.color || 'orange',
      padding: '12px 12px 12px 12px',
    },
  }
}
// 读取 package.json 中的包名
async function getPackageName() {
  try {
    const packageJsonPath = path.join(process.cwd(), 'package.json')
    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf8')
    const packageData = JSON.parse(packageJsonContent)

    if (!packageData.name) {
      return '未捕获仓库名'
    }
    return packageData.name
  } catch (error) {
    return '未捕获仓库名'
  }
}

const increaseSheet = async (data, depository) => {
  const { value: report, type } = data
  try {
    const { tenant_access_token } = await request('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
      app_id: 'cli_a73c31ee693d1013',
      app_secret: 'lyQBAVSgH8mFIme3wjILqbOem4Lasyg7',
    })
    const sheetToken = 'ACDOsugKqhzPTytaBapc5DNznTh'
    const Authorization = `Bearer ${tenant_access_token}`
    const { data } = await request(`https://open.feishu.cn/open-apis/sheets/v3/spreadsheets/${sheetToken}/sheets/query`, {}, { Authorization }, 'GET')
    const sheetId = data.sheets?.[0]?.sheet_id
    const increaseLine = await request(`https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/${sheetToken}/dimension_range`, {
      dimension: {
        sheetId,
        majorDimension: 'ROWS',
        length: 20,
      },
    }, { Authorization })

    let values = []
    if (type == 'string') {
      values = [depository, `异常上报：${report}`]
    } else {
      values = report.vulnerabilities.map(v => [depository, v.packageName, v.severity, v.title, v.recommendation, v.referenceUrl, v.cves?.join(','), v.vulnerableVersions, v.patchedVersions])
    }

    const increaseData = await request(`https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/${sheetToken}/values_append`, {
      valueRange: {
        range: sheetId,
        values,
      },
    }, { Authorization })

    console.log(`${colors.blue}${colors.bold}上报成功-----------------------${colors.reset}\n`)
  } catch (e) {
    console.error(`表格上报失败:${e}`)
  }
}

async function main() {
  try {
    const report = await checkDependencies(process.env.PACKAGE_MANAGER)
    const depository = await getPackageName()
    // 获取机器人卡片模板
    const message = await getTemplate(report, depository)
    // 增加表格数据
    increaseSheet(report, depository)
    // 发送机器人卡片
    if (message) notify(report, message)

    console.log(`${colors.green}检查完成！最高类型：${report.value?.recommendations?.type}- ${report.value?.recommendations?.message} ${colors.reset}`)
    console.log(`${colors.blue}${colors.bold}═══════════════════════════════════════════════════${colors.reset}\n`)
  } catch (error) {
    console.error(`${colors.red}执行过程中发生错误:${colors.reset}`, error)
    // process.exit(1);
  }
}

main()
