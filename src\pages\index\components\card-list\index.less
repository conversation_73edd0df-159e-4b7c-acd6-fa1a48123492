.body {
  padding-bottom: 120rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 16rpx;
  padding-bottom: 32rpx;
  color: gray;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.empty {
  padding-top: 8vh;
  padding-bottom: 8vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  &.struct {
    flex-direction: column;
    padding-top: 64rpx;
  }
}

.refresh-message {
  padding-bottom: 16rpx;
  color: #999;
  font-size: 27rpx;
  text-align: center;
  margin: 0 24rpx;
}

.no-data-img {
  width: 200rpx !important;
  height: 200rpx !important;
}

.struct-filter-tag {
  box-sizing: border-box;
  width: 100%;
  padding: 0 24rpx;
  margin-top: 64rpx;
}

.force-insert{
  animation: force-insert 300ms cubic-bezier(.42,0,.58,1);
}

@keyframes force-insert {
  0% {
    opacity: .7;
    max-height: 0;
  }
  100% {
    opacity: 1;
    max-height: 500px;
  }
}

.slide-down {
  animation: slide-down 0.4s cubic-bezier(.42,0,.58,1);
}

@keyframes slide-down {
  0% {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

