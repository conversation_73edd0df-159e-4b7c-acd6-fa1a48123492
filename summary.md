# Fast Publish Second 集成 City-Promote 组件总结

## 概述
基于 fast_publish_first 中已有的 city-promote 集成案例，成功在 fast_publish_second 组件中实现了相同的推广城市功能集成。

## 更改文件列表
- `src/subpackage/recruit/fast_issue/index/components/fast_publish_second/index.wxml`
- `src/subpackage/recruit/fast_issue/index/components/fast_publish_second/index.ts`
- `src/subpackage/recruit/fast_issue/index/components/fast_publish_second/index.json`

## 详细更改内容

### 1. WXML 文件更改 (`index.wxml`)

**添加位置：** 在表单区域的 `</m-form>` 标签后

```xml
<view class="form-item">
  <city-promote 
    value="{{promoteCities}}" 
    wx:if="{{promoteCityVisible}}" 
    publishCity="{{currentAddress}}" 
    bind:change="onPromoteCityChange">
  </city-promote>
</view>
```

**功能说明：**
- `value`: 绑定当前选择的推广城市数组
- `wx:if`: 根据配置动态显示/隐藏组件
- `publishCity`: 传入当前发布城市信息，用于校验推广城市与发布城市的一致性
- `bind:change`: 绑定推广城市选择变化的回调方法

### 2. TypeScript 文件更改 (`index.ts`)

#### 2.1 数据初始化
在 `setInitData()` 方法中添加：

```typescript
/** 推广城市 */
promoteCities: [],
/** 是否展示推广城市 */
promoteCityVisible: false,
/** 当前地址信息 */
currentAddress: <any>{},
```

#### 2.2 推广城市变化处理方法
新增方法：

```typescript
/** 推广城市变化处理 */
onPromoteCityChange(e) {
  const { value } = e.detail
  this.setData({
    promoteCities: value || [],
  })
}
```

#### 2.3 地址选择更新
修改 `changeCityLocation` 方法，添加 `currentAddress` 更新：

```typescript
this.setData({
  shortAliasAddressStr: `${addressStr}招`,
  currentAddress: value, // 新增
})
```

#### 2.4 地址初始化更新
修改 `initAddress` 方法，在成功获取地址后设置推广城市相关状态：

```typescript
this.setData({
  firstArea: showAddress,
  shortAliasAddressStr: `${addressStr}招`,
  currentAddress: showAddress,     // 新增
  promoteCityVisible: true,        // 新增
})
```

#### 2.5 GPS 定位更新
修改 `onGetGPS` 方法，同样添加推广城市状态设置：

```typescript
this.setData({
  firstArea: showAddress,
  shortAliasAddressStr: `${addressStr}招`,
  currentAddress: showAddress,     // 新增
  promoteCityVisible: true,        // 新增
})
```

#### 2.6 模板刷新逻辑
修改 `refreshTemp` 方法，添加推广城市显示控制逻辑：

```typescript
const promoteCityVisible = wx.$.u.getObjVal(
  templates,
  'promoteCity.status',
  false,
)

this.setData({
  templates,
  showPhone: isValidPhone || telTempUsable || showPhone,
  templateSource: source,
  promoteCityVisible,                                           // 新增
  promoteCities: promoteCityVisible ? this.data.promoteCities : [], // 新增
})
```

### 3. JSON 配置文件更改 (`index.json`)

在 `usingComponents` 中添加 city-promote 组件引用：

```json
{
  "usingComponents": {
    // ... 其他组件
    "city-promote": "../../components/city-promote/index"
  }
}
```

## 功能特性

### 1. 动态显示控制
- 根据选择的工种和后端模板配置动态显示/隐藏推广城市组件
- 通过 `refreshTemp` 方法实现工种变化时的自动更新

### 2. 数据一致性
- 推广城市与发布城市的一致性校验
- 当推广城市与发布城市不一致时，会弹出确认提示

### 3. 数据传递
- 推广城市数据通过 `onSubmit` 方法的 `this.triggerEvent('submit', { ...this.data })` 传递给父组件
- 确保推广城市信息能够正确提交到后端

### 4. 用户体验
- 单选模式，用户只能选择一个推广城市
- 禁用港澳台地区选择
- 提供友好的确认弹窗提示

## 与 fast_publish_first 的一致性

本次集成完全参考了 fast_publish_first 中的实现方式，确保了：

1. **相同的组件配置**：使用相同的属性和事件绑定
2. **一致的数据结构**：推广城市相关的数据字段命名和类型保持一致
3. **相同的业务逻辑**：推广城市显示控制、数据校验等逻辑保持一致
4. **统一的用户体验**：两个组件中的推广城市功能表现完全一致

## 测试建议

1. **功能测试**
   - 验证推广城市组件在不同工种选择下的显示/隐藏
   - 测试推广城市选择和数据保存
   - 验证推广城市与发布城市一致性校验

2. **集成测试**
   - 确认推广城市数据能正确传递到父组件
   - 验证表单提交时推广城市数据的完整性

3. **用户体验测试**
   - 测试推广城市选择的交互流程
   - 验证确认弹窗的显示和操作

## 总结

本次集成成功将 city-promote 组件完整地集成到了 fast_publish_second 中，实现了与 fast_publish_first 完全一致的推广城市功能。所有必要的数据字段、方法和配置都已正确添加，确保了功能的完整性和一致性。