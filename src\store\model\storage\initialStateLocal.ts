/** 不和model关联的本地缓存 */

import { post_resume1Def } from './defData'

export default {
  /** 工种所有的数据 */
  allOccData: {
    /** 招工筛选排序的数据集合 */
    jos_list_types: [],
    /** 找活筛选排序的数据集合 */
    resume_list_types: [],
    /** 工厂的工种数据集 */
    occupations_factory: [],
    /** 工厂找活筛选类型数据集 */
    staff_tree: [],
    // 工厂热门工种
    factoryHotClassify: {},
    // 工种数据
    occupations: [],
    // 工种数据版本号
    occupations_v: null,
    // 兼职工种数据
    partoccupations: [],
    // 兼职工种数据版本号
    partoccupations_v: null,
    // 物流专区数据源
    logistics_factory: [],
  },

  // !浏览记录通用
  /** 用于标题置灰已查看水印显示 */
  cardViewedHistory: {},
  /** 城市数据列表 */
  areaTree: [],
  /** 是否勾选实名认证&人脸协议状态 */
  agreementCheck: false,
  // 是否首次拨打电话  招工详情recruit 找活详情resume 找活详情免费拨打freeresume
  isFirstCallPhone: {
    recruit: true,
    resume: true,
    freeresume: true,
  },
  /** 人脸认证成功 */
  faceSuccess: false,
  // 是否首次进入im会话页面
  isNoFirstImConversation: null, // 存储积分分享弹窗或口碑分享弹窗弹出类型,1:积分分享弹窗  2:口碑分享弹窗
  integralShareType: {
    // 弹窗弹出类型,1:积分分享弹窗  2:口碑分享弹窗
    type: 0,
    // 时间
    time: 0,
    // 是否需要显示积分分享弹窗或口碑分享弹窗
    isShow: false,
    // 积分弹框配置
    integral: { num: 0 },
    // 口碑弹框配置
    praise: { num: 0 },
  },
  /** 是否应该再次拉取cdn的高德地址 */
  isRefreshCDN: false,
  // 安全核验-招工-在用户面前-当前名称的弹窗-展示的总次数
  ypCheckPopRecruitShowNumber: {
    index: 0,
    factory: 0,
    logitics: 0,
  },
  // 安全核验-找活-在用户面前-当前名称的弹窗-展示的总次数
  ypCheckPopResumeShowNumber: {
    index: 0,
    factory: 0,
    logitics: 0,
  },
  // 安全核验弹窗在用户面前展示的总次数
  ypCheckPopShowNumber: 0,
  // 物流列表工种导航栏 - Delete
  /* logisticsMenc: {
    recruit: [],
    resume: [],
  }, */
  // 联想词传参
  words_list: [],
  /** 每日首次进入找活大列表时间存储 - Delete */
  // firstEntryResumeDate: 0,
  /** 大转盘配置信息 */
  showTurntableInfo: {
    show_turntable: 0,
    is_turntable: 0,
    // 是否有存储大转盘数据
    isSave: false,
  },
  /**
   * 鱼泡快招小程序的跳转到找活大列表和工厂找活大列表携带的参数缓存，（成功发布招工需要存储）, 不挂载到model上
   * - Delete
   * */
  // quickTrickParams: {
  //   /** 城市id */
  //   area_id: '',
  //   /** 工种id，以逗号分割 */
  //   classify_id: '',
  // },
  // 是否从工种筛选器里选择的工种标签
  isChooseFromClassifyPop: false,
  // 需要带到专区的地址数据
  toZoneAddress: {},
  /** 是否显示视频宣导组件-存储观看时长大于30秒时将值设置为false */
  midphoneVisible: true,
  /** 首次进入简历视频拍摄页 */
  firstResumeShoot: false,
  /** 是否弹出-授权未读消息弹框-推送中间号拨打虚拟号模版信息 */
  midphoneMessage: false,
  /** 中间号弹框是否显示阅读倒计时 */
  isShowMidphoneTime: true,
  /** 中间号主叫方通话结束弹窗是否弹出 */
  isShowPopup: '',
  /** 中间号主叫方通话结束真实号码弹窗是否弹出 */
  showRealTel: '',
  showRealTelState: null as any,
  /** 合作意向弹窗是否自动弹出 */
  showCooperation: {},
  /** 弹窗配置集合 */
  dialogConfigList: {
    list: [],
    version: null,
    appVersion: null,
  },
  /** 弹窗模版集合 */
  dialogTemplate: [],
  /** 首页初始化加载弹窗 */
  indexShowModal: false,

  /** 首页是否获取过弹窗,存储时间戳 */
  indexGetDialog: 0,
  /** 会员中心是否获取过弹窗 - Delete */
  // ucenterGetDialog: false,
  /** 用户IM账号 */
  // userImAcc: '',
  /** 置顶过期弹窗频次 */
  showTopExpiredNum: 0,

  /** 判断是否首次刷新我的找活名片 false首次  true非首次 */
  myResumeRefreshFirst: false,
  /** 首页名片刷新加急引导弹窗当天弹出次数(v3.6.3 只弹一次) */
  indexResumeRefreshNum: 0,
  /** 用于判断web-view页面是否需要刷新 */
  webviewRefresh: '',
  /** 评价活动领取时间 */
  evaluationActivityFinishTime: [],
  /** 工厂-列表当日进入详情次数（置顶、刷新引导弹窗使用） */
  factoryDetailCount: {
    /** 当日进入过的招工详情id列表 */
    recruitDetailIds: [],
    /** 招工置顶、刷新引导弹窗弹出次数 */
    recruitPopupNum: 0,
    /** 当日进入过的找活详情id列表 */
    resumeDetailIds: [],
    /** 找活置顶、刷新引导弹窗弹出次数 */
    resumePopupNum: 0,
  },
  /** 引导弹窗配置 */
  guideLoginConfig: {
    /* 终端 */
    terminal: '',
    /* 配置列表 */
    configDTOList: [],
    /* 点击会员中心是否登录 */
    memberCenter: false,
    /* 账号切换过期天数 */
    accountSwitchExpiredTime: 0,
  },
  /** 账号缓存信息 */
  guideLoginTotal: {
    /** 招工列表 */
    jobList: {
      show: false,
    },
    /** 进入招工详情次数 */
    jobDetail: {
      /** 次数 */
      count: 0,
      /** 是否弹出过登录弹窗 */
      show: false,
      /** 信息id数组 */
      ids: [],
    },
    /** 找活列表 */
    resumeList: {
      show: false,
    },
    /** 进入找活详情次数 */
    resumeDetail: {
      /** 次数 */
      count: 0,
      /** 是否弹出过登录弹窗 */
      show: false,
      /** 信息id数组 */
      ids: [],
    },
  },
  /** 工种选择器临时存放数据 */
  changeClassify: null,
  /**
   * 客人态助力,判读用户是否是新用户,弹出不同的toast提示
   * ①，如当前用户为新用户，则完成授权后在当前页面toast提示：助力成功，您已成功助力朋友！
   * ②、如当前用户为老用户，则完成授权后在当前页面toast提示：助力失败，您不是鱼泡网新用户，无法为您的朋友完成助力哦！
   * */
  newUserToastZhuli: {
    video_dialog: 0, // 0为不弹框
    isNewUser: 0, // 0: 不处理 1: 新用户 2: 老用户
  },
  /** 鱼泡资讯里面的红点数据 */
  ypNewsRedPointData: [],
  /** 当日第几次查看招工 */
  lookJobNum: 1,
  /** 首页发布找活名片弹窗 v4.0.0 弹框标识：post_resume1的弹出条件缓存配置 */
  post_resume1: { ...post_resume1Def },
  /** 当前位置的经纬度-过期时间三分钟 */
  timeLocation: '',
  /** 当日招工大列表工种是否筛选过 */
  isRecruitClassify: false,
  /** 当日注册用户查看招工详情记录 */
  browseJobDetail: [],
  /** 是否弹查看招工付费确认弹框（一天内） */
  notPopToday: 0,
  /** 发布招工的完善工种页数据缓存，这个缓存在发布成功之后需要清理掉 */
  fastRecruitPerfect: {
    /** 工种数据 eg: '12,139,13' */
    classifyIds: '',
    /** 完善的数据集合 */
    list: [],
    /** 已上传的项目资料 */
    image: [],
  },
  /** 弹框的弹出规则缓存，这里json的key代表的是弹框的标识 */
  dialogRule: {} as {
    [key in string]: {
      /** 最后一次弹出的毫秒时间戳 */
      timestamp: number,
      /** 已弹出的次数 */
      showNum: number,
      /** 弹框的弹出规则配置 */
      rule: { [key in string]?: number }
    }
  },
  /** 调用任务完成接口次数 */
  taskCompleteQueryTimes: {
    'new:collectMini': 0,
    'new:addMiniToDesktop': 0,
    'new:addMiniToMyMini': 0,
  },
  /** 标签补工种 */
  recruitlabel: {
    occid: null,
    occ: {},
    date: 0,
    // 次数
    num: 0,
    // 是否已上传曝光埋点
    bg: false,
  },
  /** 招工列表引导完善 */
  recruitscore: {
    score: -1,
    date: 0,
    // 次数
    num: 0,
    // 是否已上传曝光埋点
    bg: false,
  },
  /** 发布招工 挽留弹窗弹窗日期 YYYY/MM/DD */
  publishRecRetentionDateLocal: undefined,
  /** 大转盘 当天是否已弹框 看视频提示 */
  turntableTipsShow: false,
  /** 大转盘 看视频提示 次数 */
  turntableTipsNum: 0,
  /** 记录全部职位是否首次进入 */
  quanbuzhiweiIsFirst: true,
  /** IM列表tab类型选择 */
  imtyps: {
    htab: 'chat',
    ctab: 'ALL',
    poptab: 'INIT_BY_MYSELF',
    cinter: 'LOOK_ME',
    first: true,
  },
  /** 隐藏推荐附件简历名称的uuid */
  hideAttachmentUuid: [] as string[],
  /** 隐藏-标签选择器 */
  hideGuidanceLabel: false,
  jobVieCheckType: '',
  /** 服务宕机计数 */
  sysmaincount: 0,
  /** 每天统计 拨打招工电话的有效扣费次数 */
  successCallRecruitPhone: 0,
  /** 每天一次，招工详情弹出 送积分或权益卡弹窗 */
  isShowCallRecruitPhonePop: false,
  /** 是否进入过简历发布页面 */
  isEnterPublish: false,
  /** 今日是否请求guideToPublish接口判断跳转简历发布页面 */
  isRequestGuideToPublish: false,
  /** 求职期望自动跳转到完善页的次数 */
  resumeAutoPerfect: 0,
  APP_LAUNCH_TIMESTAMP: 0,
  /** 大列表搜索行为参数 */
  SEARCH_ACTION: {
    searchWord: '',
    searchTime: 0,
  },
  /** 全职职位筛选器的为你推荐数据请求间隔时间 */
  classifyRecommendTime: 0,
  /** 职位搜索结果页存储的临时工种id */
  recruitSearchClassifyId: [],
  /** 兼职职位筛选器的为你推荐数据请求间隔时间 */
  partclassifyRecommendTime: 0,
  /** 发布职位的推荐数据 */
  recRecommendClassifies: [],
  /** 发布职位推荐数据请求间隔时间 */
  recRecommendclassifyRecommendTime: 0,
  /** 发布简历的推荐数据 */
  resRecommendClassifies: [],
  /** 发布简历推荐数据请求间隔时间 */
  resRecommendclassifyRecommendTime: 0,
  /** SLS埋点数据 */
  slsEvents: [],
  /** 资源位配置数据 */
  allResourceData: {
    list: [],
    version: null,
    appVersion: null,
  },
  // 完善信息流
  resumeInfoFlow: {
    // 关闭弹窗时间
    closeTime: 0,
    // 下次展示信息流卡片的天数
    nextShowDay: 0,
  },
  classifySearchStoreIds: [],
  // 首页综合筛选是否使用过
  isComprehensiveSearchUsed: false,
}
