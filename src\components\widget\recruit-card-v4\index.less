
.recruit-card {
  position: relative;
  background-color: #fff;
  margin: 16rpx 24rpx;
  border-radius: 24rpx;
  color: #333;
  font-size: 30rpx;
  overflow: hidden;

  .gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.no_margin_top {
  margin-top: 0rpx !important;
}

.no_margin_bottom {
  margin-bottom: 0rpx !important;
}

.content {
  padding: 32rpx 24rpx;
  box-sizing: border-box;

  .tag-icon {
    position: absolute;
    top: 0;
    left: 0;

    .icon {
      width: 80rpx;
      height: 72rpx;
    }
  }

  .title {
    display: flex;
    align-items: center;
    word-break: break-all;
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
  }
    // 卡片名企样式
    .famous-icon {
      height: 40rpx;
      vertical-align: middle;
      margin-right: 8rpx;
      transform: translateY(-4rpx);
    }


    // 急聘样式
    .urgent-tag {
      display: inline-flex;
      height: 36rpx;
      min-width: 56rpx;
      padding: 0 8rpx;
      border-radius: 8rpx;
      border: 1rpx solid #e8362e;
      background: #ffebec;
      color: #e8362e;
      font-weight: 500;
      font-size: 20rpx;
      line-height: 20rpx;
      box-sizing: border-box;
      transform: translateY(-6rpx);
      align-items: center;
    }

    // 外地标签样式
    .outcity-tag {
      display: inline-flex;
      height: 36rpx;
      min-width: 56rpx;
      padding: 0 8rpx;
      border-radius: 8rpx;
      border: 1rpx solid #ff9500;
      background: #fff7e6;
      color: #ff9500;
      font-weight: 500;
      font-size: 20rpx;
      line-height: 20rpx;
      box-sizing: border-box;
      transform: translateY(-6rpx);
      align-items: center;
    }
    // 富文本兜底样式 - 专门的类名避免冲突
    .rich-text-fallback {
      .famous-icon {
        display: inline-block;
        vertical-align: middle;
        flex-shrink: 0; // 防止图标被压缩
      }

    .urgent-tag {
      display: inline-block;
      vertical-align: middle;
      flex-shrink: 0; // 防止标签被压缩
      white-space: nowrap; // 防止标签内部换行
    }

    .outcity-tag {
      display: inline-block;
      vertical-align: middle;
      flex-shrink: 0; // 防止标签被压缩
      white-space: nowrap; // 防止标签内部换行
    }
  }

  .user {
    display: flex;
    flex-direction: row;
    padding: 16rpx 0;
    position: relative;

    &.ac {
      align-items: center;
    }

    .cont {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .viewed-img {
      position: absolute;
      right: 0;
      bottom: -4rpx;

      .icon {
        width: 80rpx;
        height: 72rpx;
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    width: 100%;
    line-height: 30rpx;
    color: #808080;
    vertical-align: middle;
  }

  .date {
    margin-left: 32rpx;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.45);
    text-align: right;
    word-break: break-all;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 40rpx;
    line-height: 40rpx;
  }

  .boss-btn {
    display: flex;
    align-items: center;
    background: #e5f4ff;
    border-radius: 12rpx;
    color: #0092ff;
    font-size: 24rpx;
    line-height: 1;
    flex-shrink: 0;
    padding: 16rpx 14rpx;
    margin-left: 24rpx;

    .icon {
      margin-right: 8rpx;
    }

    .text {
      font-weight: bold;
    }
  }
}

.address {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: rgba(0, 0, 0, 0.45);
  overflow: hidden;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.address-tex {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.45);
}

.location-txt {
  color: rgba(0, 0, 0, 45%);
  padding-left: 8rpx;
  font-size: 26rpx;
  line-height: 40rpx;
}

.recruit-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  height: 48rpx;
  overflow: hidden;

  &.is-viewed {
    margin-right: 104rpx;
  }

  .r-tag {
    flex-shrink: 0;
    padding: 0 12rpx;
    font-size: 26rpx;
    margin-right: 8rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    border-radius: 8rpx;
    background: #f5f6faff;
    color: rgba(0, 0, 0, 0.651);

    &:last-child {
      margin-right: 0;
    }
  }

  .hightLightTag {
    background: rgba(224, 242, 255, 1);
    color: rgba(0, 146, 255, 1) !important;
  }
}

.img {
  width: 102rpx;
  height: 40rpx;
  float: left;
  display: inline;
  position: relative;
  top: 4rpx;
  left: 0;
  margin-right: 8rpx;
}

.footer-ext-container {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  margin-left: auto;
}

.footer-ext-right {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  align-items: center;
}

.dot-green {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
  background: rgba(6, 181, 120, 1);
}



.salary {
  color: #0092ffff;
  font-weight: 500;
  font-size: 30rpx;
  line-height: 1;
  .salary-text {
    margin-top: 16rpx;
  }
}