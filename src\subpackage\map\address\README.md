# 城市选择器模块

## 概述

城市选择器是一个通用的地址选择组件，支持省市区三级联动选择，提供多种配置选项以适应不同业务场景的需求。

## 目录结构

```
src/subpackage/map/address/
├── index.ts                    # 主页面逻辑
├── index.wxml                  # 主页面模板
├── index.less                  # 主页面样式
├── index.json                  # 页面配置
├── components/                 # 子组件
│   ├── search/                 # 搜索组件
│   ├── map-footer/             # 底部操作栏
│   ├── list-view/              # 地址列表视图
│   └── head-location/          # 定位头部组件
└── utils/                      # 工具函数
    ├── index.ts                # 核心逻辑函数
    ├── index.d.ts              # 类型定义
    └── tools.ts                # 工具函数和配置
```

## 核心功能

### 1. 地址选择
- **三级联动**: 支持省-市-区三级地址选择
- **热门城市**: 提供热门城市快速选择
- **搜索功能**: 支持城市名称和拼音搜索
- **定位功能**: 支持GPS定位当前城市

### 2. 多种选择模式
- **单选模式**: 选择一个地址后直接返回
- **多选模式**: 支持选择多个地址，显示底部操作栏
- **区域多选**: 支持同一城市下的多个区域选择

### 3. 特殊配置支持
- **forceCityOnly**: 强制只显示到市一级，隐藏区级选项
- **禁用地区**: 支持禁用特定地区（如港澳台）
- **级别控制**: 可配置显示2级或3级地址
- **全地址控制**: 可配置是否显示"全省"、"全市"选项

## 主要组件

### 主页面 (index.ts)

主页面负责整体的数据管理和事件协调：

```typescript
// 主要数据结构
data = {
  title: '选择城市',
  oneArea: [],      // 第一列数据（省份）
  twoArea: [],      // 第二列数据（城市）
  threeArea: [],    // 第三列数据（区域）
  selectAddr: [],   // 已选择的地址
  deleteAddr: [],   // 被删除的地址
  addrConf: {},     // 地址配置
  isSearch: false,  // 是否显示搜索
  clickLevel: 0,    // 当前点击的级别
}
```

**主要方法**：
- `onLoad()`: 页面初始化，获取配置并加载数据
- `onSelect()`: 处理地址选择事件
- `onConfirm()`: 确认选择并返回结果
- `confirmHandler()`: 统一的确认处理逻辑

### 搜索组件 (components/search/)

提供城市搜索功能：

**功能特性**：
- 支持城市名称搜索
- 支持拼音首字母搜索
- 实时搜索结果高亮
- 空状态提示

**使用方式**：
```xml
<search
  isSearch="{{isSearch}}"
  bind:showSearch="onShowSearch"
  bind:select="onSearchSelect"
/>
```

### 列表视图组件 (components/list-view/)

显示地址列表的核心组件：

**功能特性**：
- 支持三列地址显示
- 选中状态管理
- 滚动定位
- 数量统计显示

**配置参数**：
```typescript
properties = {
  dataSource: Array,    // 数据源
  level: Number,        // 当前级别 (1/2/3)
  selectAddr: Array,    // 已选地址
  addrConf: Object,     // 地址配置
  isFooter: Boolean,    // 是否显示底部
  clickLevel: Number,   // 点击级别
}
```

### 底部操作栏 (components/map-footer/)

多选模式下的操作界面：

**功能特性**：
- 显示已选择的地址
- 支持删除单个地址
- 清空所有选择
- 确认选择操作

### 定位组件 (components/head-location/)

GPS定位功能组件：

**功能特性**：
- 获取当前位置
- 显示定位状态
- 快速选择当前城市
- 定位权限引导

## 工具函数 (utils/)

### 核心函数

#### `initAddress(areas)`
初始化地址数据，根据已选地址设置初始状态。

#### `selectAddress(area, selecting, isHot)`
处理地址选择逻辑，返回选择结果和状态。

#### `selectHandler(addrState, isHot)`
处理地址列表的选中状态和数量统计。

#### `handlerData(tree, level)`
根据配置过滤和处理地址数据。

#### `getAddrConf()`
获取地址选择器的配置参数。

### 配置参数

```typescript
interface IAddressParams {
  type: 'resume' | 'job'           // 业务类型
  title?: string                   // 页面标题
  level?: 2 | 3                   // 显示级别
  maxNum?: number                 // 最大选择数量
  areas?: (string | number)[]     // 已选地址
  disabledIds?: number[]          // 禁用地址ID
  hideNation?: boolean            // 是否隐藏全国
  forceCityOnly?: boolean         // 强制只显示到市级
  selectType?: 'district' | 'resumePositionTab'  // 选择类型
  hasAllType?: 'all' | 'province' | 'city' | 'none' | 'noneHideRegion'  // 全地址显示类型
  headType?: 'location' | 'search' | 'all'  // 头部类型
  point?: {                       // 埋点数据
    source_id: string
    button_name?: string
  }
}
```

## 使用方式

### 1. 基本调用

```typescript
// 打开城市选择器
wx.$.openAddress({
  type: 'job',
  title: '选择工作城市',
  level: 2,
  maxNum: 1,
  hideNation: true,
  disabledIds: [33, 34, 35], // 禁用港澳台
}, (result) => {
  console.log('选择结果:', result.value)
})
```

### 2. 多选模式

```typescript
wx.$.openAddress({
  type: 'resume',
  title: '选择意向城市',
  maxNum: 5,
  selectType: 'district',
  hasAllType: 'city',
}, (result) => {
  console.log('选择的城市:', result.value)
})
```

### 3. 强制市级模式

```typescript
wx.$.openAddress({
  type: 'job',
  title: '推广城市',
  level: 2,
  forceCityOnly: true,  // 不显示区级
  disabledIds: [33, 34, 35],
}, (result) => {
  console.log('选择的城市:', result.value)
})
```

## 数据结构

### 地址数据格式

```typescript
interface AreaData {
  id: string | number      // 地址ID
  name: string            // 地址名称
  ad_code: string         // 行政区划代码
  pid: string | number    // 父级ID
  gid?: string | number   // 祖父级ID
  level: number           // 级别 (1-省 2-市 3-区)
  isFull?: boolean        // 是否为全地址
  checked?: boolean       // 是否选中
  letter?: string         // 拼音全拼
  initials?: string       // 拼音首字母
}
```

### 返回数据格式

```typescript
interface AddressResult {
  value: AreaData[]       // 选中的地址数组
}
```

## 特殊功能详解

### 1. forceCityOnly 功能

当设置 `forceCityOnly: true` 时，系统会在多个层面阻止区级数据的显示和选择：

#### 数据层面处理
- **handlerData**: 过滤第三列（区级）数据
- **handlerSelect**: 不生成区级数据
- **selectAddress**: 市级选择后直接结束

#### 界面层面处理
- **模板条件**: 不渲染第三列组件
- **布局适配**: 自动调整为两列布局

#### 生效流程
```
用户选择市级 → 检查forceCityOnly → 直接结束选择 → 返回结果
```

**实现原理**：
```typescript
// 1. 数据过滤
if (addrConf.forceCityOnly && level === 3) {
  return [] // 过滤区级数据
}

// 2. 选择逻辑
if (addrConf.forceCityOnly && area.level == 2) {
  isEnd = true // 市级选择后直接结束
}

// 3. 界面渲染
wx:if="{{addrConf.level == 3 && !addrConf.forceCityOnly}}"
```

### 2. 直辖市处理

系统自动识别直辖市（北京、上海、天津、重庆）：
- **数据结构**: 直辖市 → 区
- **显示逻辑**: 按照特殊规则处理
- **选择逻辑**: 支持直接选择直辖市

### 3. 热门城市

提供常用城市的快速选择：
- 动态配置热门城市列表
- 优先显示在第一列
- 支持不同业务类型的热门城市配置

### 4. 搜索功能

支持多种搜索方式：
- 城市名称模糊搜索
- 拼音全拼搜索
- 拼音首字母搜索
- 搜索结果高亮显示

## 埋点统计

系统内置完整的埋点统计：

```typescript
// 页面访问埋点
pagePoint('city_filter_click')

// 搜索埋点
pagePoint('city_filter_search_click')
pagePoint('city_filter_search_submit', { search_area_name: '北京' })

// 定位埋点
pagePoint('city_filter_location_enable')
pagePoint('city_filter_current_location_select', { select_city: '北京' })

// 选择结果埋点
pagePoint('addressSelection', {}, ['110000'])
```

## 性能优化

### 1. 数据缓存
- 地址树数据缓存
- 热门城市配置缓存
- 搜索数据预加载

### 2. 渲染优化
- 虚拟滚动支持
- 按需渲染列表项
- 防抖处理用户操作

### 3. 内存管理
- 组件销毁时清理缓存
- 避免内存泄漏
- 合理的数据结构设计

## 注意事项

1. **数据依赖**: 依赖全局的地址数据服务 `wx.$.l`
2. **权限处理**: 定位功能需要用户授权
3. **兼容性**: 支持不同小程序平台
4. **错误处理**: 完善的异常处理和降级方案
5. **类型安全**: 完整的 TypeScript 类型定义

## 扩展开发

### 添加新的选择类型

1. 在 `IAddressParams` 中添加新的 `selectType`
2. 在 `selectAddress` 函数中添加对应逻辑
3. 在 `handlerData` 中添加数据过滤规则

### 自定义显示逻辑

1. 修改 `handlerData` 函数的过滤条件
2. 调整 `selectHandler` 中的选中状态处理
3. 更新模板中的显示逻辑

### 添加新的埋点

1. 在 `pagePoint` 函数中添加新的事件类型
2. 在对应的交互位置调用埋点函数
3. 更新 `sourceMap` 映射关系

## forceCityOnly 详细分析

### 生效链路

```
配置传递 → 数据处理 → 逻辑控制 → 界面渲染
    ↓           ↓         ↓         ↓
openAddress → handlerData → selectAddress → 模板条件
```

### 关键判断点

| 函数 | 判断条件 | 作用 |
|------|----------|------|
| `handlerData` | `level === 3 && forceCityOnly` | 过滤第三列数据 |
| `handlerSelect` | `!forceCityOnly` | 控制数据生成 |
| `selectAddress` | `forceCityOnly && level == 2` | 市级直接结束 |
| `index.wxml` | `!forceCityOnly` | 控制组件渲染 |

### 数据流向

```
forceCityOnly: true
        ↓
┌─────────────────┐
│   配置获取      │ ← getAddrConfig()
└─────────────────┘
        ↓
┌─────────────────┐
│   数据过滤      │ ← handlerData() 过滤区级
└─────────────────┘
        ↓
┌─────────────────┐
│   选择控制      │ ← selectAddress() 市级结束
└─────────────────┘
        ↓
┌─────────────────┐
│   界面渲染      │ ← 不显示第三列
└─────────────────┘
```

这个城市选择器模块提供了完整的地址选择解决方案，支持多种业务场景，具有良好的扩展性和维护性。通过 `forceCityOnly` 功能，可以灵活控制地址选择的精度，满足不同业务需求。