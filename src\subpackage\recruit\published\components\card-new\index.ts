/*
 * @Date: 2024-10-17 10:47:47
 * @Description: 卡片
 */

import { point } from '../../utils'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { nwSaveFilterStoreByIds } from '@/utils/helper/common/index'
import { tryPromise } from '@/utils/tools/common/index'
import dayjs from '@/lib/dayjs/index'

Component(class extends wx.$.Component {
  properties = {
    item: { type: Object, value: {} },
    index: { type: Number, value: 0 },
    activeTab: { type: String, value: '' },
    tabs: { type: Array, value: [] },
    /** 置顶状态类型：-1-未置顶; 0-预约置顶中；1-置顶中;2-暂停置顶中，3-置顶已结束 */
    topStatus: { type: null, value: -1 },
    /** 加急状态 */
    urgentStatus: { type: null, value: -1 },
    /** 客服400开关 */
    jobPointSwitch: {
      type: Boolean,
      value: false,
    },
  }

  data = {
    stickyCountdown: false, // 置顶倒计时
    vieCountdown: false, // 竞招倒计时
  } as any

  timer = null

  lifetimes = {
    detached() {
      clearTimeout(this.timer)
    },
  }

  observers = {
    item(item) {
      // 竞招职位结束时间
      const planEndTime = wx.$.u.getObjVal(item, 'vieBanner.planEndTime', '')
      // 竞招职位
      const isCompetitiveJob = wx.$.u.getObjVal(item, 'isCompete', false)
      // 显示置顶区分职位类型和职位有效期状态：(非竞招职位 || 竞招职位没有过期时间 || 竞招职位有效期大于1天)
      // 不足一天：跟产品确认根据当前时间与过期时间对比不足24小时
      const showTopByJobState = !isCompetitiveJob || (!planEndTime || dayjs(planEndTime).diff(dayjs(), 'hour') >= 24)

      const offlineReasonCode = wx.$.u.getObjVal(item, 'complianceOfflineReason.code')

      const getDraftCardType = (draftId: any, offlineReasonCode: number): string => {
        if (draftId) return 'draft'
        if (offlineReasonCode) return 'offline'
        return ''
      }

      const actionType = this.getActionType(offlineReasonCode, item.draftReasonCode)
      const draftCardType = getDraftCardType(item.draftId, offlineReasonCode)

      if (actionType) {
        this.setData({
          draftCardType,
          actionType,
        })
      }

      /** 添加字段空校验 */
      if (item && item.checkInfo) {
        /** 更新倒计时 */
        // 跟后端确认countDown审核时间为空或者已过期直接返0, 审核通过24小时之内有倒计时
        const { countdown } = item.checkInfo

        // 未置顶或者置顶结束，并且有置顶倒计时  && (非竞招职位 || 竞招职位有效期大于1天)
        // 置顶状态类型：-1-未置顶; 0-预约置顶中；1-置顶中;2-暂停置顶中，3-置顶已结束
        if (this.data.topStatus != 1 && countdown && showTopByJobState) {
          clearTimeout(this.timer)
          this.updateFormatSeconds(countdown)
        }
        if (item.checkInfo.checkFailMsg) {
          this.setData({ hasTel: item.checkInfo.checkFailMsg.indexOf('************') != -1, checkFailMsg: this.data.jobPointSwitch ? (`${item.checkInfo.checkFailMsg.slice(0, 38)}...`) : item.checkInfo.checkFailMsg })
        }
      }

      const showJobVieBanner = wx.$.u.getObjVal(item, 'vieBanner.showBanner', false)
      const vieExpireSoon = wx.$.u.getObjVal(item, 'vieBanner.bannerType', 1) == 3 || wx.$.u.getObjVal(item, 'vieBanner.bannerType', 1) == 6
      const showTopGuidance = wx.$.u.getObjVal(item, 'checkInfo.countdown', 0) && this.data.topStatus != 1 && showTopByJobState
      const topOrUrgentStatus = this.data.topStatus == 1 || this.data.urgentStatus == 1
      // const topOrUrgentStatus = false
      // const showTopGuidance = false
      const viePlanEndTime = wx.$.u.getObjVal(item, 'vieBanner.planEndTime', false)

      /** 展示竞招banner，竞招今日到期，未展示置顶引导，置顶中加急中 */
      if (showJobVieBanner && vieExpireSoon && !showTopGuidance && !topOrUrgentStatus && viePlanEndTime) {
        clearTimeout(this.timer)
        const vieEndDate = new Date(viePlanEndTime)

        this.updateVieSeconds(vieEndDate.valueOf())
      }
    },
  }

  async handleDraftAction() {
    await wx.$.u.waitAsync(this, this.handleDraftAction, [], 1000)
    const { actionType } = this.data
    if (actionType == 1) {
      const url = encodeURIComponent('/enterprise-verify?tabType=1')
      wx.$.r.push({
        path: `/subpackage/web-view/index?url=${url}`,
      })
    }
    if (actionType == 2) {
      const url = encodeURIComponent('/enterprise-verify?tabType=0')
      wx.$.r.push({
        path: `/subpackage/web-view/index?url=${url}`,
      })
    }
    if (actionType == 3) {
      wx.$.u.openCustomerService()
    }
  }

  getActionType(offlineReasonCode: number, draftReasonCode: number): number {
    if (offlineReasonCode === 1 || draftReasonCode === 9) return 1
    if (offlineReasonCode === 2 || draftReasonCode === 10) return 2
    if (offlineReasonCode === 3 || offlineReasonCode === 4) return 3
    return 0
  }

  /** 置顶倒计时 */
  updateFormatSeconds(countdown) {
    if (countdown > 0) {
      this.setData({
        stickyCountdown: formatSeconds(countdown),
      })
      this.timer = setTimeout(() => {
        this.updateFormatSeconds(countdown - 1)
      }, 1000)
    } else {
      this.setData({ stickyCountdown: false })
    }
  }

  updateVieSeconds(timestamp: number) {
    const curValue = new Date().valueOf()

    if (timestamp - curValue > 0) {
      this.setData({
        vieCountdown: getRemainingTime(timestamp - curValue),
      })
      this.timer = setTimeout(() => {
        this.updateVieSeconds(timestamp)
      }, 60000)
    } else {
      this.setData({
        vieCountdown: false,
      })
    }
  }

  /** 竞招延长 */
  async onVie(event) {
    // if (this.data.item.vieBanner.canRenew)
    const canRenew = wx.$.u.getObjVal(this.data, 'item.vieBanner.canRenew', false)
    if (!canRenew) return
    await wx.$.u.waitAsync(this, this.onVie, [event], 300)
    const { promptText, label } = wx.$.u.getObjVal(event, 'currentTarget.dataset', {})
    this.positionManagementClickBuryingPoint(promptText, label)
    const dataName = wx.$.u.getObjVal(event, 'currentTarget.dataset.name')
    if (dataName) {
      wx.$.collectEvent.event('rnPageClickButton', {
        page_name: 'my_find_worker',
        button_name: dataName,
      })
    }
    const { vieBanner, jobId, cityId = '', occV2 } = this.data.item
    let occIds = [];
    (Array.isArray(occV2) ? occV2 : []).forEach(({ occIds: hids }) => {
      occIds.push(...hids)
    })
    occIds = Array.from(new Set(occIds))

    const occIdStr = occIds.join(',')

    const url = vieBanner.vieType == 0 ? `/b-member-vip/order?isRenew=1&jobId=${jobId}&vipFromPageSource=JOB_VIE_VIP&occupationV2Ids=${occIdStr}&cities=${cityId}` : `/campaign-recruitment?isRenew=1&jobId=${jobId}&occupationV2Ids=${occIdStr}&cities=${cityId}`
    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        isLogin: true,
        url: encodeURIComponent(url),
        vieJumpType: 'renew',
      },
    })
  }

  /** 加急招 */
  async onUrgentNav() {
    await wx.$.u.waitAsync(this, this.onUrgentNav, [], 300)
    this.positionManagementClickBuryingPoint('加急招生效中', '查看效果')
    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        isLogin: true,
        url: encodeURIComponent(`/props-effect/job-urgent?jobId=${this.data.item.jobId}`),
      },
    })
  }

  /** 查看置顶效果 */
  async onTopNav() {
    await wx.$.u.waitAsync(this, this.onTopNav, [], 300)
    this.positionManagementClickBuryingPoint('置顶生效中', '查看效果')
    wx.$.r.push({
      path: '/subpackage/web-view/index',
      query: {
        isLogin: true,
        url: encodeURIComponent(`/props-effect/job-top?jobId=${this.data.item.jobId}`),
      },
    })
  }

  /** 跳转到详情 */
  async onClick() {
    await wx.$.u.waitAsync(this, this.onClick, [], 300)
    // 跳转待开放详情
    const { draftId } = this.data.item
    if (draftId) {
      this.triggerEvent('draftCard', { draftId })
      return
    }
    wx.$.r.push({
      path: '/subpackage/recruit/my_detail/index',
      query: {
        id: this.data.item.jobId,
      },
    })
  }

  /** 去升级 */
  async onUpgrade() {
    const { item, index } = this.data
    this.triggerEvent('upgrade', { item, index })
    const { jobId } = item || {}
    const h5Url = `/campaign-recruitment-gift?jobId=${jobId}`
    wx.$.r.push({
      path: `/subpackage/web-view/index?isLogin=true&url=${encodeURIComponent(h5Url)}`,
    })
  }

  /** 去完善 */
  async onJumpPer(event) {
    await wx.$.u.waitAsync(this, this.onJumpPer, [event], 300)
    const { promptText } = wx.$.u.getObjVal(event, 'currentTarget.dataset', {})
    const handlePromptText = `有多个牛人想知道 ${promptText?.join('、') || ''}`
    this.positionManagementClickBuryingPoint(handlePromptText, '去完善')
    point(this, '去完善')
    const { jobId, occV2 } = this.data.item
    const occIds = occV2.map(item => item.occIds).flat()
    wx.$.l.jumpToComplete({ jobId, occIds: wx.$.u.uniqueArr(occIds), type: 'manualImprovement', isPublish: false }, {})
  }

  /** 待开放 开始招聘 */
  async onPublishDraft() {
    await wx.$.u.waitAsync(this, this.onPublishDraft, [], 500)
    point(this, '开始招聘')
    this.triggerEvent('publishDraft', { draftId: this.data.item.draftId })
  }

  /** 编辑待开放招工信息 */
  async onEditDraft() {
    await wx.$.u.waitAsync(this, this.onEditDraft, [], 300)
    wx.$.r.push({
      path: '/subpackage/recruit/edit-draft/index',
      query: { draftId: this.data.item.draftId },
    })
  }

  /** 删除待开放 */
  async onDeleteDraft() {
    await wx.$.u.waitAsync(this, this.onDeleteDraft, [], 300)
    const popup = await dealDialogRepByApi('deleteJob')
    if (popup) {
      wx.$.showModal({
        ...popup,
        success: ({ routePath }) => {
          if (routePath === 'deleteJob') {
            wx.$.javafetch['POST/job/v3/manage/draft/del']({ draftId: this.data.item.draftId, completeV3SourceFlag: true }).then((response) => {
              if (response && response.code == 30020203) {
                wx.$.msg(response.message)
                this.triggerEvent('jumpTabTotal')
                return
              }
              wx.$.msg('删除职位成功')
              this.onRefreshList()
            })
          }
        },
      })
    }
  }

  /** 重新发布 */
  async onReSend() {
    await wx.$.u.waitAsync(this, this.onReSend, [], 300)
    point(this, '重新发布')
    this.triggerEvent('resend', { JobId: this.data.item.jobId })
  }

  /** 在线客服 */
  async onContact() {
    await wx.$.u.waitAsync(this, this.onContact, [], 300)
    wx.$.u.openCustomerService('1')
  }

  /** 刷新事件 */
  async onRefresh() {
    await wx.$.u.waitAsync(this, this.onRefresh, [], 300, { title: '刷新中...' })
    point(this, '刷新')
    const { item } = this.data
    const resCheck = await fetchRefreshCheck.call(this, { jobId: item.jobId })
    if (!resCheck || resCheck.code != 0) {
      return
    }
    const { data } = resCheck.data
    fetchRefresh.call(this, {
      free: data.free,
      jobId: item.jobId,
      refreshCheckId: data.refreshCheckId,
    }).then(res => {
      if (res.code == 0) {
        const consumption_points = resCheck.data.free ? 0 : resCheck.data.discountPrice
        /** 刷新成功的埋点 */
        wx.$.collectEvent.event('recruitmentRefresh', {
          source: '管理招工刷新',
          /** 刷新使用的积分 */
          consumption_points,
          refresh_results: '成功',
        })
        // 刷新成功后，更新列表
        this.onRefreshList()
      }
      if (res.popType == 'top') {
        this.onJumpTop()
      }
    })
  }

  positionManagementClickBuryingPoint(prompt_text, button_name) {
    wx.$.collectEvent.event('position_management_click', {
      job_id: String(this.data.item?.jobId || ''),
      prompt_text,
      button_name,
    })
  }

  /** 去置顶 */
  async onJumpTop(e?) {
    await wx.$.u.waitAsync(this, this.onJumpTop, [e], 300)
    const { buryingpointId } = e.currentTarget.dataset
    if (buryingpointId == 'position_management_click') {
      this.positionManagementClickBuryingPoint('置顶职位获得更多曝光', '去置顶')
    }
    e && point(this, e.currentTarget.dataset.text)
    this.onJumpTopOrUrgent({ showTab: this.data.urgentStatus == 1 ? false : undefined })
  }

  /** 加急招 */
  async onJumpUrgent(e) {
    await wx.$.u.waitAsync(this, this.onJumpUrgent, [e], 300)
    const { buryingpointId } = e.currentTarget.dataset
    if (buryingpointId == 'position_management_click') {
      this.positionManagementClickBuryingPoint('曝光免费，查看才扣费！', '加急招')
    }
    e && point(this, e.currentTarget.dataset.text)
    const topOrUrgentStatus = this.data.topStatus == 1 || this.data.urgentStatus == 1
    this.onJumpTopOrUrgent({ showTab: topOrUrgentStatus ? false : undefined, activeTab: 1 })
  }

  onJumpTopOrUrgent(e) {
    const { item } = this.data
    wx.$.l.existJobTopSet(item.jobId, {
      pageFrom: 'jobManage',
      showTab: 1,
      activeTab: 0,
      jumpMethod: 'push',
      ...e,
    })
  }

  /** 修改 */
  async onSetting() {
    await wx.$.u.waitAsync(this, this.onSetting, [], 300)
    point(this, '修改')
    const { item } = this.data
    const { code: isCheck } = wx.$.u.getObjVal(item, 'checkInfo.isCheck', {}) || {}
    wx.$.r.push({
      path: '/subpackage/recruit/jisu_issue/index',
      query: {
        id: item.jobId,
        isCheck,
      },
    })
  }

  /** 关闭招工 */
  async onClosed() {
    await wx.$.u.waitAsync(this, this.onClosed, [], 300)
    point(this, '关闭职位')
    const { popup, code } = await tryPromise(wx.$.javafetch['POST/job/v2/manage/job/close/preCheck']({ jobId: this.data.item.jobId }), {})
    if (popup) {
      wx.$.showModal({
        ...popup,
        success: ({ routePath }) => {
          if (routePath === 'closeJobConfirm' || routePath == 'closeJob') {
            this.closeJobConfirm()
          }
        },
      })
    } else if (code == 0) {
      this.closeJobConfirm(true)
    }
  }

  /** 关闭招工弹窗确认事件 */
  async closeJobConfirm(withToast = false) {
    const { item } = this.data
    const res = await fetchClose({ jobId: item.jobId })
    if (res.code == 0) {
      this.onRefreshList()
      // 刷新简历列表职位tab
      // initTabPosition(true)
      if (withToast) {
        wx.$.msg('将不再展示此条信息')
      }
    }
  }

  /** 刷新列表 */
  async onRefreshList() {
    this.triggerEvent('refresh')
  }

  /** 实名认证次数使用完后，需强制实名认证-6191227990 */
  async onRealName() {
    await wx.$.u.waitAsync(this, this.onRealName, [], 300)
    wx.$.r.push({ path: '/subpackage/member/realname/index' })
  }
})

/** 格式化时间 */
function formatSeconds(seconds) {
  // 计算小时、分钟和秒数
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  // 补零处理
  const formatNumber = (num) => num.toString().padStart(2, '0')
  // 返回格式化的时间字符串
  return `${formatNumber(hours)}:${formatNumber(minutes)}:${formatNumber(secs)}`
}

/** 关闭招工 */
async function fetchClose(params: YModels['POST/job/v2/manage/job/close/do']['Req']) {
  const res = await wx.$.javafetch['POST/job/v2/manage/job/close/do'](params).catch(err => {
    return err
  })
  wx.hideLoading()
  if (res.code != 0) {
    wx.$.msg(res.message || '关闭失败')
  } else {
    wx.$.msg('将不再展示此条信息')
  }
  return res
}

/** 检测刷新招工 */
async function fetchRefreshCheck(params: YModels['POST/job/v2/manage/job/refresh/check']['Req']) {
  const res = await wx.$.javafetch['POST/job/v2/manage/job/refresh/check'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })
  wx.hideLoading()
  if (res.dialogType) {
    return res
  }

  if (!res.popup) {
    // 未匹配的任何弹框的处理
    if (res.code != 0) {
      wx.$.msg(res.message || '刷新失败')
    }
    return res
  }

  // 通用弹框
  return handlerDialogCall.call(this, res.popup, res)
}

/** 通用弹框处理
 * @param msg: 没有匹配到弹框的toast提示处理
 */
async function handlerDialogCall(popup, res) {
  const { ignore = [], dialogData } = res
  const { item } = this.data
  const dialogIdentify = popup.dialog_identify || ''
  const areaId = item.cityId || item.provinceId

  if (ignore.includes(dialogIdentify)) return Promise.reject('静默失败')

  return new Promise(async (resolve) => {
    if (dialogIdentify == 'wjzqytc') {
      const tabType = dialogData?.template?.tabType
      const occupationV2Ids = item.occV2.map(i => {
        return i.occIds.join(',')
      }).join(',')
      const areaObj: any = await wx.$.l.getAreaById(item.cityId)
      const cities = areaObj.city?.id || areaObj.province?.id
      const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}&occupationV2Ids=${occupationV2Ids}&cities=${cities}`)
      wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
      resolve({ code: -1 })
      return
    }
    wx.$.showModal({
      ...popup,
      success: async (result) => {
        const { routePath } = result || { btnIndex: -1, routePath: '' }
        switch (`${routePath}`) {
          case 'modify2publish': // 重新发布标识
            republishByJobId(item.jobId, routePath)
            resolve({ code: -1 })
            break
          case 'modify': // 重新发布招工的付费弹框
          case 'refresh': // 刷新按钮标识
            break
          case 'toEarnPoints': { // 去赚积分按钮标识
            const query = {
              isFromPage: 'CLRefreshRecruitCard',
              jobId: item.jobId,
            }
            wx.$.toGetIntegral(query)
            resolve({ code: -1 })
            break
          }
          case 'viewDetails': // 账号异常的跳转处理
            wx.$.r.push({ path: '/subpackage/systips/index' })
            resolve({ code: -1 })
            break
          case 'toResume': // 刷新招工成功之后点击【查看工友信息】按钮
          case 'workerList': // 刷新招工成功之后点击【查看工友信息】按钮
            // 处理页面传入页面的路由参数
            nwSaveFilterStoreByIds(areaId, await wx.$.l.transformClsIdHidsByOccV2(item.occV2 || []), 'resume')
            wx.$.r.reLaunch({ path: '/pages/resume/index' })
            resolve({ code: -1 })
            break
          case 'top': { // 点击【去置顶】按钮
            wx.$.l.existJobTopSet(item.jobId, {
              showTab: 1,
            })
            resolve({ code: -1 })
            break
          }
          default:
            // 走到这里代表点击了取消按钮或者没有匹配到对应的按钮
            resolve({ code: -1 })
        }
        resolve(res)
      },
    })
  })
}

// 修改状态转新发布招工，单独查一次招工信息来获取城市地区
const republishByJobId = async (jobId: number, routePath) => {
  const { data, code } = await wx.$.javafetch['POST/job/v3/manage/job/info']({ jobId })

  if (!code) {
    const areaId = data.urbanAreas.countyId || data.urbanAreas.cityId
    const areaData = await wx.$.l.getAreaById(areaId) as any
    wx.$.r.push({
      path: '/subpackage/recruit/fast_issue/index/index',
      query: {
        flag: routePath,
        occV2: data.occV2
          .map((it) => it.occIds)
          .flat()
          .join(','),
        content: data.detail,
        title: data.title,
        areaId: areaData.current.id || areaData.city.id,
        adCode: areaData.current.ad_code,
        location: `${data.location?.longitude || ''},${data.location?.latitude || ''}`,
        address: data.addressTitle,
      },
    })
  }
}

/** 刷新招工 */
async function fetchRefresh(params: YModels['POST/job/v2/manage/job/refresh/do']['Req']) {
  const res = await wx.$.javafetch['POST/job/v2/manage/job/refresh/do'](params, {
    showErrCodes: ['10001'],
  }).catch((err) => {
    return err
  })
  if (!res.dialogData) {
    wx.$.msg(res.message || res.code == 0 ? '刷新成功' : '刷新失败')
    return res
  }
  res.popup && handlerDialogCall.call(this, res.popup, res)
  return res
}

function getRemainingTime(diffMs: number) {
  // const now = new Date().valueOf() // 当前时间
  // const diffMs = targetTime - now // 时间差，单位为毫秒
  if (diffMs <= 0) {
    return '00:00' // 如果时间已过，返回 00:00
  }

  // 计算小时和分钟
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60)) // 总小时数
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60)) // 剩余分钟
  // const diffSeconds = Math.floor(((diffMs % (1000 * 60 * 60)) % (1000 * 60)) / 1000)

  // 格式化为两位数
  const hours = String(diffHours).padStart(2, '0')
  const minutes = String(diffMinutes).padStart(2, '0')
  // const seconds = String(diffSeconds).padStart(2, '0')

  return `${hours}:${minutes}`
}
