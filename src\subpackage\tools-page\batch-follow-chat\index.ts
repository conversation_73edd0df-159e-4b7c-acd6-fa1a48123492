import { getDom } from '@/utils/tools/common/index'

/*
 * @Description: 批量追聊页面
 */
const { top: mTop, height } = wx.$.u.sInfo().menuRect
Page(class extends wx.$.Page {
  useStore(store: StoreRootState) {
    const { imGlobalSwitch } = store.message || {}
    const { maxMessageWordCountLimit, b2cSayHelloAgainContent } = imGlobalSwitch || {}
    return {
      maxMessageWordCountLimit,
      b2cSayHelloAgainContent,
    }
  }

  data = {
    // 顶部导航高度
    headerHeitght: mTop + height + 8,
    // 是否初始化数据中
    isLoad: true,
    // 追聊牛人数据
    list: [],
    // 选中的追聊牛人数据
    sltedObj: {},
    // 招呼语列表
    chatList: [],
    // 选中的招呼语
    chatValue: '',
    // 选中的招呼语下标
    chatSltedIdx: -1,
    // 键盘高度
    keyboardHeight: 0,
  }

  onLoad() {
    wx.$.collectEvent.event('batch_greet_detail_page_exposure')
    this.initData()
    this.getHelloWordList()
  }

  initData() {
    this.setData({ isLoad: true })
    wx.showLoading({ title: '加载中...' })
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/notReplyUserList']({}, { isFkReject: true }).then((res) => {
      wx.hideLoading()
      const { data, code } = res || {}
      if (code == 0) {
        const { list } = data || {}
        const sltedObj = {}
        if (wx.$.u.isArrayVal(list)) {
          list.forEach((item) => {
            const { conversationId } = item || {}
            sltedObj[conversationId] = conversationId
          })
        }

        this.setData({ list: list || [], sltedObj, isLoad: false })
      } else {
        this.setData({ list: [], sltedObj: {}, isLoad: false })
      }
    }).catch(() => {
      this.setData({ isLoad: false, list: [] })
      wx.hideLoading()
    })
  }

  getHelloWordList() {
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/helloWordList']().then((res) => {
      const { data, code } = res || {}
      if (code == 0) {
        const { list } = data || {}
        let chatValue = ''
        let chatSltedIdx = -1
        let nList = []
        if (wx.$.u.isArrayVal(list)) {
          nList = list.map((item) => (item && item.content) || '')
          const item = list[0] || {}
          const { content } = item || {}
          chatValue = content || ''
          chatSltedIdx = 0
        }
        this.setData({ chatList: nList, chatValue, chatSltedIdx })
      } else {
        this.setData({ chatList: [], chatValue: '', chatSltedIdx: -1 })
      }
    }).catch(() => {
      this.setData({ chatList: [], chatValue: '', chatSltedIdx: -1 })
    })
  }

  async onSxClick() {
    await wx.$.u.waitAsync(this, this.onSxClick, [], 2000)
    this.setData({ keyboardHeight: 0 })
    const { sltedObj, list } = this.data
    wx.$.nav.push(
      '/subpackage/tools-page/batch-follow-chat-list/index',
      {},
      (data) => {
        this.setData({ sltedObj: data.sltedObj })
      },
      {
        sltedObj,
        list,
      },
    )
  }

  onCheckClick(e) {
    const { sltedObj } = this.data
    const { item } = e.currentTarget.dataset
    const { conversationId } = item || {}
    const nSltedObj = { ...sltedObj }
    if (nSltedObj[conversationId]) {
      delete nSltedObj[conversationId]
    } else {
      nSltedObj[conversationId] = conversationId
    }
    this.setData({ sltedObj: nSltedObj })
  }

  onChatChange() {
    wx.$.collectEvent.event('batch_greet_detail_page_click', { click_button: '换一句' })
    const { chatSltedIdx, chatList } = this.data
    if (chatSltedIdx == -1 || !wx.$.u.isArrayVal(chatList)) return
    const nSltedIdx = (chatSltedIdx + 1) % chatList.length
    const chatValue = chatList[nSltedIdx]
    this.setData({ chatSltedIdx: nSltedIdx, chatValue })
  }

  onChatInput(e) {
    const { content } = e.detail
    this.setData({ chatValue: content })
  }

  onBindkKeyBoardHeightChange(e) {
    const { height } = e.detail
    if (height > 0) {
      setTimeout(() => {
        this.moveToHeader()
      }, 20)
    }
    this.setData({ keyboardHeight: height || 0 })
  }

  moveToHeader(cb?) {
  /** 需要滚动的距离实际就是金刚区的高度加上margin-bottom，因为顶部的搜索区域不在文档流中，不算高度  */
    getDom('#zw-view').then(res => {
      if (res) {
        const zwView = res.height
        const scrollTop = zwView
        wx.pageScrollTo({
          scrollTop, // 滚动到的位置（距离顶部 px）
          duration: 200, // 滚动所需时间 如果不需要滚动过渡动画，设为0（ms）
          complete: () => {
            cb && cb(scrollTop)
          },
        })
      }
    })
  }

  async onSend() {
    await wx.$.u.waitAsync(this, this.onSend, [], 2000)
    const { chatValue, sltedObj, list } = this.data
    const conversationIdArr = Object.keys(sltedObj || {})
    if (!wx.$.u.isArrayVal(conversationIdArr)) {
      wx.$.msg('请选择牛人')
      return
    }
    if (!chatValue) {
      wx.$.msg('请输入追聊消息')
      return
    }
    wx.$.collectEvent.event('batch_greet_detail_page_click', { click_button: '发送' })
    let isBack = true
    let nList = []
    if (list.length > conversationIdArr.length) {
      isBack = false
      nList = list.filter(item => {
        return !conversationIdArr.includes(item.conversationId)
      })
    }
    const failMsg = '发送失败，请稍后重试'
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/b2cSayHelloAgain/batchCommit']({ conversationIds: conversationIdArr, helloWord: chatValue }).then((res) => {
      wx.hideLoading()
      const { code, data } = res || {}
      const { successNum } = data || {}
      if (code == 0 && list.length == conversationIdArr.length) {
        wx.$.msg(successNum == 0 ? failMsg : '发送成功').then(() => {
          wx.$.r.back()
        })
        return
      }
      !isBack && this.setData({ list: nList, sltedObj: {} })
      if (code == 0) {
        wx.$.msg(successNum == 0 ? failMsg : '发送成功')
        return
      }

      wx.$.msg(failMsg).then(() => {
        isBack && wx.$.r.back()
      })
    }).catch(() => {
      wx.hideLoading()
      !isBack && this.setData({ list: nList, sltedObj: {} })
      wx.$.msg(failMsg).then(() => {
        isBack && wx.$.r.back()
      })
    })
  }
})
