.container {
    width: 100%;
    padding: 40rpx 0;
    border-bottom: 1rpx solid  rgba(233, 237, 243, 1);
}

.placeholder{
    color: rgba(0,0,0,.25);
    font-size: 30rpx;
}

.title {
    color: @text65;
    font-size: 30rpx;
    font-weight: 400;
    line-height: 42rpx;
}

.content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    padding-top: 16rpx;
    
}

.content-text {
    font-size: 34rpx;
    font-weight: 400;
    color: @text85;
    line-height: 48rpx;
    .textrow(1);
}

.content-suffix {
    display: flex;
    flex-shrink: 0;
}

.disabled {
    color: rgba(0,0,0,.25);
}