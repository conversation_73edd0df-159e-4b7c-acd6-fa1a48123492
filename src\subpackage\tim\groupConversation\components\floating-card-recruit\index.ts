/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 招工悬浮卡片
 */
import { actions, dispatch } from '@/store/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'

Component(class extends wx.$.Component {
  properties = {
    conversation: {
      type: Object,
      value: {},
    },
  }

  data = {
    cardInfo: {} as any,
    // 是否展开 悬空内容
    isShow: false,
  }

  observers = {
    conversation(v) {
      if (wx.$.u.isEmptyObject(v)) return
      const { infoDetail = {} } = v || {}
      const { cardInfo } = infoDetail || {}
      this.setData({ cardInfo })
    },
  }

  onChange() {
    const { isShow } = this.data
    this.setData({ isShow: !isShow })
    this.triggerEvent('change')
  }

  // 详情页埋点使用字段
  setBuryingPoint(item) {
    const buryingPoint = {
      id: item.job_id,
      info: {
        source: 'IM卡片',
        source_id: '15',
      },
    }
    dispatch(actions.recruitDetailActions.setState({ buryingPoint }))
  }

  async onDetailClick() {
    const { conversation } = this.data as DataTypes<typeof this>
    const { infoDetail, telRightsInfo, toUserId } = conversation || {} as any
    const { telType, infoId, infoType } = telRightsInfo || {}
    const path = '/subpackage/recruit/details/index'
    const { relatedInfoId, infoId: detailInfoId } = infoDetail || {}
    const options = wx.$.r.getQuery()
    this.setBuryingPoint({ job_id: Number(relatedInfoId) ? relatedInfoId : detailInfoId })
    wx.$.collectEvent.event('universal_click', {
      bis: 'ypzp',
      s_t: 'IM',
      code: options.conversationId,
    })
    wx.$.r.push({
      path,
      query: {
        source: 'IM卡片',
        source_id: '15',
        id: Number(relatedInfoId) ? relatedInfoId : detailInfoId,
        type: 'groupConversation',
        telType,
        infoId,
        infoType,
        s_t: 'IM',
        r_s_code: options.conversationId,
        myepc: getPageCode(),
        toUserId,
      },
    })
  }
})
