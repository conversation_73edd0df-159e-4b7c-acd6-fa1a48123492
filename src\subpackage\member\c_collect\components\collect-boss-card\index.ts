import { communicate } from '@/utils/helper/member/index'

/*
 * @Author: wyl <EMAIL>
 * @Date: 2024-10-10 17:19:56
 * @FilePath: \yp-mini\src\subpackage\member\c_collect\components\collect-boss-card\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
Component({
  properties: {
    item: {
      type: Object,
      value: {},
    },
    isNeedMarginBottom: {
      type: Boolean,
      value: true,
    },
  },
  methods: {
    async onViewProfile() {
      await wx.$.u.waitAsync(this, this.onViewProfile, [], 800)
      const { item } = this.data
      const { jobInfoRpcResp, userInfoResp, id: collectId } = item || {}
      const { id } = jobInfoRpcResp || {}
      const { headPortrait, userName, userId } = userInfoResp || {}
      // 跳转 老板主页
      wx.$.r.push({ path: '/subpackage/recruit/individual/index',
        params: { info: {
          ...item,
          jobId: id || 0,
          avatarUrl: headPortrait || '',
          userName: userName || '先生',
        } },
        query: { userId, jobId: id || 0, collectId: collectId || 0 } })
    },

    // 取消收藏
    async onCancelCollectBoss() {
      await wx.$.u.waitAsync(this, this.onCancelCollectBoss, [], 500)

      const { item } = this.data
      const { id: collectId } = item || {}
      const res = await communicate.asyncCancelAttention(
        {
          collectId,
        },
        {
          type: 'cancelid',
          loadingText: '取消收藏',
          successMsg: '已取消收藏',
        },
      )
      if (res) {
        this.triggerEvent('cancelCollectBoss', { collectId })
      }
    },
  },
})
