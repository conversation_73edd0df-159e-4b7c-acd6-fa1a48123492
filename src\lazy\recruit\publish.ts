import { tryPromise } from '@/utils/tools/common/index'
import { storage } from '@/store/index'
import { Bury } from './bury'
import { getAreaById } from '../helper/location/index'

/** 管理招工页面 */
const manageRecruit = '/subpackage/recruit/published/index'

/** 详细地址的 转值 */
export function formatThoroughAddress(selectArea: any = {}) {
  let address = ''
  if (selectArea) {
    address = `${selectArea.name || selectArea.ad_name}`
    if (selectArea.district || selectArea.address) {
      address += '@@@@@'
      if (selectArea.district) {
        address += selectArea.district
      }
      if (selectArea.address) {
        address += selectArea.address
      }
    }
  }
  return address
}

/**
 * 发布预检接口
 * @param {IPublishParams} params 发布参数
 * @param {IPublishSuccessCB} callback 发布成功的回调
 * @returns {Promise}
 */
export const publishWithPrevCheck = async (params: any, ext: IPrevCheckExtern, callback: IPublishSuccessCB) => {
  wx.$.loading('发布中...')
  const { location } = params
  /** 发布职位根据职位获取商圈，默认选中第一个 */
  if (location) {
    const { longitude, latitude } = location
    const businessCircles = await tryPromise(wx.$.javafetch['POST/lbs/v1/location/businessArea']({ longitude, latitude }).then(response => response.data.data.map(({ name }, index) => ({ name, selected: index == 0 ? 1 : 0 }))), [])
    params.businessCircles = businessCircles
  }
  const { closeInfoId: _, ...preCheckParams } = <any>params
  const response = await wx.$.javafetch['POST/job/v3/manage/job/publish/preCheck'](preCheckParams, { hideErrCodes: ['10001'] }).catch((response) => {
    wx.hideLoading({ noConflict: true })
    return Promise.reject(response)
  })
  if (wx.$.u.isEmptyObject(response)) return Promise.reject()
  return handlePublishResponse(params, response, false, ext).then(async (routePath) => {
    /** 没有弹窗 或者匹配到弹窗触发按钮的routePath，执行后续流程 */
    if (!routePath || ['confirm', 'continueReleaseInfo'].includes(routePath)) {
      const publishParams = { ...params, ...response.data.data }
      wx.$.loading('发布中...')
      await publishRecruit(publishParams, false, ext || {}, callback)
    }
  })
}

/**
 * 发布预检接口 + 草稿箱
 * # 需要在`.catch`代码块上捕获特殊弹窗标识
 * @param {IPublishParams} params 发布参数
 * @param {IPublishSuccessCB} callback 发布成功的回调
 * @returns {Promise}
 */
export const prevCheckWithDraft = async (params: any, ext: IPrevCheckExtern, callback: IPublishSuccessCB) => {
  wx.$.loading('发布中...')
  const { location } = params
  /** 发布职位根据职位获取商圈，默认选中第一个 */
  if (location) {
    const { longitude, latitude } = location
    const businessCircles = await tryPromise(wx.$.javafetch['POST/lbs/v1/location/businessArea']({ longitude, latitude }).then(response => response.data.data.map(({ name }, index) => ({ name, selected: index == 0 ? 1 : 0 }))), [])
    // eslint-disable-next-line no-param-reassign
    params.businessCircles = businessCircles
  }
  const { closeInfoId: _, ...preCheckParams } = <any>params
  const response = await wx.$.javafetch['POST/job/v3/manage/job/publish/preCheck']({ ...preCheckParams, completeV3SourceFlag: true }, { hideErrCodes: ['10001'] }).catch((response) => {
    wx.hideLoading({ noConflict: true })
    return Promise.reject(response)
  })
  if (wx.$.u.isEmptyObject(response)) return Promise.reject()
  return handlePublishResponse(params, response, true, ext).then(async (routePath) => {
    /** 没有弹窗 或者匹配到弹窗触发按钮的routePath，执行后续流程 */
    if (!routePath || ['confirm', 'continueReleaseInfo'].includes(routePath)) {
      const publishParams = { ...params, ...response.data.data }
      wx.$.loading('发布中...')
      await publishRecruit(publishParams, true, ext, callback)
    }
  })
}

/**
 *
 * @param {any} params 发布参数
 * @param {IPublishSuccessCB} callback 发布成功的回调
 * @returns {Promise}
 */
export const publishRecruit = async (params: any, useDraft: boolean, ext, callback: IPublishSuccessCB) => {
  const response = await wx.$.javafetch['POST/job/v3/manage/job/publish/job']({ ...params, completeV3SourceFlag: true }, { hideErrCodes: ['10001'] }).catch(response => {
    wx.hideLoading({ noConflict: true })
    return Promise.reject(response)
  })
  if (!response || !response.data) {
    wx.$.msg(response && response.message ? response.message : '发布失败')
    return Promise.reject()
  }
  // 这个需要发布成功后删除
  storage.remove('fastRecruitPerfect')
  return handlePublishResponse(params, response as any, useDraft, ext || {}).then(async (routePath) => {
    if (!routePath || ['confirm', 'continueReleaseInfo', 'cancel'].includes(routePath)) {
      /** 发布成功 */
      callback(false, response)
    }
    if (['toCompanyAuth'].includes(routePath)) {
      /** 临时发布成功 */
      callback(true, response)
    }
    /** 捕获未发布成功的情况 */
    return Promise.reject()
  }).catch((error) => {
    wx.hideLoading({ noConflict: true })
    /** 自定义弹窗，走到 */
    if (error && ['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(error.dialogIdentify)) {
      throw error
    }
  })
}

/**
 * 发布招工弹窗配置
 * @param params
 * @param response
 * @param useDraft
 * @param ext
 * @returns
 */
export const handlePublishResponse = (
  params: IPublishParams,
  response: IPublishResponse,
  useDraft: boolean,
  ext: IPrevCheckExtern,
) => {
  const { reportData = {}, ignoreDialog = [] } = ext
  wx.hideLoading({ noConflict: true })
  if (response && response.dialogData) {
    const dialogIdentify = wx.$.u.getObjVal(response.dialogData, 'dialogIdentify')
    //! 返回了弹窗 但是没有获取到对应的配置，直接拒绝执行
    if (!response.popup) {
      Bury.operation({
        data: JSON.stringify({ dialogIdentify }),
        functionName: 'handlePublishResponse',
        type: 'error',
        name: 'CMS通用弹窗',
      })
      return Promise.reject(response.dialogData)
    }
    return new Promise(async (resolve, reject) => {
      // const dialogIdentify = wx.$.u.getObjVal(response.dialogData, 'dialogIdentify')
      if (ignoreDialog.includes(dialogIdentify)) {
        Bury.operation({
          data: JSON.stringify({ dialogIdentify }),
          type: 'error',
          functionName: 'handlePublishResponse',
          name: 'CMS通用弹窗',
        })
        Promise.reject(`流程异常，'${dialogIdentify}'异常弹窗已被忽略`)
        return
      }
      if (dialogIdentify === 'permit_due_popup') {
        saveDraft({ ...params, draftReasonCode: 7, reportData }, tempMsgSec)
        wx.$.r.reLaunch({
          path: manageRecruit,
          query: { activeTab: 'waiting' },
          success: () => {
            setTimeout(() => {
              const url = encodeURIComponent('/enterprise-verify?tabType=0')
              wx.$.r.push({
                path: `/subpackage/web-view/index?url=${url}`,
              })
            }, 1000)
          },
        })
      }
      /** 自定义弹窗，走到错误逻辑 */
      if (['Guidetofillafterpublishing', 'Guidetofillbeforepublishing'].includes(dialogIdentify)) {
        const e = <any>{ ...response.dialogData }
        e.params = params
        e.reportData = reportData
        e.draftReasonCode = 2
        /** 获取JobId */
        e.jobId = wx.$.u.getObjVal(response, 'data.data.id', '')

        Bury.operation({
          data: JSON.stringify({ dialogIdentify }),
          type: 'special',
          functionName: 'handlePublishResponse',
          name: 'CMS通用弹窗',
        })
        console.error('publish,Job', e)
        reject(e)
        return
      }
      if (dialogIdentify == 'wjzqytc') {
        const tabType = wx.$.u.getObjVal(response.dialogData, 'template.tabType')
        reject('toPurchase')
        const occupationV2Ids = params.occV2.map(i => {
          return i.occIds.join(',')
        }).join(',')
        const areaObj: any = await getAreaById(params.areaId)
        const cities = areaObj.city?.id || areaObj.province?.id
        const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}&occupationV2Ids=${occupationV2Ids}&cities=${cities}`)
        if (useDraft) {
          storage.setItemSync('toPurchaseJobVieData', { ...params, draftReasonCode: 3 })
        }
        Bury.operation({
          data: JSON.stringify({ dialogIdentify }),
          functionName: 'handlePublishResponse',
          type: 'compete',
          name: 'CMS通用弹窗',
        })
        wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })
        return
      }

      //  若用户未完成实名认证，且该工种免实名认证次数用完-6191227990
      if (['RealNameHaveNoTimes', 'RealNameHaveTimes'].includes(dialogIdentify)) {
        dialogIdentify === 'RealNameHaveNoTimes' && await saveDraftPrevCheck(dialogIdentify, { jumpEventType: 3 }, params, reportData).catch(() => false)
        reject('')
        wx.$.r.reLaunch({
          path: manageRecruit,
          success: () => {
            wx.$.r.push({ path: '/subpackage/member/realname/index' })
          },
        })
        return
      }

      const stamp = Date.now()
      wx.$.showModal({
        ...response.popup,
        clickInterceptor: async (userAction) => {
          const { routePath } = userAction
          /** 保存到草稿箱的标识 */
          let savedDraftFlag = false
          /** 待开放逻辑 */
          if (useDraft) {
            savedDraftFlag = await saveDraftPrevCheck(dialogIdentify, userAction, params, reportData).catch(() => false)
          }
          Bury.operation({
            data: JSON.stringify({ dialogIdentify, action: `${userAction.buttonName}` }),
            functionName: 'handlePublishResponse',
            name: 'CMS通用弹窗',
            /** 从触发弹窗到用户点击耗时 */
            duration: stamp - Date.now(),
            /** 触发弹窗时间戳 */
            dialogInvokeTime: stamp,
          })

          switch (routePath) {
            case 'toResume': // 跳转到牛人列表
              wx.$.r.push({ path: '/pages/resume/index' })
              break
            case 'confirm':/** 继续发布 */
            case 'continueReleaseInfo':/** 付费发布 */
              resolve(routePath)
              break
            // case 'toapply': {
            //   wx.$.r.reLaunch({
            //     path: manageRecruit,
            //     query: { activeTab: 'waiting' },
            //     success: () => {
            //       setTimeout(() => {
            //         const url = encodeURIComponent('/enterprise-verify?tabType=1')
            //         wx.$.r.push({
            //           path: `/subpackage/web-view/index?url=${url}`,
            //         })
            //       }, 1000)
            //     },
            //   })
            //   break
            // }
            case 'cancel': { /** publishEnterpriseAuthOnlinePromp 企业认证弹窗 取消按钮 */
              /** 临时发布 */
              if (['publishEnterpriseAuthOnlinePromp', 'enterpriseAuthOnlinePromptJump'].includes(dialogIdentify)) {
                resolve(routePath)
                return Promise.resolve()
              }
              const classify_id = await wx.$.l.transformOccV2ToHidClsId(params.occV2)
              const area_id = params.areaId || ''
              reject(routePath)
              wx.$.r.reLaunch({ path: '/pages/resume/index', params: { classify_id, area_id } })
              break
            }
            case 'toCompanyAuth': { /** publishEnterpriseAuthOnlinePromp 企业认证弹窗 立即认证按钮 */
              resolve(routePath)
              if (savedDraftFlag) {
                wx.$.r.reLaunch({
                  path: manageRecruit,
                  query: { activeTab: 'waiting' },
                  success: () => {
                    setTimeout(() => {
                      wx.$.r.push({
                        path: '/subpackage/member/firmAuth/index',
                        query: { origin: 'publishRecruitPop' },
                      })
                    }, 1000)
                  },
                })
                return Promise.resolve()
              }

              wx.$.r.reLaunch({ path: '/subpackage/member/firmAuth/index', query: { origin: 'publishRecruitPop' } })
              break
            }
            case 'toEarnPoints': { /** publishIntegralLack 积分不足弹窗 获取积分按钮 */
              reject('toEarnPoints')
              if (savedDraftFlag) {
                wx.$.r.reLaunch({
                  path: manageRecruit,
                  query: { activeTab: 'waiting' },
                  success: () => {
                    setTimeout(() => {
                      wx.$.toGetIntegral({
                        isFromPage: 'CLPublishRecruit',
                      })
                    }, 1000)
                  },
                })
                return Promise.resolve()
              }
              wx.$.toGetIntegral({
                isFromPage: 'CLPublishRecruit',
              })

              // this.triggerEvent('close', { action: 2, text: '' })
              break
            }
            // case 'toPurchase': {
            //   const tabType = wx.$.u.getObjVal(response.dialogData, 'template.tabType')
            //   reject('toPurchase')
            //   const url = encodeURIComponent(`/campaign-recruitment?tabTypes=${tabType}`)
            //   if (useDraft) {
            //     storage.setItemSync('toPurchaseJobVieData', { ...params, draftReasonCode: 3 })
            //   }
            //   wx.$.r.push({ path: `/subpackage/web-view/index?url=${url}&isLogin=true` })

            //   break
            // }
            default:
              if (savedDraftFlag) {
                await new Promise((resolve) => {
                  wx.$.r.reLaunch({
                    path: manageRecruit,
                    query: { activeTab: 'waiting' },
                    success: () => {
                      setTimeout(() => {
                        resolve(undefined)
                      }, 1000)
                    },
                  })
                })
              }
              reject()
              break
                /** noop */
          }

          return Promise.resolve()
        },
      })
    })
  }
  return Promise.resolve(undefined)
}

export function jumpToCompleteWin<T extends IJumpParams, P>(pageIns, params: T, reportData: P, method: 'winPush'|'winReplace'|'winRelaunch' = 'winPush') {
  const { occIds, publishBtnStatus, isPublish, type } = params
  if (method == 'winRelaunch') {
    jumpToComplete(params, reportData, 'relaunch')
  } else if (occIds.length > 1) {
    wx.$.nav[method](
      pageIns,
      '/subpackage/recruit/improve-recruitment/index',
      {
        occIds,
        jobId: params.jobId,
        publishBtnStatus,
        isPublish,
        origin: params.origin,
        type,
      },
      () => {},
      {
        routeParams: { ...params, reportData },
      },
    )
  } else {
    wx.$.nav[method](
      pageIns,
      '/subpackage/recruit/improve-recruitment-info/index',
      {
        occId: occIds[0],
        jobId: params.jobId,
        publishBtnStatus,
        isPublish,
        origin: params.origin,
        type,
      },
      () => {},
      {
        values: params.completeSetting,
        routeParams: { ...params, reportData },
      },
    )
  }
}

interface IJumpParams extends Record<string, any> { occIds: LiteralText[], publishBtnStatus?: number, isPublish: boolean }

/**
 * 根据用户选择，存储草稿箱
 */
export const saveDraftPrevCheck = async (dialogIdentify: string, userAction: IUserAction, params: Record<string, any> = {}, reportData = {}) => {
  /** 自招招聘范围限制 */
  if (selfSurpassOverLimit.includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 4, reportData }, tempMsgSec)
  }
  /** 代招招聘范围限制 */
  if (hiringClintOverLimit.includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 5, reportData }, tempMsgSec)
  }
  /** 客户关系异常 */
  if (hiringRelationLimit.includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 6, reportData }, tempMsgSec)
  }
  /** 招聘资质异常 */
  if (hiringClientLimit.includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 7, reportData }, tempMsgSec)
  }
  if (realNameDraftKeys.includes(dialogIdentify)) {
    /** 实名认证审核中 */
    if (dialogIdentify === 'ComplianceAuthCheck') {
      return saveDraft({ ...params, draftReasonCode: 2, reportData }, tempMsgSec)
      /** 取消 */
    }
    return saveDraft({ ...params, draftReasonCode: 1, reportData }, tempMsgSec)
  }
  /** 企业实名 */
  if (enterpriseDraftKeys.includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 2, reportData }, tempMsgSec)
  }
  /** 积分不足 */
  if (['modifyIntegralLackV1', 'publishIntegralLack'].includes(dialogIdentify)) {
    /** 取消 */
    return saveDraft({ ...params, draftReasonCode: 3, reportData }, tempMsgSec)
  }
  /** 点击的右上角关闭按钮 */
  if (['jfczydgmbdhy', 'jfbuydgmbdhy', 'fbzgjfczydgmck', 'fbzgjfbzydgmck'].includes(dialogIdentify) && userAction.btnIndex === -1) {
    return saveDraft({ ...params, draftReasonCode: 3, reportData }, tempMsgSec)
  }
  /** 积分充足购买会员，保存到待开放  */
  if (['jfczydgmbdhy', 'fbzgjfczydgmck'].includes(dialogIdentify) && userAction.jumpEventType === 2) {
    return saveDraft({ ...params, draftReasonCode: 3, reportData }, tempMsgSec)
  }
  /** 积分不足引导购买会员弹窗 */
  if (['fbzgjfbzydgmck', 'jfbuydgmbdhy'].includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 3, reportData }, tempMsgSec)
  }
  /** 付费发布，会员次卡消费确认弹窗，点击取消 */
  if (['jobPaidPublishingPopJava', 'fbzgyfbqy', 'wjzqytc'].includes(dialogIdentify) && userAction.jumpEventType === 3) {
    return saveDraft({ ...params, draftReasonCode: 3, reportData }, tempMsgSec)
  }
  /** 触发风控引导去完善企业认证补充资料 - 后，需要保存草稿变成待开放 */
  if (['guide_enterprise_certification'].includes(dialogIdentify)) {
    return saveDraft({ ...params, draftReasonCode: 2, reportData }, tempMsgSec)
  }
}

export const saveDraft = async (params: unknown, msg: string = tempMsgSec) => {
  let draftSavedFlag = false
  const { reportData = {}, ...newParams } = <any>params || {}
  /** 发布职位根据职位获取商圈，默认选中第一个 */
  if (newParams.location) {
    const { longitude, latitude } = newParams.location
    const businessCircles = await tryPromise(wx.$.javafetch['POST/lbs/v1/location/businessArea']({ longitude, latitude }).then(response => response.data.data.map(({ name }, index) => ({ name, selected: index == 0 ? 1 : 0 }))), [])
    newParams.businessCircles = businessCircles
  }

  const result = await wx.$.javafetch['POST/job/v3/manage/draft/save'](newParams, { hideMsg: true })
  if (result && !result.error) {
    wx.$.collectEvent.event('releaseRecruitment', { ...reportData, published_results: '待开放' })
    setTimeout(() => {
      wx.$.msg(msg)
    }, 1000)

    /** TODO 跳转到待开放列表 */
    draftSavedFlag = true
  }
  /** 已保存到待开放信息列表 */
  return draftSavedFlag
}

/**
 * 跳转到完善页
 * @param params 携带参数
 */
export const jumpToComplete = <T extends IJumpParams, P>(params: T, reportData: P, method: 'push'|'replace'|'relaunch' = 'push') => {
  const { occIds, publishBtnStatus, isPublish, type } = params
  if (occIds.length > 1) {
    wx.$.nav[method](
      '/subpackage/recruit/improve-recruitment/index',
      {
        occIds,
        jobId: params.jobId,
        publishBtnStatus,
        isPublish,
        origin: params.origin,
        type,
      },
      () => {},
      {
        routeParams: { ...params, reportData },
      },
    )
  } else {
    wx.$.nav[method](
      '/subpackage/recruit/improve-recruitment-info/index',
      {
        occId: occIds[0],
        jobId: params.jobId,
        publishBtnStatus,
        isPublish,
        origin: params.origin,
        type,
      },
      () => {},
      {
        values: params.completeSetting,
        routeParams: { ...params, reportData },
      },
    )
  }
}

export function jumpToRecruitTopSet(id: string, pageFrom = 'publish') {
  let p = `?jobId=${id}&pageFrom=${pageFrom}&showTab=${1}&activeTab=${0}`
  p = encodeURIComponent(p)
  wx.$.r.reLaunch({
    path: `/subpackage/web-view/index?url=/top-and-urgent${p}&isLogin=true`,
  })
}

export function existJobTopSet(id: string|number, { pageFrom = 'jobManage',
  showTab = 1,
  activeTab = 0,
  jumpMethod = 'push' }:IJumpExistTopSet = {}) {
  let p = `?jobId=${id}&pageFrom=${pageFrom}&showTab=${showTab}&activeTab=${activeTab}`
  p = encodeURIComponent(p)
  wx.$.r[jumpMethod]({
    path: `/subpackage/web-view/index?url=/top-and-urgent${p}&isLogin=true`,
  })
}

/** 删除空字段 */
export const deleteNullProp = <T>(obj: T) => {
  const newObj = { ...obj }
  const keys = Object.keys(newObj)
  keys.forEach((key) => {
    if (!Object.prototype.hasOwnProperty.call(newObj, key)) return
    if (newObj[key] === null || newObj[key] === undefined || newObj[key] === '') {
      delete newObj[key]
    }
  })

  return newObj
}

/** 个人实名弹窗 */
const realNameDraftKeys = ['to_auth_v2', 'ComplianceToAuth', 'ComplianceAuthCheck', 'RealNameHaveNoTimes']
/** 企业实名弹窗 */
const enterpriseDraftKeys = ['ComplianceEntToAuth', 'to_company_auth_v2']
/** 自招超限 */
const selfSurpassOverLimit = ['HrSelfSurpass-publish', 'CompanyOverCity', 'CompanyOverPostType', 'CompanyOverPostTypeAndCity']
/** 代招超限 */
const hiringClintOverLimit = ['HrProxyOverCity', 'HrProxyOverPostType', 'HrProxyOverPostTypeAndCity']
/** 客户关系受限 */
const hiringRelationLimit = ['customer_due_popup']
/** 招聘资质受限 */
const hiringClientLimit = ['permit_due_popup']

const tempMsgSec = '信息已存入草稿箱，可在管理职位-待开放中继续发布'

type IUserAction = {
  routePath?: string,
  jumpEventType: number,
  btnIndex?: number
} & Record<string, LiteralText>

export type IPublishParams = SingleParamOf<JavaFetch['POST/job/v2/manage/job/publish/preCheck']>

export type IPublishResponse = Awaited<ReturnType<JavaFetch['POST/job/v2/manage/job/publish/preCheck']>>

interface IPrevCheckExtern {
  serial_number: string,
  reportData: Record<string, any>,
  ignoreDialog?: string[]
}

/**
 * 跳转到置顶页参数
 * @property { "publish"| "jobManage" } pageFrom 来源页面
 * @property { 0|1 } showTab 是否展示置顶加急招切换选项卡 0 不展示 1 展示
 * @property { 0|1 } activeTab 置顶加急招切换选项卡选中项 0 置顶 1 加急
 * @property { "push"|"reLaunch"|"replace" } jumpMethod 跳转方式
 */
interface IJumpExistTopSet {
  /** 跳转的页面 */
  pageFrom?: 'publish'|'jobManage',
  /** 是否展示置顶加急招切换选项卡 */
  showTab?: 0|1,
  /** 置顶加急招切换选项卡选中项 */
  activeTab?: 0|1,
  /** 跳转方式 */
  jumpMethod?:'push'|'reLaunch'|'replace'
}

type LiteralText = string | number

type SingleParamOf<P> = P extends (argv: infer R) => any ? R : never

// interface IJumpParams extends Record<string, any> { occIds: LiteralText[], publishBtnStatus?: number, isPublish: boolean }

type IPublishSuccessCB = (temporary: boolean, response?: any) => void
