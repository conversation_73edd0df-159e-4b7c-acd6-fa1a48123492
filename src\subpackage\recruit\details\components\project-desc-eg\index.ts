/*
 * @Date: 2023-09-19 15:56:13
 * @Description: 项目简介
 */

import { insertHelper } from '@/pages/utils'
import { getComponentDom } from '../../utils'

Component({
  properties: {
    info: {
      type: Object,
      value: {},
    },
    /** 当前页面是否是百度seo分享页面 */
    isSeoPage: {
      type: Boolean,
      value: false,
    },
    operatorVisible: {
      type: Boolean,
      value: true,
    },
    isNoLookComplaint: {
      type: Boolean,
      value: false,
    },
    companyCardInfo: {
      type: Object,
      value: {},
    },
  },
  externalClasses: ['p-class'],
  data: {
    /** 展示全部按钮 */
    showAllBtn: false,
    showAllContent: false,
    occs: [],
    isMul: false,
    occsObj: {},
    qualificationInfoList: [], // 资质信息
  },
  observers: {
    info() {
      const { isSeoPage } = this.data
      /** seo页面，直接展示全部 */
      if (isSeoPage) {
        this.onLookAll()
        return
      }
      this.getClientRect()
    },
    'info.occShowTags': function () {
      this.getShowTags(this.data.info)
    },
  },
  methods: {
    getShowTags(info) {
      const { occShowTags, isWhiteCollar } = info || {}
      if (wx.$.u.isArrayVal(occShowTags)) {
        const isMul = occShowTags.length > 1
        const nOccsObj = {}
        const occs = []

        const getNoLabelTypes = (isMul, isWhiteCollar) => {
          if (!isMul) {
            return isWhiteCollar ? ['1', '15', '17'] : ['1', '6', '16']
          }
          return ['1']
        }

        occShowTags.forEach((item, idx) => {
          const { showTags = [] } = item
          const salaryObj = showTags.find(t => t.type == 1)
          const noLabelTypes = getNoLabelTypes(isMul, isWhiteCollar)
          const nShowTags = showTags.filter(t => !noLabelTypes.includes(`${t.type}`))

          nOccsObj[idx] = {
            showNum: nShowTags.length - 1,
            salary: salaryObj ? salaryObj.name : '',
          }

          occs.push({ ...item, showTags: nShowTags })
        })
        this.setData({ occsObj: nOccsObj, occs, isMul })
      } else {
        this.setData({ occs: [], occsObj: {}, isMul: false })
      }
    },
    /** 查看全部内容 */
    onLookAll() {
      insertHelper.trigger(2)
      this.setData({
        showAllBtn: false,
        showAllContent: true,
      })
    },
    onComplain() {
      this.triggerEvent('complain', {})
    },
    onYjComplain() {
      wx.$.msg('请勿重复投诉')
    },
    /** 获取招工详情dom结点 */
    async getClientRect() {
      const timer = setTimeout(async () => {
        if (!this.data.info.detail) {
          return
        }
        const outer = (await getComponentDom.call(this, '#contentOuter')) || {} as any
        const inner = (await getComponentDom.call(this, '#contentInner')) || {} as any
        if (outer.height < inner.height) {
          this.setData({
            showAllBtn: true,
          })
          clearTimeout(timer)
        }
      }, 50)
    },

    // 对象深度比较
    deepEqual(a, b) {
      // 基本类型直接比较
      if (a === b) return true

      // 检查类型是否一致
      if (typeof a !== typeof b) return false

      // 处理数组比较
      if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false
        for (let i = 0; i < a.length; i++) {
          if (!this.deepEqual(a[i], b[i])) return false
        }
        return true
      }

      // 处理对象比较
      if (typeof a === 'object' && a !== null && b !== null) {
        const keysA = Object.keys(a)
        const keysB = Object.keys(b)

        if (keysA.length !== keysB.length) return false

        return keysA.every((key) => {
          return keysB.includes(key) && this.deepEqual(a[key], b[key])
        })
      }

      // 其他类型不相等
      return false
    },
  },
})
