Component({
  properties: {
    info: {
      type: Object,
      value: {},
    },
    jobId: {
      type: Number,
      value: 0,
    },
    source_page: {
      type: String,
      value: '职位详情页',
    },
  },
  methods: {
    onCardClick() {
      this.reportsData('enterpriseEntranceClick')
      wx.$.r.push({
        path: '/subpackage/company/home/<USER>',
        query: {
          enterpriseBaseInfoId: this.data.info.enterpriseBaseInfoId,
          pageType: 'home',
          jobId: this.data.jobId,
          pageSource: this.data.source_page,
        },
      })
    },
    /** 上报埋点 */
    reportsData(name?: string) {
      const reportParams = {
        enterprise_name: this.data.info.name || '',
        source_page: this.data.source_page,
      }
      const exportName = name || 'enterpriseEntranceExposure'
      wx.$.collectEvent.event(exportName, reportParams)
    },
  },
  pageLifetimes: {
    show() {
      this.reportsData()
    },
  },
  lifetimes: {
    attached() {
      this.reportsData()
    },
  },
})
