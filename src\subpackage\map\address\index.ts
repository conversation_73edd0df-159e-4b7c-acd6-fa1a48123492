import type { TTreeFullVal } from './utils/index.d'

import {
  getAddrConf,
  initAddress,
  pagePoint,
  selectAddress,
  selectArrHandler,
  selectClear,
  selectHandler,
} from './utils/index'
import { addrConfDef, hotID } from './utils/tools'
import { isByAdcodeRegion } from '@/utils/helper/location/index'
import { dealDialogRepByApi, dealDialogShow } from '@/utils/helper/dialog/index'

Page(class extends wx.$.Page {
  data = {
    title: '选择城市',
    oneArea: [],
    twoArea: [],
    threeArea: [],
    selectAddr: [],
    deleteAddr: [],
    addrConf: { ...addrConfDef },
    /** 是否打开了搜索 */
    isSearch: false,
    clickLevel: 0,
  }

  onLoad() {
    const { title } = this.data
    const addrConf = getAddrConf() // 这个一定先先执行一次
    /** 埋点 */
    pagePoint('city_filter_click')

    wx.$.loading()
    this.setData({
      addrConf,
      title: addrConf.title || title,
    })

    initAddress(addrConf.areas).then(res => {
      wx.hideLoading()
      this.setData({
        oneArea: res.oneArea,
        twoArea: res.twoArea,
        deleteAddr: res.deleteAddr || [],
        threeArea: res.threeArea,
        selectAddr: res.selectAddr,
      })

      if (wx.$.u.isArrayVal(res.deleteAddr)) {
        this.onReselectCity()
      }
    }).catch(() => {
      wx.hideLoading()
      wx.$.msg('加载失败请退出页面重新进入')
    })
  }

  onReselectCity() {
    dealDialogShow({
      dialogIdentify: 'reselectCity',
    }).then(() => {
      this.setData({ deleteAddr: [] })
    }).catch(() => {
      this.setData({ deleteAddr: [] })
    })
  }

  /** 定位地址 */
  onLocChange({ detail }) {
    const { value, isReposition } = detail
    const { addrConf } = this.data
    if (wx.$.u.isArrayVal(addrConf.disabledIds) && addrConf.disabledIds.some(id => id == value.id)) {
      wx.$.msg('此地区暂未提供服务')
      return
    }
    this.confirmHandler([value], { isLocation: true, isReposition })
    if (isReposition) {
      this.onSelect({ detail })
    }
  }

  /** 检查推广城市与发布城市是否不一致 */
  async checkCityDifference(
    promoteCities: CityData[],
    publishCity: CityData,
  ): Promise<boolean> {
    if (!publishCity || !publishCity.id) return false
    const pubCityId = wx.$.u.getObjVal(publishCity, 'selectCity.id')
    const areas = await wx.$.l.getAreaById(publishCity.cityId || pubCityId || publishCity.id)

    const provinceId = wx.$.u.getObjVal(areas, 'province.id')
    const cityId = wx.$.u.getObjVal(areas, 'city.id')
    if (!provinceId && !cityId) return false

    // 如果推广城市中包含发布城市，则认为一致
    return !promoteCities.some(
      city => (String(city.id) === String(provinceId) || String(city.id) === String(cityId)),
    )
  }

  /** 选择地址 */
  async onSelect({ detail }) {
    this.setData({ clickLevel: detail.level })
    const { value } = detail
    const { addrConf, oneArea, twoArea, threeArea } = this.data

    let selectAddr = wx.$.u.deepClone(this.data.selectAddr)
    const isHot = (oneArea[0].checked && oneArea[0].id === hotID) && detail.level != 1
    const { isEnd, isConfirm, ...setArea } = await selectAddress(value, selectAddr, isHot)

    if (isConfirm) {
      this.confirmHandler([value])
      return
    }
    if (addrConf.isMultiple && isEnd) {
      // 多选的情况
      selectAddr = selectArrHandler(value, selectAddr)
      if (selectAddr.length > addrConf.maxNum) {
        if (addrConf.selectType === 'district') {
          wx.$.msg(`最多选择${addrConf.maxNum}个地区`)
        } else {
          wx.$.msg(`最多只能选择${addrConf.maxNum}个城市`)
        }
        return
      }

      let areas = isByAdcodeRegion(value.ad_code) ? twoArea : threeArea

      if (addrConf.selectType === 'resumePositionTab') {
        areas = twoArea
      } else if (oneArea[0].checked) {
        // 如果打开的是热门
        areas = threeArea
      }
      const areasCount = areas.length - 1
      const isHeaderSel = ['district', 'resumePositionTab'].includes(addrConf.selectType)

      if (!value.isFull
        && areasCount > 0
        && addrConf.level == 3
        && isHeaderSel
        && selectAddr.length === areasCount) {
        // 当用户所选地级市中县级行政单位数最大为选中的值时，则自动勾选【全市】
        selectAddr = [areas[0]]
      }
      this.setData({
        selectAddr,
      })
    }

    const data = selectHandler({ ...setArea, selectAddr })

    this.setData({
      ...data,
    })
  }

  /** 搜索栏选中的地址 */
  onSearchSelect({ detail }) {
    const { addrConf } = this.data
    if (wx.$.u.isArrayVal(addrConf.disabledIds) && addrConf.disabledIds.some(id => id == detail.id)) {
      wx.$.msg('此地区暂未提供服务')
      return
    }
    this.confirmHandler([detail])
  }

  /** 确定按钮 */
  onConfirm() {
    const { selectAddr } = this.data
    if (!wx.$.u.isArrayVal(selectAddr)) {
      wx.$.msg('请选择地址')
      return
    }
    this.confirmHandler(selectAddr)
  }

  /** 确定按钮 */
  onScrollEnd() {
    this.setData({
      clickLevel: 0,
    })
  }

  /** 选中返回上一页 */
  async confirmHandler(areas: TTreeFullVal, options: { isReposition?: boolean } & Record<string, any> = {}) {
    const value = areas.map(({ children, isFull, checked, ...item }) => {
      return {
        ...item,
        /** 这里处理地址pid不是数字的情况 */
        pid: Number.isNaN(Number(item.pid)) ? 1 : item.pid,
      }
    })
    const { addrConf } = this.data

    if (addrConf.forceCityOnly && addrConf.publishCity) {
      const diff = await this.checkCityDifference([...value], addrConf.publishCity)
      if (diff) {
        // 使用CMS弹窗系统
        const popup = await dealDialogRepByApi('PromotionConfirm')

        if (popup) {
          await new Promise((resolve, reject) => {
            wx.$.showModal({
              ...popup,
              success: ({ routePath }) => {
                if (routePath === 'confirm') {
                  resolve('确定推广城市选中')
                } else {
                  reject('取消推广城市选中')
                }
              },
            })
          })
        }
      }
    }
    const areaIds = areas.map(item => {
      return `${item.id}`
    })

    pagePoint('addressSelection', {}, areaIds)

    const { isReposition, ...restOptions } = options

    wx.$.nav.event({
      ...restOptions,
      value,
    })

    // 无权限时重新定位不返回上一页
    if (!isReposition) {
      wx.$.nav.back()
    }
  }

  /** 清除逻辑 */
  onClear() {
    const { oneArea, twoArea, threeArea } = this.data
    const data = selectClear({
      oneArea,
      twoArea,
      threeArea,
    })
    this.setData({
      ...data,
      selectAddr: [],
    })
  }

  /** 底部按钮的删除地址逻辑 */
  onDelete({ detail }) {
    this.handlerDelete(detail.index)
  }

  /** 底部按钮的删除地址逻辑 */
  handlerDelete(index: number) {
    const selectAddr = wx.$.u.deepClone(this.data.selectAddr)
    const { oneArea, twoArea, threeArea } = this.data
    selectAddr.splice(index, 1)
    this.setData({ selectAddr })

    if (selectAddr.length < 1) {
      this.onClear()
      return
    }

    const data = selectHandler({
      oneArea,
      twoArea,
      threeArea,
      selectAddr,
    })

    this.setData({ ...data })
  }

  /** 显示搜索栏 */
  onShowSearch() {
    if (!this.data.isSearch) {
      this.setData({
        isSearch: true,
      })
    }
  }

  /** 返回上一页 */
  onNavBack() {
    if (this.data.isSearch) {
      this.setData({
        isSearch: false,
      })
      return
    }
    wx.$.nav.back()
  }
})

interface CityData {
  id: number | string;
  name?: string;
  pid?: number | string;
  [key: string]: any;
}
