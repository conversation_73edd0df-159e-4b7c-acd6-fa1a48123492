/*
 * @Date: 2022-07-18 17:08:15
 * @Description: 我的关注通用方法
 */
import { getState, dispatch, actions } from '@/store/index'

type IParamsAttention = YModels['POST/clues/v1/collect/add']['Req']
type IParamsCancelAttention = YModels['POST/clues/v1/collect/cancel']['Req']

type AttentionOptions = {
  /** 关注上限之后的跳转方式 默认：push */
  routeType?: 'push' | 'replace'
  /** loading提示 */
  loadingText?: string
  /** 关注成功的toast提示 如果为false代表不弹出成功的toast提示 */
  successMsg?: string | false
  /** 新接口 */
  type?: 'new' | ''
}
type ICancelAttentionOptions = {
  /** loading提示 */
  loadingText?: string
  /** 关注成功的toast提示 如果为false代表不弹出成功的toast提示 */
  successMsg?: string | false
  /** 新接口 */
  type?: 'new' | 'cancelid' | ''
}

/**
 * 牛人收藏接口
 * @param {Object} params-请求参数
 * @param {Object} options-[可选]额外的配置参数
 * @param {String} options.routeType-关注上限之后的跳转方式 默认：push
 * @param {String} options.loadingText-loading文案
 * @param {String|false} options.successMsg-关注成功的toast提示 如果为false代表不弹出成功的toast提示
 * @returns
 */
export const asyncAttention = async (params, options?: AttentionOptions) => {
  const newOptions = options || { routeType: 'push', loadingText: '', successMsg: '', type: '' }
  if (newOptions.loadingText) {
    wx.showLoading({ title: newOptions.loadingText, mask: true })
  }
  let path = 'POST/clues/v1/collect/add'
  if (newOptions.type == 'new') {
    path = 'POST/clues/v1/collect/addBossByJobId'
  }
  return new Promise((resolve, reject) => {
    wx.$.javafetch[path](params).then(async (result) => {
      if (newOptions.loadingText) {
        wx.hideLoading()
      }
      if (result.error) {
        if (String(result.message).trim()) {
          wx.$.msg(result.message)
        }
        resolve(false)
        return
      }
      wx.$.msg(newOptions.successMsg || result.message)
      resolve(result)
    }).catch((err) => {
      if (newOptions.loadingText) {
        wx.hideLoading()
      }
      reject(err)
    })
  })
}

/**
 * 取消收藏接口
 * @param {Object} params-请求参数
 * @param {Object} options-[可选]额外的配置参数
 * @param {String} options.loadingText-loading文案
 * @param {String|false} options.successMsg-关注成功的toast提示 如果为false代表不弹出成功的toast提示
 * @returns
 */
export const asyncCancelAttention = (params: any, options?: ICancelAttentionOptions) => {
  const newOptions = options || { loadingText: '', successMsg: '', type: '' }

  if (newOptions.loadingText) {
    wx.showLoading({ title: newOptions.loadingText, mask: true })
  }
  let path = 'POST/clues/v1/collect/cancel'
  if (newOptions.type == 'new') {
    // 通过职位id取消收藏
    path = 'POST/clues/v1/collect/cancelBossByJobId'
  } else if (newOptions.type == 'cancelid') {
    // 通过收藏id取消收藏
    path = 'POST/clues/v1/collect/cancelBossByCollectId'
  }

  return new Promise((resolve, reject) => {
    wx.$.javafetch[path](params)
      .then((result) => {
        if (newOptions.loadingText) {
          wx.hideLoading()
        }
        if (newOptions.successMsg !== false) {
          wx.$.msg(newOptions.successMsg || result.message, 600)
        }
        if (result.error) {
          reject(result)
        } else {
          resolve(result)
        }
      })
      .catch(err => {
        if (newOptions.loadingText) {
          wx.hideLoading()
        }
        reject(err)
      })
  })
}

/**
 * 计算优惠金额
*/
export const getDiscountAmt = (amt, discountItem) => {
  const cardDisAmt = Number(discountItem.priceInfo.actualAmount || 0) // 卡卷优惠金额
  let discountAmt = cardDisAmt // 实际优惠金额
  let paidIn = Number(amt) // 实付金额
  if (amt - cardDisAmt > 0) {
    paidIn -= discountAmt
  } else {
    discountAmt = amt
    paidIn = 0
  }
  return { discountAmt, paidIn, cardDisAmt }
}

// 判断时间是否已过期
export const jugeTime = (item) => {
  let start = 0
  let end = 0

  if (item.canUseStartTime) {
    start = new Date(item.canUseStartTime).getTime()
  }
  if (item.canUseEndTime) {
    end = new Date(item.canUseEndTime).getTime()
  }
  const nT = new Date().getTime()
  if (start && end) {
    if (nT >= start && nT <= end) {
      return true
    }
  } else if (!start && end && nT <= end) {
    return true
  }
  return false
}

/** 评价系统的通用埋点
 * @params extData: 自定义的参数
*/
export const uploadEvalteReportData = (reportData, extData = {}) => {
  const reportParms = {
    // 页面名-哪个页面触发的评价
    page_name: reportData.page_name,
    // 触发条件
    trigger_condition: reportData.trigger_condition,
    // 触发来源
    source: reportData.source,
    // 评价类型
    evaluation_type: reportData.evaluation_type,
    // 评价结果
    evaluation_result: reportData.evaluation_result,
    // 原因
    reason_reached: reportData.reason_reached,
  }
  wx.$.collectEvent.event('userEvaluation', { ...reportParms, ...extData })
}

/** 切换角色 role 1 是老板，2 是工人，launch 为重定向的地址 */
export const switchRoles = async (role: 1 | 2, launch?: string) => {
  await changeUserRole(role, getState().storage.userState.login)
  if (launch) {
    wx.$.r.reLaunch({ path: launch })
  } else {
    wx.$.r.reLaunch({ path: role === 1 ? '/subpackage/recruit/published/index' : '/pages/index/index' })
  }
}

/** 保存和变更用户角色 */
export const changeUserRole = async (role: 1 | 2, isLogin = true) => {
  await dispatch(actions.storageActions.setItem({ key: 'userChooseRole', value: role }))
  isLogin && await wx.$.l.timLogoutAndReLogin()
  isLogin && await wx.$.javafetch['POST/account/v1/role/changeRole']({ role })
  dispatch(actions.messageActions.getGlobalSwitch())
}
